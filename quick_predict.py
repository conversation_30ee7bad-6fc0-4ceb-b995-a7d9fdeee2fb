#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速预测脚本
使用已训练的模型进行成功选股预测
"""

import pandas as pd
import numpy as np
import joblib
import os
import argparse
from datetime import datetime

class QuickPredictor:
    def __init__(self, model_timestamp):
        """初始化快速预测器"""
        self.model_timestamp = model_timestamp
        self.models = {}
        self.scaler = None
        self.feature_selector = None
        self.label_encoder = None
        
        # 输入特征定义
        self.input_features = [
            "A点开盘", "A点收盘", "A点最高", "A点最低", "A点成交量", "A点实体涨跌幅", "A点价格振幅",
            "B点开盘", "B点收盘", "B点最高", "B点最低", "B点成交量", "B点实体涨跌幅", "B点价格振幅", 
            "C点开盘", "C点收盘", "C点最高", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点开盘", "D点收盘", "D点最高", "D点最低", "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点开盘", "E点收盘", "E点最高", "E点最低", "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量"
        ]
        
        self._load_models()
    
    def _load_models(self):
        """加载训练好的模型"""
        print(f"🔄 加载模型 (时间戳: {self.model_timestamp})")
        
        model_dir = "success_prediction_models"
        
        # 加载模型
        model_names = ['RandomForest', 'GradientBoosting', 'LogisticRegression', 'SVM']
        for model_name in model_names:
            model_path = f"{model_dir}/{model_name}_{self.model_timestamp}.pkl"
            if os.path.exists(model_path):
                self.models[model_name] = joblib.load(model_path)
                print(f"   ✅ {model_name} 加载成功")
            else:
                print(f"   ❌ {model_name} 文件不存在: {model_path}")
        
        # 加载预处理器
        self.scaler = joblib.load(f"{model_dir}/scaler_{self.model_timestamp}.pkl")
        self.feature_selector = joblib.load(f"{model_dir}/feature_selector_{self.model_timestamp}.pkl")
        self.label_encoder = joblib.load(f"{model_dir}/label_encoder_{self.model_timestamp}.pkl")
        
        print(f"   ✅ 预处理器加载成功")
        print(f"   📊 可用模型: {len(self.models)} 个")
    
    def preprocess_data(self, file_path):
        """预处理数据"""
        print(f"📁 加载数据: {os.path.basename(file_path)}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        print(f"   📊 原始数据形状: {df.shape}")
        
        # 提取特征
        missing_features = [f for f in self.input_features if f not in df.columns]
        if missing_features:
            print(f"   ⚠️  缺失特征: {missing_features}")
            for feature in missing_features:
                df[feature] = 0
        
        X = df[self.input_features].copy()
        
        # 数据清洗
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # 数值类型转换
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    if X[col].astype(str).str.contains('%').any():
                        X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                    else:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    X[col] = 0
        
        # 填充缺失值
        X = X.fillna(X.median())
        
        # 移除包含缺失值的行
        mask = ~X.isnull().any(axis=1)
        X = X[mask]
        df_clean = df[mask]
        
        print(f"   ✅ 清洗后数据形状: X={X.shape}")
        return X, df_clean
    
    def predict(self, X, original_df):
        """进行预测"""
        print("🔮 进行预测...")
        
        # 预处理
        X_scaled = self.scaler.transform(X)
        X_selected = self.feature_selector.transform(X_scaled)
        
        print(f"   📊 预测特征矩阵: {X_selected.shape}")
        
        # 使用所有模型进行预测
        predictions = {}
        probabilities = {}
        
        for model_name, model in self.models.items():
            pred = model.predict(X_selected)
            pred_proba = model.predict_proba(X_selected)
            
            # 解码预测结果
            pred_labels = self.label_encoder.inverse_transform(pred)
            
            predictions[model_name] = pred_labels
            probabilities[model_name] = pred_proba[:, 1]  # 成功的概率
            
            print(f"   ✅ {model_name} 预测完成")
        
        # 集成预测（投票）
        ensemble_pred = []
        ensemble_proba = []
        
        for i in range(len(X_selected)):
            # 投票决定最终预测
            votes = [predictions[model][i] for model in self.models.keys()]
            success_votes = votes.count('成功')
            final_pred = '成功' if success_votes > len(votes) / 2 else '失败'
            ensemble_pred.append(final_pred)
            
            # 平均概率
            avg_proba = np.mean([probabilities[model][i] for model in self.models.keys()])
            ensemble_proba.append(avg_proba)
        
        # 格式化结果
        results_df = original_df.copy()
        results_df['集成预测'] = ensemble_pred
        results_df['成功概率'] = ensemble_proba
        
        # 添加各模型的预测结果
        for model_name in self.models.keys():
            results_df[f'{model_name}_预测'] = predictions[model_name]
            results_df[f'{model_name}_概率'] = probabilities[model_name]
        
        # 统计预测结果
        pred_counts = pd.Series(ensemble_pred).value_counts()
        print(f"\n   📊 预测结果统计:")
        for label, count in pred_counts.items():
            percentage = count / len(ensemble_pred) * 100
            print(f"      {label}: {count}个 ({percentage:.1f}%)")
        
        return results_df
    
    def save_results(self, results_df, input_file):
        """保存预测结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存Excel结果
        output_file = f"success_prediction_results/快速预测_{timestamp}.xlsx"
        results_df.to_excel(output_file, index=False)
        
        print(f"\n💾 预测结果已保存: {output_file}")
        
        # 显示重点推荐
        high_prob_stocks = results_df[
            (results_df['集成预测'] == '成功') & 
            (results_df['成功概率'] > 0.7)
        ]
        
        if len(high_prob_stocks) > 0:
            print(f"\n🎯 高概率成功股票推荐:")
            for idx, row in high_prob_stocks.iterrows():
                stock = row.get('股票', 'N/A')
                prob = row['成功概率']
                print(f"   📈 {stock}: 成功概率 {prob:.1%}")
        else:
            print(f"\n⚠️  无高概率成功股票")
        
        return output_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速成功选股预测')
    parser.add_argument('--input', '-i', required=True, help='输入Excel文件路径')
    parser.add_argument('--model', '-m', default='20250727_232058', help='模型时间戳')
    
    args = parser.parse_args()
    
    print("🚀 快速成功选股预测")
    print("=" * 50)
    
    try:
        # 创建预测器
        predictor = QuickPredictor(args.model)
        
        # 预处理数据
        X, df = predictor.preprocess_data(args.input)
        
        # 进行预测
        results_df = predictor.predict(X, df)
        
        # 保存结果
        output_file = predictor.save_results(results_df, args.input)
        
        print("\n🎉 预测完成！")
        
    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
