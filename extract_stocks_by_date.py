#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据按买入日期提取工具
从选股分析结果中提取指定日期范围内的股票数据
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import argparse

def extract_stocks_by_date_range(input_file, start_date, end_date, output_file=None):
    """
    从Excel文件中提取指定买入日期范围内的股票数据
    
    Args:
        input_file (str): 输入Excel文件路径
        start_date (str): 开始日期 (格式: YYYY-MM-DD)
        end_date (str): 结束日期 (格式: YYYY-MM-DD)
        output_file (str): 输出文件路径，如果为None则自动生成
    
    Returns:
        str: 输出文件路径
    """
    
    print(f"\n📊 股票数据提取工具")
    print("=" * 50)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return None
    
    try:
        # 读取Excel文件
        print(f"📁 正在读取文件: {os.path.basename(input_file)}")
        df = pd.read_excel(input_file)
        print(f"   总记录数: {len(df)}")
        print(f"   列名: {list(df.columns)}")
        
        # 检查是否有买入日期列
        date_columns = [col for col in df.columns if '买入日期' in col or '日期' in col]
        if not date_columns:
            print("❌ 未找到买入日期相关列，请检查文件格式")
            print(f"   可用列: {list(df.columns)}")
            return None
        
        # 使用第一个找到的日期列
        date_column = date_columns[0]
        print(f"📅 使用日期列: {date_column}")
        
        # 转换日期格式
        try:
            df[date_column] = pd.to_datetime(df[date_column])
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
        except Exception as e:
            print(f"❌ 日期格式转换失败: {e}")
            return None
        
        # 筛选日期范围内的数据
        mask = (df[date_column] >= start_dt) & (df[date_column] <= end_dt)
        filtered_df = df[mask].copy()
        
        print(f"\n🔍 筛选结果:")
        print(f"   日期范围: {start_date} 到 {end_date}")
        print(f"   筛选后记录数: {len(filtered_df)}")
        
        if len(filtered_df) == 0:
            print("⚠️  指定日期范围内没有找到数据")
            return None
        
        # 显示日期分布
        date_counts = filtered_df[date_column].dt.date.value_counts().sort_index()
        print(f"\n📈 日期分布:")
        for date, count in date_counts.items():
            print(f"   {date}: {count} 条记录")
        
        # 生成输出文件名
        if output_file is None:
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_file = f"{base_name}_筛选_{start_date}_到_{end_date}.xlsx"
            output_file = os.path.join(os.path.dirname(input_file), output_file)
        
        # 保存筛选后的数据
        filtered_df.to_excel(output_file, index=False)
        print(f"\n💾 数据已保存到: {output_file}")
        
        # 显示部分数据预览
        print(f"\n📋 数据预览 (前5条):")
        preview_columns = [date_column]
        if '股票代码' in df.columns:
            preview_columns.append('股票代码')
        if '股票名称' in df.columns:
            preview_columns.append('股票名称')
        if '预测概率' in df.columns:
            preview_columns.append('预测概率')
        
        print(filtered_df[preview_columns].head().to_string(index=False))
        
        return output_file
        
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return None

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='从选股分析结果中提取指定日期范围的股票数据')
    parser.add_argument('--input', '-i', 
                       default=r'C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250803_140012.xlsx',
                       help='输入Excel文件路径')
    parser.add_argument('--start', '-s', required=True, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', '-e', required=True, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--output', '-o', help='输出文件路径 (可选)')
    
    args = parser.parse_args()
    
    # 执行提取
    result = extract_stocks_by_date_range(
        input_file=args.input,
        start_date=args.start,
        end_date=args.end,
        output_file=args.output
    )
    
    if result:
        print(f"\n✅ 提取完成! 输出文件: {result}")
    else:
        print(f"\n❌ 提取失败!")

def interactive_mode():
    """交互模式"""
    print("\n🎯 股票数据提取工具 - 交互模式")
    print("=" * 50)
    
    # 默认输入文件
    default_input = r'C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250803_140012.xlsx'
    
    # 获取输入文件
    input_file = input(f"请输入Excel文件路径 (回车使用默认): ").strip()
    if not input_file:
        input_file = default_input
    
    # 获取日期范围
    print(f"\n请输入日期范围 (格式: YYYY-MM-DD):")
    start_date = input("开始日期: ").strip()
    end_date = input("结束日期: ").strip()
    
    # 获取输出文件 (可选)
    output_file = input("输出文件路径 (回车自动生成): ").strip()
    if not output_file:
        output_file = None
    
    # 执行提取
    result = extract_stocks_by_date_range(
        input_file=input_file,
        start_date=start_date,
        end_date=end_date,
        output_file=output_file
    )
    
    if result:
        print(f"\n✅ 提取完成! 输出文件: {result}")
    else:
        print(f"\n❌ 提取失败!")

if __name__ == "__main__":
    import sys
    
    # 如果没有命令行参数，启动交互模式
    if len(sys.argv) == 1:
        interactive_mode()
    else:
        main()
