#!/usr/bin/env python3
"""
HTML报告渲染器
生成训练和预测结果的HTML报告
"""

import os
import json
import pandas as pd
from datetime import datetime

class HTMLRenderer:
    """HTML报告渲染器"""
    
    def __init__(self):
        """初始化渲染器"""
        pass
    
    def create_training_html(self, training_results_file):
        """创建训练结果HTML报告"""
        try:
            # 读取训练结果
            with open(training_results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习训练报告</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f8f9fa; }}
        .header {{ text-align: center; color: #333; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .metrics {{ display: flex; justify-content: space-around; }}
        .metric {{ text-align: center; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: center; }}
        th {{ background-color: #4CAF50; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        tr:hover {{ background-color: #e8f5e8; }}
        .best-score {{ background-color: #d4edda; font-weight: bold; }}
        .model-name {{ font-weight: bold; color: #2c3e50; }}
        h2 {{ color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 深度学习训练报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>📊 模型性能对比</h2>
        <table>
            <tr>
                <th>模型</th>
                <th>准确率</th>
                <th>精确率</th>
                <th>召回率</th>
                <th>F1分数</th>
                <th>AUC</th>
            </tr>
"""
            
            # 添加模型性能数据
            if 'evaluation_metrics' in results:
                # 找出每个指标的最佳值
                all_metrics = results['evaluation_metrics']
                best_accuracy = max(all_metrics.values(), key=lambda x: x.get('test_accuracy', 0))['test_accuracy']
                best_precision = max(all_metrics.values(), key=lambda x: x.get('test_precision', 0))['test_precision']
                best_recall = max(all_metrics.values(), key=lambda x: x.get('test_recall', 0))['test_recall']
                best_f1 = max(all_metrics.values(), key=lambda x: x.get('f1_score', 0))['f1_score']
                best_auc = max(all_metrics.values(), key=lambda x: x.get('auc_score', 0))['auc_score']

                for model_name, metrics in all_metrics.items():
                    accuracy = metrics.get('test_accuracy', 0)
                    precision = metrics.get('test_precision', 0)
                    recall = metrics.get('test_recall', 0)
                    f1 = metrics.get('f1_score', 0)
                    auc = metrics.get('auc_score', 0)

                    html_content += f"""
            <tr>
                <td class="model-name">{model_name}</td>
                <td{' class="best-score"' if accuracy == best_accuracy else ''}>{accuracy:.3f}</td>
                <td{' class="best-score"' if precision == best_precision else ''}>{precision:.3f}</td>
                <td{' class="best-score"' if recall == best_recall else ''}>{recall:.3f}</td>
                <td{' class="best-score"' if f1 == best_f1 else ''}>{f1:.3f}</td>
                <td{' class="best-score"' if auc == best_auc else ''}>{auc:.3f}</td>
            </tr>
"""
            
            html_content += """
        </table>
    </div>

    <div class="section">
        <h2>🏆 最佳模型推荐</h2>
        <div class="metrics">
"""

            # 推荐最佳模型
            if 'evaluation_metrics' in results:
                all_metrics = results['evaluation_metrics']
                best_auc_model = max(all_metrics.items(), key=lambda x: x[1].get('auc_score', 0))
                best_f1_model = max(all_metrics.items(), key=lambda x: x[1].get('f1_score', 0))

                html_content += f"""
            <div class="metric">
                <h3>🎯 最佳AUC模型</h3>
                <p><strong>{best_auc_model[0]}</strong></p>
                <p>AUC: {best_auc_model[1]['auc_score']:.3f}</p>
            </div>
            <div class="metric">
                <h3>⚖️ 最佳F1模型</h3>
                <p><strong>{best_f1_model[0]}</strong></p>
                <p>F1: {best_f1_model[1]['f1_score']:.3f}</p>
            </div>
"""

            html_content += """
        </div>
    </div>

    <div class="section">
        <h2>📈 训练信息</h2>
        <p><strong>数据集信息:</strong></p>
        <ul>
"""

            # 添加数据信息
            if 'data_info' in results:
                data_info = results['data_info']
                positive_rate = (data_info.get('positive_samples', 0) / data_info.get('total_samples', 1)) * 100
                html_content += f"""
            <li>训练样本: {data_info.get('train_samples', 'N/A')}</li>
            <li>测试样本: {data_info.get('test_samples', 'N/A')}</li>
            <li>特征数量: {data_info.get('feature_count', 'N/A')}</li>
            <li>正样本率: {positive_rate:.1f}%</li>
            <li>数据平衡性: {'不平衡' if positive_rate < 20 else '相对平衡'}</li>
"""

            html_content += """
        </ul>

        <p><strong>训练配置:</strong></p>
        <ul>
"""

            # 添加训练配置信息
            if 'config' in results and 'training' in results['config']:
                training_config = results['config']['training']
                html_content += f"""
            <li>学习率: {training_config.get('learning_rate', 'N/A')}</li>
            <li>批次大小: {training_config.get('batch_size', 'N/A')}</li>
            <li>最大轮数: {training_config.get('epochs', 'N/A')}</li>
            <li>早停耐心: {training_config.get('early_stopping_patience', 'N/A')}</li>
            <li>优化器: {training_config.get('optimizer', 'N/A').upper()}</li>
"""

            html_content += """
        </ul>
    </div>
</body>
</html>
"""
            
            # 保存HTML文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_path = f"deeplearning/output/training_report_{timestamp}.html"
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_path
            
        except Exception as e:
            print(f"❌ 生成训练HTML报告失败: {e}")
            return None
    
    def create_prediction_html(self, prediction_excel_file):
        """创建预测结果HTML报告"""
        try:
            # 读取预测结果
            df = pd.read_excel(prediction_excel_file)
            
            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习预测报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; color: #333; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #f2f2f2; }}
        .high-prob {{ background-color: #d4edda; }}
        .medium-prob {{ background-color: #fff3cd; }}
        .low-prob {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔮 深度学习预测报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>预测股票数量: {len(df)}</p>
    </div>
    
    <div class="section">
        <h2>🏆 Top20 预测结果</h2>
        <table>
            <tr>
                <th>排名</th>
                <th>股票代码</th>
                <th>买入日期</th>
                <th>预测概率</th>
                <th>置信度等级</th>
                <th>投资建议</th>
"""
            
            # 添加实际涨幅列（如果存在）
            if '5日最大涨幅' in df.columns:
                html_content += "<th>实际涨幅</th>"
            
            html_content += "</tr>"
            
            # 添加Top20数据
            top20 = df.head(20)
            for i, row in top20.iterrows():
                prob = row.get('涨超10%概率', 0)
                prob_class = 'high-prob' if prob >= 0.4 else 'medium-prob' if prob >= 0.3 else 'low-prob'
                
                html_content += f"""
            <tr class="{prob_class}">
                <td>{i+1}</td>
                <td>{row.get('股票', 'N/A')}</td>
                <td>{row.get('原始买入日期', row.get('买入日期', 'N/A'))}</td>
                <td>{prob:.1%}</td>
                <td>{row.get('置信度等级', 'N/A')}</td>
                <td>{row.get('投资建议', 'N/A')}</td>
"""
                
                if '5日最大涨幅' in df.columns:
                    actual_gain = row.get('5日最大涨幅', 0)
                    if isinstance(actual_gain, (int, float)):
                        html_content += f"<td>{actual_gain:.1%}</td>"
                    else:
                        html_content += f"<td>{actual_gain}</td>"
                
                html_content += "</tr>"
            
            html_content += """
        </table>
    </div>
</body>
</html>
"""
            
            # 保存HTML文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_path = f"deeplearning/output/prediction_results_{timestamp}.html"
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_path
            
        except Exception as e:
            print(f"❌ 生成预测HTML报告失败: {e}")
            return None
