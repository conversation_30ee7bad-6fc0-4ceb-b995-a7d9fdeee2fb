#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
3日成功选股预测脚本
基于现有quick_predict.py修改，使用配置文件驱动
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import json
import argparse
import glob
from datetime import datetime

class ThreeDaySuccessPredictor:
    def __init__(self, training_folder, config_path="config.json"):
        """初始化3日成功选股预测器"""
        self.training_folder = training_folder
        self.config = self.load_config(config_path)
        self.models = {}
        self.scaler = None
        self.feature_selector = None
        self.label_encoder = None
        self.feature_names = None
        self.prediction_folder = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = self.config['visualization_config']['font_family']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 从配置读取特征
        self.input_features = self.config['feature_config']['input_features']
        
        self._load_models()
    
    def load_config(self, config_path):
        """加载配置文件"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"📋 配置文件加载成功: {config_path}")
        else:
            # 尝试从训练文件夹加载配置
            training_config_path = f"{self.training_folder}/config.json"
            if os.path.exists(training_config_path):
                with open(training_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"📋 从训练文件夹加载配置: {training_config_path}")
            else:
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        return config
    
    def _load_models(self):
        """加载训练好的模型 - 基于quick_predict.py的_load_models方法"""
        print(f"🔄 加载模型 (训练文件夹: {self.training_folder})")
        
        # 加载模型
        model_files = glob.glob(f"{self.training_folder}/models/*_model.pkl")
        for model_file in model_files:
            model_name = os.path.basename(model_file).replace('_model.pkl', '')
            self.models[model_name] = joblib.load(model_file)
            print(f"   ✅ {model_name} 加载成功")
        
        if not self.models:
            raise FileNotFoundError(f"在 {self.training_folder}/models/ 中未找到模型文件")
        
        # 加载预处理器
        self.scaler = joblib.load(f"{self.training_folder}/models/scaler.pkl")
        self.feature_selector = joblib.load(f"{self.training_folder}/models/feature_selector.pkl")
        self.label_encoder = joblib.load(f"{self.training_folder}/models/label_encoder.pkl")
        
        # 加载特征名称
        with open(f"{self.training_folder}/models/feature_names.json", 'r', encoding='utf-8') as f:
            self.feature_names = json.load(f)
        
        print(f"   ✅ 预处理器加载成功")
        print(f"   📊 可用模型: {len(self.models)} 个")
        print(f"   🎯 选择特征: {len(self.feature_names)} 个")
    
    def create_prediction_folder(self):
        """创建预测输出文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_dir = self.config['output_config']['models_dir']
        self.prediction_folder = f"{base_dir}/predictions_{timestamp}"
        
        # 创建子目录
        subdirs = ['results', 'visualizations']
        for subdir in subdirs:
            os.makedirs(f"{self.prediction_folder}/{subdir}", exist_ok=True)
        
        print(f"📁 创建预测文件夹: {self.prediction_folder}")
        return self.prediction_folder
    
    def preprocess_data(self, file_path):
        """预处理数据 - 基于quick_predict.py的preprocess_data方法"""
        print(f"📁 加载数据: {os.path.basename(file_path)}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        print(f"   📊 原始数据形状: {df.shape}")
        
        # 检查特征
        missing_features = [f for f in self.input_features if f not in df.columns]
        if missing_features:
            print(f"   ⚠️  缺失特征: {missing_features[:5]}...")
            for feature in missing_features:
                df[feature] = 0
        
        X = df[self.input_features].copy()
        
        # 数据清洗 - 使用与训练时相同的逻辑
        X = self._clean_data(X)
        
        # 移除包含缺失值的行
        mask = ~X.isnull().any(axis=1)
        X = X[mask]
        df_clean = df[mask]
        
        print(f"   ✅ 清洗后数据形状: X={X.shape}")
        return X, df_clean
    
    def _clean_data(self, X):
        """数据清洗 - 与训练脚本保持一致"""
        # 替换无穷值
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # 数值类型转换
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    if X[col].astype(str).str.contains('%').any():
                        X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                    else:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    X[col] = 0
        
        # 填充缺失值
        X = X.fillna(X.median())
        
        return X
    
    def predict(self, X, original_df, threshold=0.9, min_predictions=1):
        """进行预测 - 基于quick_predict.py的predict方法，添加阈值控制"""
        print("🔮 进行预测...")
        print(f"   🎯 使用预测阈值: {threshold:.2f}")
        print(f"   📊 最少预测数量: {min_predictions}")

        # 预处理
        X_scaled = self.scaler.transform(X)
        X_selected = self.feature_selector.transform(X_scaled)

        print(f"   📊 预测特征矩阵: {X_selected.shape}")

        # 使用所有模型进行预测
        predictions = {}
        probabilities = {}
        high_precision_predictions = {}

        for model_name, model in self.models.items():
            pred_proba = model.predict_proba(X_selected)
            prob_positive = pred_proba[:, 1] if pred_proba.shape[1] > 1 else pred_proba[:, 0]

            # 使用高精确率阈值进行预测
            current_threshold = threshold
            pred_high_precision = (prob_positive > current_threshold).astype(int)

            # 如果预测数量太少，逐步降低阈值
            while pred_high_precision.sum() < min_predictions and current_threshold > 0.5:
                current_threshold -= 0.05
                pred_high_precision = (prob_positive > current_threshold).astype(int)

            # 解码预测结果
            pred_labels = self.label_encoder.inverse_transform(pred_high_precision)

            predictions[model_name] = pred_labels
            probabilities[model_name] = prob_positive
            high_precision_predictions[model_name] = pred_high_precision

            success_count = (pred_labels == '成功').sum()
            print(f"   ✅ {model_name} 预测完成 (阈值: {current_threshold:.2f}, 预测成功: {success_count}个)")
        
        # 集成预测（基于高精确率阈值的投票）
        ensemble_pred = []
        ensemble_proba = []

        for i in range(len(X_selected)):
            # 投票决定最终预测（只有当多数模型都认为是成功时才预测成功）
            votes = [predictions[model][i] for model in self.models.keys()]
            success_votes = votes.count('成功')

            # 更严格的集成策略：需要至少一半以上的模型预测成功
            final_pred = '成功' if success_votes > len(votes) / 2 else '失败'
            ensemble_pred.append(final_pred)

            # 平均概率
            avg_proba = np.mean([probabilities[model][i] for model in self.models.keys()])
            ensemble_proba.append(avg_proba)
        
        # 格式化结果
        results_df = original_df.copy()
        results_df['集成预测'] = ensemble_pred
        results_df['成功概率'] = ensemble_proba
        
        # 添加各模型的预测结果
        for model_name in self.models.keys():
            results_df[f'{model_name}_预测'] = predictions[model_name]
            results_df[f'{model_name}_概率'] = probabilities[model_name]
        
        # 统计预测结果
        pred_counts = pd.Series(ensemble_pred).value_counts()
        print(f"\n   📊 预测结果统计:")
        for label, count in pred_counts.items():
            percentage = count / len(ensemble_pred) * 100
            print(f"      {label}: {count}个 ({percentage:.1f}%)")
        
        return results_df
    
    def create_visualizations(self, results_df):
        """创建预测可视化"""
        print("\n📈 生成预测可视化")
        print("=" * 60)
        
        # 获取可视化配置
        viz_config = self.config['visualization_config']
        fig_size = viz_config['figure_size']
        colors = viz_config['colors']
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=fig_size)
        fig.suptitle('3日成功选股预测结果分析', fontsize=16, fontweight='bold')
        
        # 1. 预测分布
        ax1 = axes[0, 0]
        pred_counts = results_df['集成预测'].value_counts()
        colors_list = [colors['success'] if label == '成功' else colors['failure'] for label in pred_counts.index]
        bars = ax1.bar(pred_counts.index, pred_counts.values, color=colors_list, alpha=0.8)
        ax1.set_xlabel('预测结果')
        ax1.set_ylabel('数量')
        ax1.set_title('预测结果分布')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontsize=12)
        
        # 2. 成功概率分布
        ax2 = axes[0, 1]
        ax2.hist(results_df['成功概率'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('成功概率')
        ax2.set_ylabel('频次')
        ax2.set_title('成功概率分布')
        ax2.grid(True, alpha=0.3)
        
        # 3. 高置信度推荐
        ax3 = axes[0, 2]
        high_conf = results_df[results_df['成功概率'] > 0.7]
        medium_conf = results_df[(results_df['成功概率'] > 0.5) & (results_df['成功概率'] <= 0.7)]
        low_conf = results_df[results_df['成功概率'] <= 0.5]
        
        categories = ['高置信度\n(>70%)', '中等置信度\n(50%-70%)', '低置信度\n(≤50%)']
        counts = [len(high_conf), len(medium_conf), len(low_conf)]
        colors_conf = [colors['success'], colors['warning'], colors['failure']]
        
        bars = ax3.bar(categories, counts, color=colors_conf, alpha=0.8)
        ax3.set_ylabel('股票数量')
        ax3.set_title('置信度分析')
        
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)
        
        # 4. 模型一致性分析
        ax4 = axes[1, 0]
        model_names = [name for name in results_df.columns if name.endswith('_预测')]
        consistency_scores = []
        
        for idx, row in results_df.iterrows():
            model_preds = [row[col] for col in model_names]
            success_count = model_preds.count('成功')
            consistency = success_count / len(model_preds)
            consistency_scores.append(consistency)
        
        ax4.hist(consistency_scores, bins=10, alpha=0.7, color='lightgreen', edgecolor='black')
        ax4.set_xlabel('模型一致性 (成功预测比例)')
        ax4.set_ylabel('频次')
        ax4.set_title('模型预测一致性')
        ax4.grid(True, alpha=0.3)
        
        # 5. 推荐股票展示
        ax5 = axes[1, 1]
        if len(high_conf) > 0:
            top_stocks = high_conf.nlargest(10, '成功概率')
            if '股票' in top_stocks.columns:
                stock_names = top_stocks['股票'].tolist()
                probabilities = top_stocks['成功概率'].tolist()
                
                bars = ax5.barh(range(len(stock_names)), probabilities, color=colors['success'], alpha=0.8)
                ax5.set_yticks(range(len(stock_names)))
                ax5.set_yticklabels(stock_names)
                ax5.set_xlabel('成功概率')
                ax5.set_title('Top 10 推荐股票')
                ax5.grid(True, alpha=0.3)
            else:
                ax5.text(0.5, 0.5, '无股票代码信息', ha='center', va='center', transform=ax5.transAxes)
                ax5.set_title('推荐股票')
        else:
            ax5.text(0.5, 0.5, '无高置信度推荐', ha='center', va='center', transform=ax5.transAxes)
            ax5.set_title('推荐股票')
        
        # 6. 风险评估
        ax6 = axes[1, 2]
        risk_levels = ['低风险\n(>70%)', '中风险\n(50%-70%)', '高风险\n(≤50%)']
        risk_counts = [len(high_conf), len(medium_conf), len(low_conf)]
        risk_colors = [colors['success'], colors['warning'], colors['failure']]
        
        wedges, texts, autotexts = ax6.pie(risk_counts, labels=risk_levels, colors=risk_colors, 
                                          autopct='%1.1f%%', startangle=90)
        ax6.set_title('风险评估分布')
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = f"{self.prediction_folder}/visualizations/prediction_results.png"
        plt.savefig(chart_path, dpi=viz_config['dpi'], bbox_inches='tight')
        plt.close()  # 关闭图表而不显示

        print(f"   ✅ 图表已保存: {chart_path}")
        return chart_path

    def save_results(self, results_df, input_file):
        """保存预测结果 - 基于quick_predict.py的save_results方法"""
        print("\n💾 保存预测结果")
        print("=" * 60)

        # 保存详细预测结果
        results_path = f"{self.prediction_folder}/results/prediction_results.xlsx"
        results_df.to_excel(results_path, index=False)
        print(f"   ✅ 详细结果已保存: {results_path}")

        # 保存高置信度推荐
        high_conf_stocks = results_df[
            (results_df['集成预测'] == '成功') &
            (results_df['成功概率'] > 0.7)
        ]

        if len(high_conf_stocks) > 0:
            high_conf_path = f"{self.prediction_folder}/results/high_confidence_stocks.xlsx"
            high_conf_stocks.to_excel(high_conf_path, index=False)
            print(f"   ✅ 高置信度推荐已保存: {high_conf_path}")

            print(f"\n🎯 高置信度成功股票推荐 ({len(high_conf_stocks)}只):")
            for idx, row in high_conf_stocks.iterrows():
                stock = row.get('股票', f'第{idx+1}只')
                prob = row['成功概率']
                print(f"      📈 {stock}: 成功概率 {prob:.1%}")
        else:
            print(f"\n⚠️  无高置信度成功股票")

        # 保存预测摘要
        summary_data = {
            'prediction_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'input_file': input_file,
                'training_folder': self.training_folder,
                'total_predictions': len(results_df),
                'models_used': list(self.models.keys()),
                'selected_features': self.feature_names
            },
            'prediction_summary': {
                'success_predictions': int((results_df['集成预测'] == '成功').sum()),
                'failure_predictions': int((results_df['集成预测'] == '失败').sum()),
                'high_confidence_count': len(high_conf_stocks),
                'average_success_probability': float(results_df['成功概率'].mean()),
                'max_success_probability': float(results_df['成功概率'].max()),
                'min_success_probability': float(results_df['成功概率'].min())
            }
        }

        summary_path = f"{self.prediction_folder}/summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)

        print(f"   ✅ 预测摘要已保存: {summary_path}")

        return results_path, high_conf_path if len(high_conf_stocks) > 0 else None


def find_latest_training_folder(base_dir="models"):
    """查找最新的训练文件夹"""
    training_folders = glob.glob(f"{base_dir}/training_*")
    if not training_folders:
        raise FileNotFoundError(f"在 {base_dir} 中未找到训练文件夹")

    # 按时间戳排序，返回最新的
    latest_folder = max(training_folders, key=lambda x: os.path.basename(x).split('_')[1])
    return latest_folder


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3日成功选股预测')
    parser.add_argument('--input_file', '-i', required=True, help='输入Excel文件路径')
    parser.add_argument('--training_folder', '-t', default=None, help='训练文件夹路径 (可选，默认使用最新)')
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    parser.add_argument('--output_dir', default=None, help='输出目录 (可选)')
    parser.add_argument('--threshold', '-th', type=float, default=0.9, help='预测阈值 (0.5-0.99，默认0.9，越高精确率越高)')
    parser.add_argument('--min_predictions', '-mp', type=int, default=1, help='最少预测数量 (默认1，如果高阈值预测数量少于此值会降低阈值)')

    args = parser.parse_args()

    print("🔮 3日成功选股预测")
    print("=" * 80)

    try:
        # 确定训练文件夹
        if args.training_folder:
            training_folder = args.training_folder
        else:
            training_folder = find_latest_training_folder()

        print(f"📁 使用训练文件夹: {os.path.basename(training_folder)}")
        print(f"📊 输入数据: {os.path.basename(args.input_file)}")
        print("=" * 80)

        # 创建预测器
        predictor = ThreeDaySuccessPredictor(training_folder, args.config)

        # 如果指定了输出目录，更新配置
        if args.output_dir:
            predictor.config['output_config']['models_dir'] = args.output_dir

        # 创建预测文件夹
        prediction_folder = predictor.create_prediction_folder()

        # 1. 预处理数据
        X, df = predictor.preprocess_data(args.input_file)

        # 2. 进行预测
        results_df = predictor.predict(X, df, threshold=args.threshold, min_predictions=args.min_predictions)

        # 3. 创建可视化
        chart_path = predictor.create_visualizations(results_df)

        # 4. 保存结果
        results_path, high_conf_path = predictor.save_results(results_df, args.input_file)

        # 5. 打印总结
        print("\n" + "=" * 80)
        print("🎉 预测完成！")
        print("=" * 80)

        print(f"📊 预测数据: {len(results_df)} 条记录")
        print(f"🎯 使用特征: {len(predictor.feature_names)} 个")
        print(f"🤖 使用模型: {len(predictor.models)} 个")

        # 显示预测统计
        pred_counts = results_df['集成预测'].value_counts()
        success_count = pred_counts.get('成功', 0)
        failure_count = pred_counts.get('失败', 0)
        high_conf_count = len(results_df[
            (results_df['集成预测'] == '成功') &
            (results_df['成功概率'] > 0.7)
        ])

        print(f"📈 预测成功: {success_count} 个")
        print(f"📉 预测失败: {failure_count} 个")
        print(f"🎯 高置信度推荐: {high_conf_count} 个")

        print(f"\n📁 输出文件夹: {prediction_folder}")
        print(f"📊 可视化图表: {chart_path}")
        print(f"📋 详细结果: {results_path}")
        if high_conf_path:
            print(f"🎯 高置信度推荐: {high_conf_path}")

    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
