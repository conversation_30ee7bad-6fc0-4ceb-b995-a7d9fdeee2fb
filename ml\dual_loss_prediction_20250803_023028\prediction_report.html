
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双损失函数深度学习股票预测结果</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2196F3;
        }
        .header h1 {
            color: #1976D2;
            margin-bottom: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card {
            background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        .summary-card .value {
            font-size: 1.8em;
            font-weight: bold;
            margin: 10px 0;
        }
        .chart-container {
            margin: 30px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 10px;
        }
        .chart-title {
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
        }
        .predictions-table {
            margin: 30px 0;
            overflow-x: auto;
        }
        .predictions-table table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .predictions-table th {
            background-color: #1976D2;
            color: white;
            padding: 15px;
            text-align: left;
        }
        .predictions-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        .predictions-table tr:hover {
            background-color: #f5f5f5;
        }
        .model-tag {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 0.9em;
            font-weight: bold;
        }
        .model-precisionmlp { background-color: #4CAF50; }
        .model-residualnet { background-color: #FF9800; }
        .model-attentionnet { background-color: #9C27B0; }
        .model-ensemblemodel { background-color: #F44336; }
        .prob-bar {
            width: 100%;
            height: 20px;
            background-color: #eee;
            border-radius: 10px;
            overflow: hidden;
        }
        .prob-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #FFC107 50%, #F44336 100%);
            border-radius: 10px;
        }
        canvas {
            max-height: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 双损失函数深度学习股票预测结果</h1>
            <p>预测时间: 2025年08月03日 02:30:28</p>
            <p>输入文件: 选股分析结果_20250730_225041.xlsx</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>📊 总预测数量</h3>
                <div class="value">8</div>
                <p>个股票</p>
            </div>
            <div class="summary-card">
                <h3>🤖 使用模型</h3>
                <div class="value">1</div>
                <p>个模型</p>
            </div>
            <div class="summary-card">
                <h3>📈 平均概率</h3>
                <div class="value">71.0%</div>
                <p>预测置信度</p>
            </div>
            <div class="summary-card">
                <h3>🎯 最高概率</h3>
                <div class="value">93.7%</div>
                <p>最佳预测</p>
            </div>
            <div class="summary-card" style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);">
                <h3>✅ 预测成功</h3>
                <div class="value">0</div>
                <p>个股票</p>
            </div>
            <div class="summary-card" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                <h3>🎯 成功率</h3>
                <div class="value">0.0%</div>
                <p>预测准确性</p>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 各模型预测数量分布</div>
            <canvas id="modelChart"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-title">📈 预测概率分布</div>
            <canvas id="probChart"></canvas>
        </div>

        <div class="predictions-table">
            <h2>📋 详细预测结果</h2>
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>模型</th>
                        <th>股票代码</th>
                        <th>预测概率</th>
                        <th>概率条</th>
                        <th>买入日期</th>
                        <th>实际涨幅</th>
                        <th>预测结果</th>
                    </tr>
                </thead>
                <tbody>

                    <tr>
                        <td>4</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>603948</strong></td>
                        <td>93.7%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 93.67647171020508%"></div>
                            </div>
                        </td>
                        <td>2025-07-17</td>
                        <td style="color: red;"><strong>4.1%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>3</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>603948</strong></td>
                        <td>84.4%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 84.35111045837402%"></div>
                            </div>
                        </td>
                        <td>2025-07-18</td>
                        <td style="color: red;"><strong>3.6%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>6</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>603155</strong></td>
                        <td>80.1%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 80.06107211112976%"></div>
                            </div>
                        </td>
                        <td>2025-07-16</td>
                        <td style="color: red;"><strong>2.8%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>5</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>2811</strong></td>
                        <td>72.8%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 72.79096245765686%"></div>
                            </div>
                        </td>
                        <td>2025-07-16</td>
                        <td style="color: red;"><strong>1.9%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>8</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>603629</strong></td>
                        <td>65.1%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 65.14437794685364%"></div>
                            </div>
                        </td>
                        <td>2025-07-15</td>
                        <td style="color: red;"><strong>4.5%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>2</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>2235</strong></td>
                        <td>61.2%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 61.19621992111206%"></div>
                            </div>
                        </td>
                        <td>2025-07-19</td>
                        <td style="color: red;"><strong>3.6%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>7</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>600770</strong></td>
                        <td>58.1%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 58.10490846633911%"></div>
                            </div>
                        </td>
                        <td>2025-07-15</td>
                        <td style="color: red;"><strong>8.2%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                    <tr>
                        <td>1</td>
                        <td><span class="model-tag model-residualnet">ResidualNet</span></td>
                        <td><strong>601086</strong></td>
                        <td>52.8%</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: 52.779847383499146%"></div>
                            </div>
                        </td>
                        <td>2025-07-22</td>
                        <td style="color: red;"><strong>2.0%</strong></td>
                        <td>❌ 失败</td>
                    </tr>

                </tbody>
            </table>
        </div>

        <script>

        // 模型分布图表
        const ctx1 = document.getElementById('modelChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ['ResidualNet'],
                datasets: [{
                    data: [8],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(76, 175, 80, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '各模型预测数量占比'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 概率分布图表
        const ctx2 = document.getElementById('probChart').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                datasets: [{
                    label: '预测数量',
                    data: [2, 2, 1, 2, 1],  // 去掉最后一个，因为通常为0
                    backgroundColor: 'rgba(33, 150, 243, 0.8)',
                    borderColor: 'rgba(33, 150, 243, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '预测数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '预测概率区间'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '预测概率分布'
                    }
                }
            }
        });
        </script>
    </div>
</body>
</html>
