#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
3日成功选股预测模型训练脚本
基于现有success_prediction_system.py修改，使用配置文件驱动
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
import xgboost as xgb
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, f1_score, recall_score, precision_score
from sklearn.feature_selection import SelectKBest, mutual_info_classif
import joblib
import warnings
from datetime import datetime
import os
import json
import argparse
import glob
from scipy import stats
from scipy.stats import chi2_contingency, mannwhitneyu, ttest_ind

warnings.filterwarnings('ignore')

class StatisticalLearningTrainer:
    """统计学习方法训练器"""

    def __init__(self, config_path="config_statistical.json"):
        """初始化统计学习训练器"""
        self.config = self.load_config(config_path)
        self.statistical_rules = {}
        self.feature_statistics = {}
        self.results = {}
        self.training_folder = None

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = self.config['visualization_config']['font_family']
        plt.rcParams['axes.unicode_minus'] = False

        # 从配置读取特征和目标
        self.input_features = self.config['feature_config']['input_features']
        self.target_feature = self.config['data_config']['target_column']

        print(f"🎯 统计学习目标变量: {self.target_feature}")
        print(f"📊 输入特征: {len(self.input_features)} 个")

    def load_config(self, config_path):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"📋 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            raise

    def load_and_preprocess_data(self, file_path, is_training=True):
        """加载和预处理数据"""
        print(f"📁 加载数据: {os.path.basename(file_path)}")

        try:
            df = pd.read_excel(file_path)
            print(f"   📊 原始数据形状: {df.shape}")

            # 提取特征和目标
            available_features = [f for f in self.input_features if f in df.columns]
            print(f"   📋 可用特征: {len(available_features)}/{len(self.input_features)} 个")

            if len(available_features) == 0:
                raise ValueError("没有找到可用的特征列")

            X = df[available_features].copy()

            # 数据清理 - 转换为数值类型
            for col in X.columns:
                if X[col].dtype == 'object':
                    # 尝试转换为数值类型
                    X[col] = pd.to_numeric(X[col], errors='coerce')

            if is_training:
                if self.target_feature not in df.columns:
                    raise ValueError(f"目标变量 '{self.target_feature}' 不存在")

                y = df[self.target_feature].copy()

                # 删除目标变量为空的行
                valid_mask = y.notna() & (y != '') & (y != '未知')
                X = X[valid_mask]
                y = y[valid_mask]

                # 删除特征值全为NaN的行
                X = X.dropna(how='all')
                y = y.loc[X.index]

                print(f"   ✅ 清洗后数据形状: X={X.shape}, y={y.shape}")

                # 统计目标变量分布
                target_counts = y.value_counts().to_dict()
                print(f"   📊 目标变量分布: {target_counts}")

                return X, y, df.loc[X.index]
            else:
                # 删除特征值全为NaN的行
                X = X.dropna(how='all')
                print(f"   ✅ 清洗后数据形状: X={X.shape}")
                return X, None, df.loc[X.index]

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise

    def statistical_analysis(self, X_train, y_train):
        """统计学分析"""
        print("\n📊 统计学分析")
        print("=" * 60)

        # 分离成功和失败组
        success_mask = y_train == '成功'
        failure_mask = y_train == '失败'

        X_success = X_train[success_mask]
        X_failure = X_train[failure_mask]

        statistical_results = {}

        for feature in self.input_features:
            feature_stats = {}

            success_values = X_success[feature].dropna()
            failure_values = X_failure[feature].dropna()

            if len(success_values) > 0 and len(failure_values) > 0:
                # 基本统计
                feature_stats['success_mean'] = success_values.mean()
                feature_stats['success_std'] = success_values.std()
                feature_stats['failure_mean'] = failure_values.mean()
                feature_stats['failure_std'] = failure_values.std()

                # t检验
                if self.config['statistical_config']['statistical_tests']['use_ttest']:
                    try:
                        t_stat, p_value = ttest_ind(success_values, failure_values)
                        feature_stats['ttest_pvalue'] = p_value
                        feature_stats['ttest_significant'] = p_value < self.config['statistical_config']['significance_level']
                    except:
                        feature_stats['ttest_pvalue'] = 1.0
                        feature_stats['ttest_significant'] = False

                # Mann-Whitney U检验
                if self.config['statistical_config']['statistical_tests']['use_mannwhitney']:
                    try:
                        u_stat, p_value = mannwhitneyu(success_values, failure_values, alternative='two-sided')
                        feature_stats['mannwhitney_pvalue'] = p_value
                        feature_stats['mannwhitney_significant'] = p_value < self.config['statistical_config']['significance_level']
                    except:
                        feature_stats['mannwhitney_pvalue'] = 1.0
                        feature_stats['mannwhitney_significant'] = False

                # 相关性分析
                if self.config['statistical_config']['statistical_tests']['use_correlation']:
                    y_encoded = (y_train == '成功').astype(int)
                    correlation = X_train[feature].corr(y_encoded)
                    feature_stats['correlation'] = correlation
                    feature_stats['correlation_significant'] = abs(correlation) > self.config['statistical_config']['correlation_threshold']

                # 效应大小 (Cohen's d)
                pooled_std = np.sqrt(((len(success_values) - 1) * success_values.var() +
                                    (len(failure_values) - 1) * failure_values.var()) /
                                   (len(success_values) + len(failure_values) - 2))
                if pooled_std > 0:
                    cohens_d = (success_values.mean() - failure_values.mean()) / pooled_std
                    feature_stats['cohens_d'] = cohens_d
                    feature_stats['effect_size'] = 'large' if abs(cohens_d) > 0.8 else 'medium' if abs(cohens_d) > 0.5 else 'small'

            statistical_results[feature] = feature_stats

        self.feature_statistics = statistical_results
        return statistical_results

    def generate_statistical_rules(self, X_train, y_train):
        """基于统计分析生成预测规则"""
        print("\n🔍 生成统计学习规则")
        print("=" * 60)

        rules = {}
        significant_features = []

        # 筛选显著特征
        for feature, stats in self.feature_statistics.items():
            is_significant = False

            # 检查各种显著性
            if stats.get('ttest_significant', False):
                is_significant = True
                print(f"   ✅ {feature}: t检验显著 (p={stats['ttest_pvalue']:.4f})")

            if stats.get('mannwhitney_significant', False):
                is_significant = True
                print(f"   ✅ {feature}: Mann-Whitney显著 (p={stats['mannwhitney_pvalue']:.4f})")

            if stats.get('correlation_significant', False):
                is_significant = True
                print(f"   ✅ {feature}: 相关性显著 (r={stats['correlation']:.4f})")

            if is_significant:
                significant_features.append(feature)

                # 生成规则
                success_mean = stats['success_mean']
                failure_mean = stats['failure_mean']

                # 确定阈值方向
                if success_mean > failure_mean:
                    # 成功组均值更高，使用上阈值
                    threshold = success_mean - 0.5 * abs(success_mean - failure_mean)
                    rule_type = 'greater'
                else:
                    # 成功组均值更低，使用下阈值
                    threshold = success_mean + 0.5 * abs(success_mean - failure_mean)
                    rule_type = 'less'

                rules[feature] = {
                    'threshold': threshold,
                    'rule_type': rule_type,
                    'success_mean': success_mean,
                    'failure_mean': failure_mean,
                    'effect_size': stats.get('effect_size', 'unknown'),
                    'p_value': min(stats.get('ttest_pvalue', 1), stats.get('mannwhitney_pvalue', 1))
                }

        print(f"\n   📊 生成了 {len(rules)} 个统计规则")
        print(f"   🎯 显著特征: {len(significant_features)} 个")

        self.statistical_rules = rules
        return rules, significant_features

    def predict_with_statistical_rules(self, X_test):
        """使用统计规则进行预测"""
        print("\n🔮 统计学习预测")
        print("=" * 60)

        predictions = []
        prediction_scores = []

        for idx, row in X_test.iterrows():
            score = 0
            rule_count = 0

            for feature, rule in self.statistical_rules.items():
                if feature in row.index and not pd.isna(row[feature]):
                    rule_count += 1

                    if rule['rule_type'] == 'greater':
                        if row[feature] >= rule['threshold']:
                            score += 1
                    else:  # 'less'
                        if row[feature] <= rule['threshold']:
                            score += 1

            # 计算成功概率
            if rule_count > 0:
                success_probability = score / rule_count
            else:
                success_probability = 0.0

            # 预测结果
            threshold = self.config['statistical_config']['rule_based_criteria']['success_probability_threshold']
            prediction = '成功' if success_probability >= threshold else '失败'

            predictions.append(prediction)
            prediction_scores.append(success_probability)

        return predictions, prediction_scores

    def evaluate_statistical_model(self, X_val, y_val):
        """评估统计学习模型"""
        print("\n📈 统计学习模型评估")
        print("=" * 60)

        predictions, scores = self.predict_with_statistical_rules(X_val)

        # 计算准确率
        correct = sum(1 for pred, actual in zip(predictions, y_val) if pred == actual)
        accuracy = correct / len(predictions)

        # 计算成功预测精确率
        success_predictions = [i for i, pred in enumerate(predictions) if pred == '成功']
        if len(success_predictions) > 0:
            success_correct = sum(1 for i in success_predictions if y_val.iloc[i] == '成功')
            success_precision = success_correct / len(success_predictions)
        else:
            success_precision = 0.0

        # 计算召回率
        actual_success = sum(1 for actual in y_val if actual == '成功')
        if actual_success > 0:
            success_recall = sum(1 for pred, actual in zip(predictions, y_val) if pred == '成功' and actual == '成功') / actual_success
        else:
            success_recall = 0.0

        results = {
            'accuracy': accuracy,
            'success_precision': success_precision,
            'success_recall': success_recall,
            'total_predictions': len(predictions),
            'success_predictions': len(success_predictions),
            'actual_success': actual_success
        }

        print(f"   📊 总体准确率: {accuracy:.4f}")
        print(f"   🎯 成功预测精确率: {success_precision:.4f}")
        print(f"   📈 成功召回率: {success_recall:.4f}")
        print(f"   📋 预测成功数量: {len(success_predictions)}")
        print(f"   📋 实际成功数量: {actual_success}")

        self.results = results
        return results

    def save_statistical_model(self, train_file, val_file):
        """保存统计学习模型"""
        print("\n💾 保存统计学习模型")
        print("=" * 60)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 创建输出文件夹
        output_folder = f"statistical_models/training_{timestamp}"
        os.makedirs(output_folder, exist_ok=True)

        # 保存统计规则
        rules_path = os.path.join(output_folder, "statistical_rules.json")
        with open(rules_path, 'w', encoding='utf-8') as f:
            json.dump(self.statistical_rules, f, ensure_ascii=False, indent=2, default=str)
        print(f"   ✅ 统计规则已保存: {rules_path}")

        # 保存特征统计
        stats_path = os.path.join(output_folder, "feature_statistics.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(self.feature_statistics, f, ensure_ascii=False, indent=2, default=str)
        print(f"   ✅ 特征统计已保存: {stats_path}")

        # 保存配置和结果
        summary = {
            'timestamp': timestamp,
            'train_file': train_file,
            'val_file': val_file,
            'config': self.config,
            'results': self.results,
            'rules_count': len(self.statistical_rules)
        }

        summary_path = os.path.join(output_folder, "training_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
        print(f"   ✅ 训练总结已保存: {summary_path}")

        return output_folder

class ThreeDaySuccessTrainer:
    def __init__(self, config_path="config.json"):
        """初始化3日成功选股训练器"""
        self.config = self.load_config(config_path)
        self.models = {}
        self.scaler = None
        self.feature_selector = None
        self.label_encoder = None
        self.feature_names = None
        self.results = {}
        self.training_folder = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = self.config['visualization_config']['font_family']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 从配置读取特征和目标
        self.input_features = self.config['feature_config']['input_features']
        self.target_feature = self.config['data_config']['target_column']
        
        print(f"🎯 目标变量: {self.target_feature}")
        print(f"📊 输入特征: {len(self.input_features)} 个")
    
    def load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"📋 配置文件加载成功: {config_path}")
        return config
    
    def create_training_folder(self):
        """创建训练输出文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_dir = self.config['output_config']['models_dir']
        self.training_folder = f"{base_dir}/training_{timestamp}"
        
        # 创建子目录
        subdirs = ['models', 'reports', 'visualizations']
        for subdir in subdirs:
            os.makedirs(f"{self.training_folder}/{subdir}", exist_ok=True)
        
        print(f"📁 创建训练文件夹: {self.training_folder}")
        return self.training_folder
    
    def load_and_preprocess_data(self, file_path, is_training=True):
        """加载和预处理数据 - 基于原success_prediction_system.py的方法"""
        print(f"📁 加载数据: {os.path.basename(file_path)}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        print(f"   📊 原始数据形状: {df.shape}")
        
        # 检查目标变量是否存在
        if is_training and self.target_feature not in df.columns:
            raise ValueError(f"目标变量 '{self.target_feature}' 不存在于数据中")
        
        # 提取特征
        missing_features = [f for f in self.input_features if f not in df.columns]
        if missing_features:
            print(f"   ⚠️  缺失特征: {missing_features[:5]}...")
            for feature in missing_features:
                df[feature] = 0
        
        X = df[self.input_features].copy()
        
        # 数据清洗 - 使用原有的_clean_data逻辑
        X = self._clean_data(X)
        
        if is_training:
            y = df[self.target_feature].copy()
            
            # 移除包含缺失值的行
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X = X[mask]
            y = y[mask]
            df_clean = df[mask]
            
            print(f"   ✅ 清洗后数据形状: X={X.shape}, y={y.shape}")
            print(f"   📊 目标变量分布: {y.value_counts().to_dict()}")
            
            return X, y, df_clean
        else:
            # 移除包含缺失值的行
            mask = ~X.isnull().any(axis=1)
            X = X[mask]
            df_clean = df[mask]
            
            print(f"   ✅ 清洗后数据形状: X={X.shape}")
            return X, df_clean
    
    def _clean_data(self, X):
        """数据清洗 - 基于原success_prediction_system.py的方法"""
        # 替换无穷值
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # 数值类型转换
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    if X[col].astype(str).str.contains('%').any():
                        X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                    else:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    X[col] = 0
        
        # 填充缺失值
        X = X.fillna(X.median())
        
        return X

    def _balance_data(self, X, y):
        """数据平衡处理 - 上采样少数类以提升成功预测"""
        from collections import Counter

        # 统计类别分布
        counter = Counter(y)
        print(f"      原始分布: {dict(counter)}")

        # 找到多数类和少数类
        majority_class = counter.most_common(1)[0][0]
        minority_class = counter.most_common()[-1][0]

        # 分离多数类和少数类
        majority_mask = y == majority_class
        minority_mask = y == minority_class

        X_majority = X[majority_mask]
        y_majority = y[majority_mask]
        X_minority = X[minority_mask]
        y_minority = y[minority_mask]

        # 适度上采样少数类 - 增加到多数类的30%以保持精确率
        n_majority = len(X_majority)
        n_minority = len(X_minority)
        target_minority = int(n_majority * 0.3)  # 适度提升少数类比例，重点保证精确率

        if target_minority > n_minority:
            # 随机上采样
            indices = np.random.choice(n_minority, target_minority - n_minority, replace=True)
            X_minority_upsampled = np.vstack([X_minority, X_minority[indices]])
            y_minority_upsampled = np.hstack([y_minority, y_minority[indices]])
        else:
            X_minority_upsampled = X_minority
            y_minority_upsampled = y_minority

        # 合并数据
        X_balanced = np.vstack([X_majority, X_minority_upsampled])
        y_balanced = np.hstack([y_majority, y_minority_upsampled])

        # 打乱数据
        indices = np.random.permutation(len(X_balanced))
        X_balanced = X_balanced[indices]
        y_balanced = y_balanced[indices]

        counter_balanced = Counter(y_balanced)
        print(f"      平衡后分布: {dict(counter_balanced)}")

        return X_balanced, y_balanced

    def _balance_data_light(self, X, y):
        """轻微数据平衡处理 - 防止过拟合"""
        from collections import Counter

        # 统计类别分布
        counter = Counter(y)
        print(f"      原始分布: {dict(counter)}")

        # 计算当前正负样本比例
        minority_count = counter.most_common()[-1][1]
        majority_count = counter.most_common()[0][1]
        current_ratio = minority_count / majority_count

        print(f"   📊 当前正负样本比例: {current_ratio:.3f}")

        # 只有当比例过于不平衡时才进行轻微平衡
        if current_ratio < 0.1:  # 如果少数类占比小于10%
            target_ratio = 0.15  # 提升到15%

            # 找到多数类和少数类
            majority_class = counter.most_common(1)[0][0]
            minority_class = counter.most_common()[-1][0]

            # 分离多数类和少数类
            majority_mask = y == majority_class
            minority_mask = y == minority_class

            X_majority = X[majority_mask]
            y_majority = y[majority_mask]
            X_minority = X[minority_mask]
            y_minority = y[minority_mask]

            # 计算目标少数类数量
            target_minority = int(len(X_majority) * target_ratio)

            if target_minority > len(X_minority):
                # 随机上采样
                indices = np.random.choice(len(X_minority), target_minority - len(X_minority), replace=True)
                X_minority_upsampled = np.vstack([X_minority, X_minority[indices]])
                y_minority_upsampled = np.hstack([y_minority, y_minority[indices]])
            else:
                X_minority_upsampled = X_minority
                y_minority_upsampled = y_minority

            # 合并数据
            X_balanced = np.vstack([X_majority, X_minority_upsampled])
            y_balanced = np.hstack([y_majority, y_minority_upsampled])

            # 打乱数据
            indices = np.random.permutation(len(X_balanced))
            X_balanced = X_balanced[indices]
            y_balanced = y_balanced[indices]

            counter_balanced = Counter(y_balanced)
            print(f"   ⚖️  应用轻微平衡，目标比例: {target_ratio:.3f}")
            print(f"      平衡后分布: {dict(counter_balanced)}")
            return X_balanced, y_balanced
        else:
            print(f"   ✅ 数据比例合理，不需要平衡")
            return X, y

    def train_models(self, X_train, y_train, X_val, y_val):
        """训练机器学习模型 - 基于原success_prediction_system.py的方法"""
        print("\n🤖 训练机器学习模型")
        print("=" * 60)
        
        # 标准化特征
        print("   📊 特征标准化...")
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 编码目标变量
        print("   🏷️  编码目标变量...")
        self.label_encoder = LabelEncoder()
        y_train_encoded = self.label_encoder.fit_transform(y_train)
        y_val_encoded = self.label_encoder.transform(y_val)
        
        # 特征选择
        k_best = self.config['feature_config']['feature_selection']['k_best']
        print(f"   🎯 特征选择 (Top {k_best})...")
        self.feature_selector = SelectKBest(mutual_info_classif, k=k_best)
        X_train_selected = self.feature_selector.fit_transform(X_train_scaled, y_train_encoded)
        X_val_selected = self.feature_selector.transform(X_val_scaled)

        # 轻微数据平衡处理，防止过拟合
        print("   ⚖️  轻微数据平衡处理，防止过拟合")
        X_train_balanced, y_train_balanced = self._balance_data_light(X_train_selected, y_train_encoded)
        print(f"   ✅ 平衡后训练数据: {X_train_balanced.shape}")

        # 使用平衡后的数据
        X_train_selected = X_train_balanced
        y_train_encoded = y_train_balanced
        
        # 获取选择的特征名称
        selected_indices = self.feature_selector.get_support(indices=True)
        self.feature_names = [self.input_features[i] for i in selected_indices]
        print(f"   ✅ 选择的特征: {len(self.feature_names)}个")
        
        # 从配置创建模型
        models_config = {}
        for model_name, model_params in self.config['model_config']['models'].items():
            if model_name == 'RandomForest':
                models_config[model_name] = RandomForestClassifier(**model_params)
            elif model_name == 'GradientBoosting':
                models_config[model_name] = GradientBoostingClassifier(**model_params)
            elif model_name == 'LogisticRegression':
                models_config[model_name] = LogisticRegression(**model_params)
            elif model_name == 'SVM':
                models_config[model_name] = SVC(**model_params)
            elif model_name == 'XGBoost':
                try:
                    import xgboost as xgb
                    models_config[model_name] = xgb.XGBClassifier(**model_params)
                except ImportError:
                    print(f"   ⚠️  XGBoost未安装，跳过{model_name}模型")
                    continue
            elif model_name == 'LightGBM':
                try:
                    import lightgbm as lgb
                    models_config[model_name] = lgb.LGBMClassifier(**model_params)
                except ImportError:
                    print(f"   ⚠️  LightGBM未安装，跳过{model_name}模型")
                    continue
        
        # 训练和评估模型
        cv_config = self.config['model_config']['cross_validation']
        cv = StratifiedKFold(
            n_splits=cv_config['cv_folds'], 
            shuffle=cv_config['shuffle'], 
            random_state=cv_config['random_state']
        )
        
        for model_name, model in models_config.items():
            print(f"\n   🔄 训练 {model_name}...")
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train_selected, y_train_encoded, cv=cv, scoring='accuracy')
            
            # 训练模型 - 添加早停机制防止过拟合
            if model_name in ['XGBoost', 'LightGBM'] and 'early_stopping_rounds' in model_params:
                # 使用验证集进行早停
                if model_name == 'XGBoost':
                    model.fit(X_train_selected, y_train_encoded,
                             eval_set=[(X_val_selected, y_val_encoded)],
                             verbose=False)
                else:  # LightGBM
                    model.fit(X_train_selected, y_train_encoded,
                             eval_set=[(X_val_selected, y_val_encoded)],
                             callbacks=[lgb.early_stopping(model_params['early_stopping_rounds'], verbose=False)])
            else:
                model.fit(X_train_selected, y_train_encoded)
            
            # 预测
            train_pred = model.predict(X_train_selected)
            val_pred_proba = model.predict_proba(X_val_selected)

            # 使用更灵活的阈值策略，适应5日成功选股的特点
            thresholds_to_try = [0.8, 0.75, 0.7, 0.65, 0.6, 0.55, 0.5, 0.45, 0.4]
            final_threshold = 0.3
            val_pred = None

            for threshold in thresholds_to_try:
                temp_pred = (val_pred_proba[:, 1] > threshold).astype(int)
                if temp_pred.sum() >= 2:  # 至少有2个预测
                    final_threshold = threshold
                    val_pred = temp_pred
                    break

            # 如果所有阈值都没有足够预测，使用更低的阈值
            if val_pred is None:
                final_threshold = 0.3
                val_pred = (val_pred_proba[:, 1] > final_threshold).astype(int)

            # 记录使用的阈值
            print(f"      使用预测阈值: {final_threshold:.2f} (预测正样本: {val_pred.sum()}个)")
            
            # 计算指标
            train_accuracy = model.score(X_train_selected, y_train_encoded)
            val_accuracy = model.score(X_val_selected, y_val_encoded)
            
            try:
                auc_score = roc_auc_score(y_val_encoded, val_pred_proba[:, 1])
            except:
                auc_score = 0.0
            
            f1 = f1_score(y_val_encoded, val_pred, average='weighted')

            # 计算正样本的精确率、召回率和F1分数
            success_encoded = self.label_encoder.transform(['成功'])[0]

            # 正样本召回率（最重要的指标）
            success_recall = recall_score(y_val_encoded, val_pred, pos_label=success_encoded)

            # 正样本精确率
            success_precision = precision_score(y_val_encoded, val_pred, pos_label=success_encoded, zero_division=0)

            # 正样本F1分数
            success_f1 = f1_score(y_val_encoded, val_pred, pos_label=success_encoded, zero_division=0)

            # 统计实际数量
            actual_positive = (y_val_encoded == success_encoded).sum()
            predicted_positive = (val_pred == success_encoded).sum()
            true_positive = ((y_val_encoded == success_encoded) & (val_pred == success_encoded)).sum()

            # 高置信度预测分析（概率>0.7的预测）
            high_conf_mask = val_pred_proba[:, 1] > 0.7
            high_conf_positive_pred = val_pred[high_conf_mask] == success_encoded
            if high_conf_positive_pred.sum() > 0:
                high_conf_true_positive = ((y_val_encoded[high_conf_mask] == success_encoded) &
                                         (val_pred[high_conf_mask] == success_encoded)).sum()
                high_conf_precision = high_conf_true_positive / high_conf_positive_pred.sum()
            else:
                high_conf_precision = 0.0
                high_conf_true_positive = 0

            # 超高置信度预测分析（概率>0.8的预测）
            ultra_high_conf_mask = val_pred_proba[:, 1] > 0.8
            ultra_high_conf_positive_pred = val_pred[ultra_high_conf_mask] == success_encoded
            if ultra_high_conf_positive_pred.sum() > 0:
                ultra_high_conf_true_positive = ((y_val_encoded[ultra_high_conf_mask] == success_encoded) &
                                                (val_pred[ultra_high_conf_mask] == success_encoded)).sum()
                ultra_high_conf_precision = ultra_high_conf_true_positive / ultra_high_conf_positive_pred.sum()
            else:
                ultra_high_conf_precision = 0.0
                ultra_high_conf_true_positive = 0

            # 保存结果
            self.results[model_name] = {
                'model': model,
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'auc_score': auc_score,
                'f1_score': f1,
                'success_precision': success_precision,  # 最重要：正样本精确率
                'success_recall': success_recall,
                'success_f1': success_f1,
                'actual_positive': actual_positive,
                'predicted_positive': predicted_positive,
                'true_positive': true_positive,
                'high_conf_precision': high_conf_precision,  # 高置信度精确率
                'ultra_high_conf_precision': ultra_high_conf_precision,  # 超高置信度精确率
                'high_conf_count': high_conf_positive_pred.sum() if 'high_conf_positive_pred' in locals() else 0,
                'ultra_high_conf_count': ultra_high_conf_positive_pred.sum() if 'ultra_high_conf_positive_pred' in locals() else 0,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'y_val_true': y_val_encoded,
                'y_val_pred': val_pred,
                'y_val_pred_proba': val_pred_proba[:, 1] if val_pred_proba.shape[1] > 1 else val_pred_proba[:, 0]
            }
            
            self.models[model_name] = model
            
            print(f"      训练准确率: {train_accuracy:.4f}")
            print(f"      验证准确率: {val_accuracy:.4f}")
            print(f"      🎯 正样本精确率: {success_precision:.4f} ({true_positive}/{predicted_positive})")
            print(f"      正样本召回率: {success_recall:.4f} ({true_positive}/{actual_positive})")
            print(f"      高置信度(>70%)精确率: {high_conf_precision:.4f} ({self.results[model_name]['high_conf_count']}个预测)")
            print(f"      超高置信度(>80%)精确率: {ultra_high_conf_precision:.4f} ({self.results[model_name]['ultra_high_conf_count']}个预测)")
            print(f"      AUC得分: {auc_score:.4f}")
            print(f"      交叉验证: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        return X_train_selected, X_val_selected, y_train_encoded, y_val_encoded

    def create_visualizations(self):
        """创建可视化图表 - 基于原success_prediction_system.py的visualize_results方法"""
        print("\n📈 生成可视化图表")
        print("=" * 60)

        # 获取可视化配置
        viz_config = self.config['visualization_config']
        fig_size = viz_config['figure_size']
        colors = viz_config['colors']['primary']

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=fig_size)
        fig.suptitle('3日成功选股预测模型训练结果', fontsize=16, fontweight='bold')

        # 1. 模型准确率对比
        ax1 = axes[0, 0]
        models = list(self.results.keys())
        train_accs = [self.results[m]['train_accuracy'] for m in models]
        val_accs = [self.results[m]['val_accuracy'] for m in models]

        x = np.arange(len(models))
        width = 0.35

        bars1 = ax1.bar(x - width/2, train_accs, width, label='训练准确率', alpha=0.8, color='skyblue')
        bars2 = ax1.bar(x + width/2, val_accs, width, label='验证准确率', alpha=0.8, color='lightcoral')

        ax1.set_xlabel('模型')
        ax1.set_ylabel('准确率')
        ax1.set_title('模型准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)

        # 2. AUC得分对比
        ax2 = axes[0, 1]
        auc_scores = [self.results[m]['auc_score'] for m in models]
        bars = ax2.bar(models, auc_scores, alpha=0.8, color='lightgreen')
        ax2.set_xlabel('模型')
        ax2.set_ylabel('AUC得分')
        ax2.set_title('AUC得分对比')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)

        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # 3. F1得分对比
        ax3 = axes[0, 2]
        f1_scores = [self.results[m]['f1_score'] for m in models]
        bars = ax3.bar(models, f1_scores, alpha=0.8, color='gold')
        ax3.set_xlabel('模型')
        ax3.set_ylabel('F1得分')
        ax3.set_title('F1得分对比')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)

        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # 4. ROC曲线
        ax4 = axes[1, 0]

        for i, model_name in enumerate(models):
            if self.results[model_name]['auc_score'] > 0:
                y_true = self.results[model_name]['y_val_true']
                y_pred_proba = self.results[model_name]['y_val_pred_proba']

                fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
                auc = self.results[model_name]['auc_score']

                ax4.plot(fpr, tpr, color=colors[i], lw=2,
                        label=f'{model_name} (AUC = {auc:.3f})')

        ax4.plot([0, 1], [0, 1], color='gray', lw=2, linestyle='--', alpha=0.5)
        ax4.set_xlim([0.0, 1.0])
        ax4.set_ylim([0.0, 1.05])
        ax4.set_xlabel('假正率')
        ax4.set_ylabel('真正率')
        ax4.set_title('ROC曲线')
        ax4.legend(loc="lower right")
        ax4.grid(True, alpha=0.3)

        # 5. 混淆矩阵（最佳模型）
        best_model = max(models, key=lambda m: self.results[m]['val_accuracy'])
        ax5 = axes[1, 1]

        y_true = self.results[best_model]['y_val_true']
        y_pred = self.results[best_model]['y_val_pred']
        cm = confusion_matrix(y_true, y_pred)

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax5,
                   xticklabels=['失败', '成功'], yticklabels=['失败', '成功'])
        ax5.set_xlabel('预测标签')
        ax5.set_ylabel('真实标签')
        ax5.set_title(f'混淆矩阵 ({best_model})')

        # 6. 特征重要性（随机森林）
        ax6 = axes[1, 2]
        if 'RandomForest' in self.models:
            rf_model = self.models['RandomForest']
            importances = rf_model.feature_importances_

            # 获取前10个重要特征
            indices = np.argsort(importances)[::-1][:10]

            ax6.barh(range(10), importances[indices], alpha=0.8, color='purple')
            ax6.set_yticks(range(10))
            ax6.set_yticklabels([self.feature_names[i] for i in indices])
            ax6.set_xlabel('重要性')
            ax6.set_title('特征重要性 (随机森林)')
            ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_path = f"{self.training_folder}/visualizations/training_results.png"
        plt.savefig(chart_path, dpi=viz_config['dpi'], bbox_inches='tight')
        plt.close()  # 关闭图表而不显示

        print(f"   ✅ 图表已保存: {chart_path}")
        return chart_path

    def save_models_and_results(self, train_file, val_file):
        """保存模型和结果 - 基于原success_prediction_system.py的save_models_and_results方法"""
        print("\n💾 保存模型和结果")
        print("=" * 60)

        # 保存模型
        for model_name, model in self.models.items():
            model_path = f"{self.training_folder}/models/{model_name}_model.pkl"
            joblib.dump(model, model_path)
            print(f"   ✅ 模型已保存: {model_name}")

        # 保存预处理器
        joblib.dump(self.scaler, f"{self.training_folder}/models/scaler.pkl")
        joblib.dump(self.feature_selector, f"{self.training_folder}/models/feature_selector.pkl")
        joblib.dump(self.label_encoder, f"{self.training_folder}/models/label_encoder.pkl")

        # 保存特征名称
        with open(f"{self.training_folder}/models/feature_names.json", 'w', encoding='utf-8') as f:
            json.dump(self.feature_names, f, ensure_ascii=False, indent=2)

        print(f"   ✅ 预处理器已保存")

        # 保存训练时的配置快照
        config_snapshot = self.config.copy()
        config_snapshot['training_info'] = {
            'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
            'train_file': train_file,
            'validation_file': val_file,
            'selected_features': self.feature_names,
            'feature_count': len(self.feature_names)
        }

        with open(f"{self.training_folder}/config.json", 'w', encoding='utf-8') as f:
            json.dump(config_snapshot, f, ensure_ascii=False, indent=2)

        # 保存性能结果
        performance_results = {}
        for model_name, results in self.results.items():
            performance_results[model_name] = {
                'train_accuracy': float(results['train_accuracy']),
                'val_accuracy': float(results['val_accuracy']),
                'auc_score': float(results['auc_score']),
                'f1_score': float(results['f1_score']),
                'success_precision': float(results['success_precision']),
                'success_recall': float(results['success_recall']),
                'success_f1': float(results['success_f1']),
                'cv_mean': float(results['cv_mean']),
                'cv_std': float(results['cv_std'])
            }

        with open(f"{self.training_folder}/reports/model_performance.json", 'w', encoding='utf-8') as f:
            json.dump(performance_results, f, ensure_ascii=False, indent=2)

        # 保存特征重要性
        feature_importance = {}
        for model_name, model in self.models.items():
            if hasattr(model, 'feature_importances_'):
                # 获取特征重要性
                importances = model.feature_importances_
                feature_names = self.feature_names

                # 创建特征重要性排序
                importance_data = []
                for i, importance in enumerate(importances):
                    importance_data.append({
                        'feature': feature_names[i],
                        'importance': float(importance),
                        'rank': 0  # 稍后设置排名
                    })

                # 按重要性排序
                importance_data.sort(key=lambda x: x['importance'], reverse=True)

                # 设置排名
                for rank, item in enumerate(importance_data, 1):
                    item['rank'] = rank

                feature_importance[model_name] = importance_data
                print(f"   ✅ {model_name} 特征重要性已计算 (前5个特征)")
                for i, item in enumerate(importance_data[:5]):
                    print(f"      {i+1}. {item['feature']}: {item['importance']:.4f}")

        with open(f"{self.training_folder}/reports/feature_importance.json", 'w', encoding='utf-8') as f:
            json.dump(feature_importance, f, ensure_ascii=False, indent=2)

        print(f"   ✅ 配置、性能结果和特征重要性已保存")

        # 对验证数据进行预测并保存结果
        self.save_validation_predictions(val_file)

        return self.training_folder

    def save_validation_predictions(self, val_file):
        """对验证数据进行预测并保存结果"""
        print(f"\n🔮 对验证数据进行预测")
        print("=" * 60)

        try:
            # 加载验证数据
            X_val, val_df = self.load_and_preprocess_data(val_file, is_training=False)

            # 应用相同的预处理步骤
            # 1. 标准化
            X_val_scaled = self.scaler.transform(X_val)

            # 2. 特征选择 - 使用训练时保存的特征选择器
            X_val_selected = self.feature_selector.transform(X_val_scaled)

            print(f"   📊 验证数据预处理: {X_val.shape} -> {X_val_selected.shape}")

            # 尝试获取目标变量（如果存在）
            y_val = None
            if self.target_feature in val_df.columns:
                y_val = val_df[self.target_feature].copy()
                # 编码目标变量
                y_val_encoded = self.label_encoder.transform(y_val)

            # 对每个模型进行预测
            prediction_results = []

            for model_name, model in self.models.items():
                print(f"   🔄 使用 {model_name} 进行预测...")

                # 预测概率 - 使用特征选择后的数据
                val_pred_proba = model.predict_proba(X_val_selected)
                prob_positive = val_pred_proba[:, 1] if val_pred_proba.shape[1] > 1 else val_pred_proba[:, 0]

                # 选择概率最高的前10个作为成功预测
                top_n = 10
                top_indices = np.argsort(prob_positive)[-top_n:]  # 获取概率最高的前N个索引
                val_pred = np.zeros(len(prob_positive), dtype=int)
                val_pred[top_indices] = 1  # 将前N个设为成功预测

                final_threshold = prob_positive[top_indices[0]] if len(top_indices) > 0 else 0.5  # 最低的阈值

                # 解码预测结果
                pred_labels = self.label_encoder.inverse_transform(val_pred)

                print(f"      使用阈值: {final_threshold:.2f}")
                print(f"      预测成功: {(pred_labels == '成功').sum()} 个")

                # 为每个样本创建预测记录
                for i in range(len(X_val_selected)):
                    if pred_labels[i] == '成功':  # 只保存预测为成功的记录
                        # 获取实际结果
                        actual_result = 'N/A'
                        if y_val is not None and i < len(y_val):
                            actual_result = y_val.iloc[i] if hasattr(y_val, 'iloc') else y_val[i]

                        prediction_results.append({
                            'model': model_name,
                            'index': int(i),
                            'stock_code': str(val_df.iloc[i].get('股票代码', 'N/A')),
                            'stock_name': str(val_df.iloc[i].get('股票', 'N/A')),
                            'prediction': str(pred_labels[i]),
                            'confidence': float(prob_positive[i]),
                            'threshold_used': float(final_threshold),
                            'actual_result': str(actual_result)
                        })

            # 按置信度排序
            prediction_results.sort(key=lambda x: x['confidence'], reverse=True)

            # 保存预测结果
            with open(f"{self.training_folder}/reports/validation_predictions.json", 'w', encoding='utf-8') as f:
                json.dump(prediction_results, f, ensure_ascii=False, indent=2)

            print(f"   ✅ 验证预测结果已保存: {len(prediction_results)} 个成功预测")

            # 显示前5个高置信度预测
            if prediction_results:
                print(f"   🎯 前5个高置信度预测:")
                for i, pred in enumerate(prediction_results[:5], 1):
                    print(f"      {i}. {pred['stock_name']} ({pred['model']}): {pred['confidence']:.3f}")

        except Exception as e:
            print(f"   ❌ 验证预测失败: {e}")
            import traceback
            traceback.print_exc()

    def generate_training_report(self, train_file, val_file):
        """生成训练报告 - 基于原success_prediction_system.py的generate_report方法"""
        print("\n📋 生成训练报告")
        print("=" * 60)

        # 找到最佳模型
        best_model = max(self.results.keys(), key=lambda m: self.results[m]['val_accuracy'])
        best_performance = self.results[best_model]

        # 生成HTML报告
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>3日成功选股预测模型训练报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metric {{ background-color: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .best {{ background-color: #d4edda; color: #155724; font-weight: bold; }}
                .good {{ background-color: #d1ecf1; color: #0c5460; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎯 3日成功选股预测模型训练报告</h1>
                <p><strong>训练时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>训练数据:</strong> {os.path.basename(train_file)}</p>
                <p><strong>验证数据:</strong> {os.path.basename(val_file)}</p>
                <p><strong>目标变量:</strong> {self.target_feature}</p>
            </div>

            <div class="section">
                <h2>📊 成功判断标准</h2>
                <div class="metric">
                    <strong>3日成功选股标准:</strong><br>
                    {self.config['data_config']['success_criteria']['description']}<br>
                    <em>注: 直接使用数据集中已有的"{self.target_feature}"列</em>
                </div>
            </div>

            <div class="section">
                <h2>🏆 最佳模型表现</h2>
                <div class="metric best">
                    <strong>最佳模型:</strong> {best_model}<br>
                    <strong>验证准确率:</strong> {best_performance['val_accuracy']:.4f}<br>
                    <strong>AUC得分:</strong> {best_performance['auc_score']:.4f}<br>
                    <strong>F1得分:</strong> {best_performance['f1_score']:.4f}
                </div>
            </div>

            <div class="section">
                <h2>📈 所有模型性能对比</h2>
                <table>
                    <tr>
                        <th>模型</th>
                        <th>训练准确率</th>
                        <th>验证准确率</th>
                        <th>AUC得分</th>
                        <th>F1得分</th>
                        <th>交叉验证</th>
                    </tr>
        """

        # 添加模型性能表格
        for model_name, results in self.results.items():
            css_class = "best" if model_name == best_model else "good"
            html_report += f"""
                    <tr class="{css_class}">
                        <td>{model_name}</td>
                        <td>{results['train_accuracy']:.4f}</td>
                        <td>{results['val_accuracy']:.4f}</td>
                        <td>{results['auc_score']:.4f}</td>
                        <td>{results['f1_score']:.4f}</td>
                        <td>{results['cv_mean']:.4f} ± {results['cv_std']:.4f}</td>
                    </tr>
            """

        html_report += """
                </table>
            </div>

            <div class="section">
                <h2>🎯 特征重要性排名</h2>
                <p>以下是各模型的特征重要性排名，显示哪些特征对成功选股最有影响:</p>
        """

        # 添加特征重要性表格
        try:
            with open(f"{self.training_folder}/reports/feature_importance.json", 'r', encoding='utf-8') as f:
                feature_importance = json.load(f)

            for model_name, importance_data in feature_importance.items():
                html_report += f"""
                <h3>{model_name} 特征重要性 Top 10</h3>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr style="background-color: #f0f0f0;">
                        <th style="border: 1px solid #ddd; padding: 8px;">排名</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">特征名称</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">重要性得分</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">相对重要性</th>
                    </tr>
                """

                # 显示前10个最重要的特征
                max_importance = importance_data[0]['importance'] if importance_data else 1
                for item in importance_data[:10]:
                    relative_importance = (item['importance'] / max_importance) * 100
                    html_report += f"""
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{item['rank']}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{item['feature']}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{item['importance']:.4f}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{relative_importance:.1f}%</td>
                    </tr>
                    """

                html_report += "</table>"

        except Exception as e:
            html_report += f"<p>特征重要性数据加载失败: {e}</p>"

        html_report += """
            </div>

            <div class="section">
                <h2>🔮 验证数据预测结果</h2>
                <p>以下是对验证数据的预测结果，按置信度从高到低排序:</p>
        """

        # 添加验证预测结果
        try:
            with open(f"{self.training_folder}/reports/validation_predictions.json", 'r', encoding='utf-8') as f:
                validation_predictions = json.load(f)

            if validation_predictions:
                html_report += """
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr style="background-color: #f0f0f0;">
                        <th style="border: 1px solid #ddd; padding: 8px;">排名</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">股票代码</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">股票名称</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">模型</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">置信度</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">预测结果</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">实际结果</th>
                    </tr>
                """

                for i, pred in enumerate(validation_predictions, 1):
                    # 根据预测准确性设置行颜色
                    row_class = ""
                    if pred['actual_result'] == '成功':
                        row_class = 'style="background-color: #d4edda;"'  # 绿色 - 预测正确
                    elif pred['actual_result'] == '失败':
                        row_class = 'style="background-color: #f8d7da;"'  # 红色 - 预测错误

                    html_report += f"""
                    <tr {row_class}>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{i}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{pred['stock_code']}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{pred['stock_name']}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{pred['model']}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{pred['confidence']:.3f}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">✅ 成功</td>
                        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{pred['actual_result']}</td>
                    </tr>
                    """

                html_report += "</table>"

                # 添加预测统计
                total_predictions = len(validation_predictions)
                correct_predictions = sum(1 for pred in validation_predictions if pred['actual_result'] == '成功')
                accuracy = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0

                html_report += f"""
                <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <h4>📊 预测统计</h4>
                    <p><strong>总预测数:</strong> {total_predictions}</p>
                    <p><strong>预测正确数:</strong> {correct_predictions}</p>
                    <p><strong>预测精确率:</strong> {accuracy:.1f}%</p>
                </div>
                """
            else:
                html_report += "<p>没有找到预测为成功的股票。</p>"

        except Exception as e:
            html_report += f"<p>验证预测数据加载失败: {e}</p>"

        html_report += f"""
            </div>

            <div class="section">
                <h2>📁 输出文件</h2>
                <ul>
                    <li><strong>模型文件:</strong> models/ 目录下的 .pkl 文件</li>
                    <li><strong>可视化图表:</strong> visualizations/training_results.png</li>
                    <li><strong>性能数据:</strong> reports/model_performance.json</li>
                    <li><strong>训练配置:</strong> config.json</li>
                </ul>
            </div>

            <div class="section">
                <h2>💡 使用建议</h2>
                <ul>
                    <li>使用最佳模型 <strong>{best_model}</strong> 进行预测</li>
                    <li>关注验证准确率 <strong>{best_performance['val_accuracy']:.1%}</strong> 的表现</li>
                    <li>重点关注预测概率 > 0.7 的股票</li>
                    <li>结合其他分析方法进行综合判断</li>
                </ul>
            </div>

            <div class="section">
                <h2>🚀 下一步操作</h2>
                <p>使用以下命令进行预测:</p>
                <code>python predict_3d_success.py --input_file 新数据.xlsx --training_folder {self.training_folder}</code>
            </div>
        </body>
        </html>
        """

        # 保存HTML报告
        report_path = f"{self.training_folder}/reports/training_report.html"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_report)

        print(f"   ✅ HTML报告已保存: {report_path}")
        return report_path


def run_statistical_training(config_path='config_statistical.json'):
    """运行统计学习训练"""
    print("🔬 统计学习方法训练")
    print("=" * 80)

    # 初始化统计学习训练器
    trainer = StatisticalLearningTrainer(config_path)

    # 获取数据文件路径
    train_file = os.path.join(trainer.config['data_config']['data_folder'],
                             trainer.config['data_config']['train_file'])
    val_file = os.path.join(trainer.config['data_config']['data_folder'],
                           trainer.config['data_config']['validation_file'])

    print(f"📚 训练数据: {trainer.config['data_config']['train_file']}")
    print(f"🔍 验证数据: {trainer.config['data_config']['validation_file']}")
    print("=" * 80)

    # 1. 加载和预处理数据
    print("\n📊 数据加载和预处理")
    X_train, y_train, train_df = trainer.load_and_preprocess_data(train_file, is_training=True)
    X_val, y_val, val_df = trainer.load_and_preprocess_data(val_file, is_training=True)

    # 2. 统计学分析
    statistical_results = trainer.statistical_analysis(X_train, y_train)

    # 3. 生成统计规则
    rules, significant_features = trainer.generate_statistical_rules(X_train, y_train)

    # 4. 评估模型
    evaluation_results = trainer.evaluate_statistical_model(X_val, y_val)

    # 5. 保存模型
    output_folder = trainer.save_statistical_model(train_file, val_file)

    print("\n" + "=" * 80)
    print("🎉 统计学习训练完成！")
    print("=" * 80)
    print(f"📊 训练数据: {len(X_train)} 条记录")
    print(f"🔍 验证数据: {len(X_val)} 条记录")
    print(f"🎯 显著特征: {len(significant_features)} 个")
    print(f"📋 统计规则: {len(rules)} 个")
    print(f"📈 验证准确率: {evaluation_results['accuracy']:.4f}")
    print(f"🎯 成功预测精确率: {evaluation_results['success_precision']:.4f}")
    print(f"📁 输出文件夹: {output_folder}")

    return output_folder, evaluation_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3日成功选股预测模型训练')
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    parser.add_argument('--output_dir', default=None, help='输出目录 (可选)')
    parser.add_argument('--method', choices=['ml', 'statistical', 'both'], default='ml',
                       help='训练方法: ml=机器学习, statistical=统计学习, both=两种方法')

    args = parser.parse_args()

    print("🎯 3日成功选股预测模型训练")
    print("=" * 80)

    try:
        # 检查训练方法
        if args.method in ['statistical', 'both']:
            print("\n🔬 统计学习方法训练")
            print("=" * 80)
            stat_output, stat_results = run_statistical_training('config_statistical.json')
            print(f"✅ 统计学习完成，输出文件夹: {stat_output}")

        if args.method in ['ml', 'both']:
            print("\n🤖 机器学习方法训练")
            print("=" * 80)

            # 创建训练器
            trainer = ThreeDaySuccessTrainer(args.config)

            # 如果指定了输出目录，更新配置
            if args.output_dir:
                trainer.config['output_config']['models_dir'] = args.output_dir

            # 获取数据文件路径
            train_file = trainer.config['data_config']['train_file']
            val_file = trainer.config['data_config']['validation_file']

            print(f"📚 训练数据: {os.path.basename(train_file)}")
            print(f"🔍 验证数据: {os.path.basename(val_file)}")
            print("=" * 80)

            # 创建输出文件夹
            training_folder = trainer.create_training_folder()

            # 1. 加载和预处理数据
            print("\n📊 数据加载和预处理")
            X_train, y_train, train_df = trainer.load_and_preprocess_data(train_file, is_training=True)
            X_val, y_val, val_df = trainer.load_and_preprocess_data(val_file, is_training=True)

            # 2. 训练模型
            X_train_processed, X_val_processed, y_train_encoded, y_val_encoded = trainer.train_models(
                X_train, y_train, X_val, y_val
            )

            # 3. 生成可视化
            chart_path = trainer.create_visualizations()

            # 4. 保存模型和结果
            output_folder = trainer.save_models_and_results(train_file, val_file)

            # 5. 生成训练报告
            report_path = trainer.generate_training_report(train_file, val_file)

            # 6. 打印总结
            print("\n" + "=" * 80)
            print("🎉 机器学习训练完成！")
            print("=" * 80)

            print(f"📊 训练数据: {len(X_train)} 条记录")
            print(f"🔍 验证数据: {len(X_val)} 条记录")
            print(f"🎯 选择特征: {len(trainer.feature_names)} 个")

            # 显示最佳模型 - 优先考虑正样本精确率
            best_model_by_precision = max(trainer.results.keys(), key=lambda m: trainer.results[m]['success_precision'])
            best_model_by_high_conf = max(trainer.results.keys(), key=lambda m: trainer.results[m]['high_conf_precision'])
            best_model_by_accuracy = max(trainer.results.keys(), key=lambda m: trainer.results[m]['val_accuracy'])

            best_precision = trainer.results[best_model_by_precision]['success_precision']
            best_high_conf = trainer.results[best_model_by_high_conf]['high_conf_precision']
            best_acc = trainer.results[best_model_by_accuracy]['val_accuracy']

            print(f"🎯 最佳模型(正样本精确率): {best_model_by_precision} (精确率: {best_precision:.4f}) (召回率: {trainer.results[best_model_by_precision]['success_recall']:.4f})")
            if best_model_by_high_conf != best_model_by_precision:
                print(f"🔥 最佳模型(高置信度精确率): {best_model_by_high_conf} (高置信度精确率: {best_high_conf:.4f})")
            if best_model_by_accuracy != best_model_by_precision:
                print(f"🏆 最佳模型(验证准确率): {best_model_by_accuracy} (验证准确率: {best_acc:.4f}) (精确率: {trainer.results[best_model_by_accuracy]['success_precision']:.4f})")

            print(f"\n📁 输出文件夹: {output_folder}")
            print(f"📊 可视化图表: {chart_path}")
            print(f"📋 训练报告: {report_path}")

            print(f"\n🚀 使用预测脚本:")
            print(f"python predict_3d_success.py --input_file 新数据.xlsx --training_folder {output_folder}")

        print("\n" + "=" * 80)
        print("🎉 训练完成！")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
