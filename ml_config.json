{"data": {"input_file": "选股分析结果/选股分析结果_20250730_225530.xlsx", "target_column": "5日成功选股", "stock_column": "股票", "feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"]}, "data_split": {"test_size": 0.2, "val_size": 0.2, "random_state": 42, "ensure_stock_separation": true}, "models": {"PrecisionMLP": {"hidden_dim": 512, "dropout_rate": 0.25, "threshold": 0.5}, "ResidualNet": {"hidden_dim": 512, "dropout_rate": 0.25, "threshold": 0.5}, "AttentionNet": {"hidden_dim": 512, "dropout_rate": 0.25, "threshold": 0.5}, "EnsembleModel": {"hidden_dim": 512, "dropout_rate": 0.25, "threshold": 0.5}}, "training": {"epochs": 300, "batch_size": 16, "learning_rate": 0.0005, "weight_decay": 5e-05, "early_stopping_patience": 50, "lr_scheduler_patience": 15, "lr_scheduler_factor": 0.3}, "dual_loss": {"precision_weight": 3.0, "proportion_weight": 0.2, "target_proportion": 0.08}, "output": {"save_models": true, "generate_plots": true, "generate_html_report": true, "plot_filename": "dual_loss_analysis.png", "report_filename": "dual_loss_report.html"}}