#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D-E涨跌幅 + E点成交量/D点成交量 综合选股器
结合价格变化和成交量变化进行精准选股
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from analyze_de_change_success import MultiConditionAnalyzer
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class CombinedDEVolumeSelector:
    """D-E涨跌幅 + 成交量综合选股器"""
    
    def __init__(self):
        self.analyzer = MultiConditionAnalyzer()
        
        # 定义保留的优质组合（基于历史数据分析结果）
        self.selected_combinations = [
            {
                "price_range": "大幅下跌",
                "volume_range": "明显缩量",
                "price_min": -20, "price_max": -10,
                "volume_min": 0.4, "volume_max": 0.6,
                "expected_success_rate": 0.161,
                "expected_avg_gain": 0.148
            },
            {
                "price_range": "大幅下跌",
                "volume_range": "适度缩量",
                "price_min": -20, "price_max": -10,
                "volume_min": 0.6, "volume_max": 0.8,
                "expected_success_rate": 0.294,
                "expected_avg_gain": 0.141
            },
            {
                "price_range": "中度下跌",
                "volume_range": "严重缩量",
                "price_min": -10, "price_max": -5,
                "volume_min": 0.0, "volume_max": 0.4,
                "expected_success_rate": 0.154,
                "expected_avg_gain": 0.157
            },
            {
                "price_range": "中度下跌",
                "volume_range": "明显缩量",
                "price_min": -10, "price_max": -5,
                "volume_min": 0.4, "volume_max": 0.6,
                "expected_success_rate": 0.173,
                "expected_avg_gain": 0.169
            },
            {
                "price_range": "中度下跌",
                "volume_range": "适度缩量",
                "price_min": -10, "price_max": -5,
                "volume_min": 0.6, "volume_max": 0.8,
                "expected_success_rate": 0.166,
                "expected_avg_gain": 0.184
            }
        ]
        

    
    def analyze_combined_conditions(self, data_file: str):
        """分析D-E涨跌幅和成交量的组合条件"""
        print("🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股分析")
        print("=" * 80)
        
        # 加载数据
        df = self.analyzer.load_data(data_file)
        
        # 检查必要列
        required_cols = ['D-E涨跌幅', 'E点成交量/D点成交量', '5日成功选股']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺失必要列: {missing_cols}")
            return None
        
        # 预处理数据
        price_change = pd.to_numeric(df['D-E涨跌幅'], errors='coerce')
        volume_ratio = pd.to_numeric(df['E点成交量/D点成交量'], errors='coerce')
        
        valid_mask = pd.notna(price_change) & pd.notna(volume_ratio)
        valid_df = df[valid_mask].copy()
        valid_price = price_change[valid_mask]
        valid_volume = volume_ratio[valid_mask]
        
        print(f"✅ 有效数据: {len(valid_df)} 条")
        print(f"📊 数据概览:")
        print(f"   D-E涨跌幅: {valid_price.mean():.2f}% ± {valid_price.std():.2f}%")
        print(f"   成交量比率: {valid_volume.mean():.3f} ± {valid_volume.std():.3f}")
        
        # 分析选定的优质组合
        combination_results = self.analyze_selected_combinations(valid_df, valid_price, valid_volume)
        
        # 显示最佳组合的推荐股票
        if combination_results:
            best_combo = combination_results[0]
            self.show_recommended_stocks(best_combo['subset_df'],
                                       f"{best_combo['price_range']}+{best_combo['volume_range']}")

        strategy_results = []  # 保持兼容性
        
        # 生成可视化
        self.create_combination_charts(valid_df, valid_price, valid_volume, combination_results)
        
        return combination_results, strategy_results
    
    def analyze_selected_combinations(self, df: pd.DataFrame, price_change: pd.Series, volume_ratio: pd.Series):
        """分析选定的优质价格-成交量组合"""
        print(f"\n📊 优质组合分析 (基于历史数据筛选):")
        print("-" * 100)
        print(f"{'价格区间':<12} {'成交量区间':<12} {'数量':<6} {'成功':<6} {'成功率':<8} {'平均涨幅':<10} {'综合评分':<8}")
        print("-" * 100)

        results = []

        for combo in self.selected_combinations:
            # 筛选符合条件的数据
            price_mask = (price_change >= combo['price_min']) & (price_change < combo['price_max'])
            volume_mask = (volume_ratio >= combo['volume_min']) & (volume_ratio < combo['volume_max'])
            combined_mask = price_mask & volume_mask

            subset = df[combined_mask]

            if len(subset) > 0:
                success_mask = subset['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(subset)
                success_rate = success_count / total_count if total_count > 0 else 0

                # 计算平均涨幅
                avg_gain = 0
                if success_count > 0 and '5日最大涨幅' in subset.columns:
                    success_cases = subset[success_mask]
                    actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                    if len(actual_gains) > 0:
                        avg_gain = actual_gains.mean()

                # 计算综合评分 (成功率 * 0.7 + 样本量权重 * 0.3)
                sample_weight = min(total_count / 50, 1.0)
                composite_score = success_rate * 0.7 + sample_weight * 0.3

                print(f"{combo['price_range']:<12} {combo['volume_range']:<12} {total_count:<6} {success_count:<6} "
                      f"{success_rate:<8.1%} {avg_gain:<10.1%} {composite_score:<8.3f}")

                results.append({
                    'price_range': combo['price_range'],
                    'volume_range': combo['volume_range'],
                    'price_min': combo['price_min'],
                    'price_max': combo['price_max'],
                    'volume_min': combo['volume_min'],
                    'volume_max': combo['volume_max'],
                    'total_count': total_count,
                    'success_count': success_count,
                    'success_rate': success_rate,
                    'avg_gain': avg_gain,
                    'composite_score': composite_score,
                    'subset_df': subset,
                    'expected_success_rate': combo['expected_success_rate'],
                    'expected_avg_gain': combo['expected_avg_gain']
                })

        # 按综合评分排序
        results.sort(key=lambda x: x['composite_score'], reverse=True)

        print(f"\n🏆 当前数据表现排名:")
        for i, result in enumerate(results):
            expected_vs_actual = f"预期{result['expected_success_rate']:.1%}→实际{result['success_rate']:.1%}"
            print(f"   {i+1}. {result['price_range']} + {result['volume_range']}")
            print(f"      {expected_vs_actual}, 样本: {result['total_count']}, "
                  f"平均涨幅: {result['avg_gain']:.1%}, 评分: {result['composite_score']:.3f}")

        # 保存特定组合的数据到文件
        try:
            self.save_selected_combinations(results)
        except Exception as e:
            print(f"❌ 保存文件时出错: {str(e)}")

        return results

    def save_selected_combinations(self, results: list, output_file: str = "selected_combinations.txt"):
        """保存指定的组合数据到文件"""
        print(f"\n💾 保存选定组合数据到文件: {output_file}")

        # 定义要保存的组合
        target_combinations = [
            ("大幅下跌", "明显缩量"),
            ("大幅下跌", "适度缩量"),
            ("中度下跌", "严重缩量"),
            ("中度下跌", "明显缩量"),
            ("中度下跌", "适度缩量")
        ]

        # 筛选匹配的结果
        selected_results = []
        for result in results:
            combo_key = (result['price_range'], result['volume_range'])
            if combo_key in target_combinations:
                selected_results.append(result)

        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("D-E涨跌幅 + E点成交量/D点成交量 选定组合分析结果\n")
            f.write("=" * 80 + "\n\n")
            f.write("生成时间: " + pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")

            # 写入表头
            f.write(f"{'价格区间':<15} {'成交量区间':<15} {'数量':<6} {'成功':<6} {'成功率':<8} {'平均涨幅':<10} {'综合评分':<8}\n")
            f.write("-" * 80 + "\n")

            # 写入数据行
            for result in selected_results:
                f.write(f"{result['price_range']:<15} {result['volume_range']:<15} "
                       f"{result['total_count']:<6} {result['success_count']:<6} "
                       f"{result['success_rate']:<8.1%} {result['avg_gain']:<10.1%} "
                       f"{result['composite_score']:<8.3f}\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write("说明:\n")
            f.write("- 价格区间: D-E点价格变化幅度\n")
            f.write("- 成交量区间: E点成交量相对D点成交量的变化\n")
            f.write("- 成功率: 5日最大涨幅≥预设阈值的比例\n")
            f.write("- 平均涨幅: 成功案例的平均涨幅\n")
            f.write("- 综合评分: 成功率*0.7 + 样本量权重*0.3\n")

        print(f"✅ 已保存 {len(selected_results)} 个组合的数据到: {output_file}")
        return selected_results

    def show_recommended_stocks(self, subset_df: pd.DataFrame, strategy_name: str):
        """显示推荐的股票"""
        # print(f"\n📋 {strategy_name} - 推荐股票 (前10只):")
        # print("-" * 80)
        
        display_cols = ['股票', '买入日期', 'D-E涨跌幅', 'E点成交量/D点成交量', '5日成功选股', '5日最大涨幅']
        available_cols = [col for col in display_cols if col in subset_df.columns]
        
        # 按成功概率排序（成功的在前）
        sorted_df = subset_df.sort_values('5日成功选股', ascending=False)
        
        for i, (_, row) in enumerate(sorted_df.head(10).iterrows()):
            stock_info = []
            for col in available_cols:
                value = row.get(col, 'N/A')
                if col in ['D-E涨跌幅']:
                    try:
                        value = f"{float(value):.1f}%"
                    except:
                        pass
                elif col in ['5日最大涨幅']:
                    try:
                        value = f"{float(value*100):.3f}%"
                    except:
                        pass
                elif col == 'E点成交量/D点成交量':
                    try:
                        value = f"{float(value):.3f}"
                    except:
                        pass
                stock_info.append(f"{col}: {value}")
            
            # status = "✅" if row.get('5日成功选股') == "成功" else "❌"
            # print(f"   {i+1}. {status} {' | '.join(stock_info)}")
    
    def create_combination_charts(self, df: pd.DataFrame, price_change: pd.Series,
                                volume_ratio: pd.Series, combination_results: list):
        """创建组合分析图表"""
        print(f"\n🎨 生成组合分析图表...")

        # 设置matplotlib支持中文显示
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Price-Volume Combination Analysis', fontsize=16, fontweight='bold')
        
        # 1. 价格-成交量散点图
        ax1 = axes[0, 0]
        success_mask = df['5日成功选股'] == "成功"

        # 成功案例
        ax1.scatter(volume_ratio[success_mask], price_change[success_mask],
                   alpha=0.7, color='green', label='Success Cases', s=30)

        # 失败案例
        ax1.scatter(volume_ratio[~success_mask], price_change[~success_mask],
                   alpha=0.4, color='red', label='Failure Cases', s=20)

        ax1.set_title('Price Change vs Volume Ratio', fontweight='bold')
        ax1.set_xlabel('Volume Ratio (E/D)')
        ax1.set_ylabel('Price Change (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(0, color='black', linestyle='-', alpha=0.3)
        ax1.axvline(1.0, color='black', linestyle='-', alpha=0.3)
        
        # 2. 组合成功率柱状图
        ax2 = axes[0, 1]
        if combination_results:
            # 使用英文标签
            combo_labels = []
            success_rates = []
            sample_counts = []

            # 价格区间英文映射
            price_map = {
                "大幅下跌": "Large Decline",
                "中度下跌": "Moderate Decline",
                "小幅下跌": "Small Decline"
            }

            # 成交量区间英文映射
            volume_map = {
                "严重缩量": "Severe Shrink",
                "明显缩量": "Clear Shrink",
                "适度缩量": "Moderate Shrink"
            }

            for result in combination_results:
                price_en = price_map.get(result['price_range'], result['price_range'])
                volume_en = volume_map.get(result['volume_range'], result['volume_range'])
                combo_labels.append(f"{price_en}\n+{volume_en}")
                success_rates.append(result['success_rate'] * 100)
                sample_counts.append(result['total_count'])

            bars = ax2.bar(range(len(combo_labels)), success_rates,
                          color=['red' if x < 15 else 'orange' if x < 17 else 'green' for x in success_rates])
            ax2.set_title('Success Rate by Combination', fontweight='bold')
            ax2.set_xlabel('Price + Volume Combination')
            ax2.set_ylabel('Success Rate (%)')
            ax2.set_xticks(range(len(combo_labels)))
            ax2.set_xticklabels(combo_labels, rotation=45, ha='right', fontsize=9)

            # 添加数值标签
            for bar, count in zip(bars, sample_counts):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{height:.1f}%\n(n={count})', ha='center', va='bottom', fontsize=8)
        
        # 3. 平均涨幅对比图
        ax3 = axes[1, 0]
        if combination_results:
            # 使用英文标签
            combo_labels = []
            avg_gains = []

            for result in combination_results:
                price_en = price_map.get(result['price_range'], result['price_range'])
                volume_en = volume_map.get(result['volume_range'], result['volume_range'])
                combo_labels.append(f"{price_en}\n+{volume_en}")
                avg_gains.append(result['avg_gain'] * 100)

            bars = ax3.bar(range(len(combo_labels)), avg_gains,
                          color=['lightcoral' if x < 15 else 'gold' if x < 17 else 'lightgreen' for x in avg_gains])
            ax3.set_title('Average Gain by Combination', fontweight='bold')
            ax3.set_xlabel('Price + Volume Combination')
            ax3.set_ylabel('Average Gain (%)')
            ax3.set_xticks(range(len(combo_labels)))
            ax3.set_xticklabels(combo_labels, rotation=45, ha='right', fontsize=9)

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                        f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
        
        # 4. 综合评分对比图
        ax4 = axes[1, 1]
        if combination_results:
            # 使用英文标签
            combo_labels = []
            scores = []

            for result in combination_results:
                price_en = price_map.get(result['price_range'], result['price_range'])
                volume_en = volume_map.get(result['volume_range'], result['volume_range'])
                combo_labels.append(f"{price_en}\n+{volume_en}")
                scores.append(result['composite_score'])

            bars = ax4.bar(range(len(combo_labels)), scores,
                          color=['lightcoral' if x < 0.35 else 'gold' if x < 0.40 else 'lightgreen' for x in scores])
            ax4.set_title('Composite Score by Combination', fontweight='bold')
            ax4.set_xlabel('Price + Volume Combination')
            ax4.set_ylabel('Composite Score')
            ax4.set_xticks(range(len(combo_labels)))
            ax4.set_xticklabels(combo_labels, rotation=45, ha='right', fontsize=9)

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = "combined_de_volume_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {output_file}")
        
        plt.show()
    
    def generate_selection_report(self, data_file: str, output_file: str = "combined_selection_report.txt"):
        """生成综合选股报告"""
        print(f"\n📝 生成综合选股报告...")
        
        combination_results, strategy_results = self.analyze_combined_conditions(data_file)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("D-E涨跌幅 + E点成交量/D点成交量 综合选股报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("一、分析方法说明\n")
            f.write("-" * 30 + "\n")
            f.write("本报告结合D-E点价格变化和成交量变化进行综合选股分析\n")
            f.write("通过价格-成交量组合矩阵找出最优的选股条件\n\n")
            
            if combination_results:
                f.write("二、最佳组合分析结果\n")
                f.write("-" * 30 + "\n")
                for i, result in enumerate(combination_results[:5]):
                    f.write(f"{i+1}. {result['price_range']} + {result['volume_range']}\n")
                    f.write(f"   成功率: {result['success_rate']:.1%}\n")
                    f.write(f"   样本数: {result['total_count']}\n")
                    f.write(f"   平均涨幅: {result['avg_gain']:.1%}\n")
                    f.write(f"   综合评分: {result['composite_score']:.3f}\n\n")
            
            if strategy_results:
                f.write("三、预定义策略结果\n")
                f.write("-" * 30 + "\n")
                for strategy in strategy_results:
                    f.write(f"{strategy['name']}: {strategy['description']}\n")
                    f.write(f"   成功率: {strategy['success_rate']:.1%}\n")
                    f.write(f"   样本数: {strategy['total_count']}\n")
                    f.write(f"   平均涨幅: {strategy['avg_gain']:.1%}\n\n")
            
            f.write(f"报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 报告已保存: {output_file}")

def main():
    """主函数"""
    print("🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股器")
    print("=" * 60)
    
    # 创建选股器
    selector = CombinedDEVolumeSelector()
    
    # 数据文件
    data_file = "../../选股分析结果/选股分析结果_20250804_2025-01-01-2025-07-15.xlsx"

    # 执行综合分析
    selector.generate_selection_report(data_file)
    
    print(f"\n💡 综合选股要点:")
    print("1. 价格适度下跌 + 成交量适度缩量 = 最佳组合")
    print("2. 避免极端情况：大幅下跌+严重缩量，可能是死水")
    print("3. 关注突破候选：小幅上涨+适度缩量，可能是启动信号")
    print("4. 综合考虑成功率、样本量和平均涨幅")

if __name__ == "__main__":
    main()
