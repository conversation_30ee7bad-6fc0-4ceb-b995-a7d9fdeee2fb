#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D-E涨跌幅 + E点成交量/D点成交量 综合选股器
结合价格变化和成交量变化进行精准选股
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from analyze_de_change_success import MultiConditionAnalyzer
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class CombinedDEVolumeSelector:
    """D-E涨跌幅 + 成交量综合选股器"""
    
    def __init__(self):
        self.analyzer = MultiConditionAnalyzer()
        
        # 定义D-E涨跌幅区间
        self.price_ranges = [
            ("大幅下跌", -20, -10),
            ("中度下跌", -10, -5),
            ("小幅下跌", -5, -2),
            ("微幅下跌", -2, 0),
            ("基本持平", 0, 2),
            ("小幅上涨", 2, 5),
            ("中度上涨", 5, 10)
        ]
        
        # 定义成交量比率区间
        self.volume_ranges = [
            ("严重缩量", 0.0, 0.4),
            ("明显缩量", 0.4, 0.6),
            ("适度缩量", 0.6, 0.8),
            ("基本持平", 0.8, 1.2)
        ]
        
        # 预定义的优质组合策略
        self.premium_strategies = {
            "golden_combo": {
                "name": "黄金组合",
                "description": "中度下跌 + 适度缩量",
                "price_range": (-10, -5),
                "volume_range": (0.6, 0.8)
            },
            "oversold_opportunity": {
                "name": "超跌机会",
                "description": "大幅下跌 + 明显缩量",
                "price_range": (-20, -10),
                "volume_range": (0.4, 0.6)
            },
            "consolidation_setup": {
                "name": "整理蓄势",
                "description": "微幅下跌 + 适度缩量",
                "price_range": (-2, 0),
                "volume_range": (0.6, 0.8)
            },
            "breakout_candidate": {
                "name": "突破候选",
                "description": "小幅上涨 + 适度缩量",
                "price_range": (2, 5),
                "volume_range": (0.6, 0.8)
            },
            "value_hunting": {
                "name": "价值挖掘",
                "description": "小幅下跌 + 明显缩量",
                "price_range": (-5, -2),
                "volume_range": (0.4, 0.6)
            }
        }
    
    def analyze_combined_conditions(self, data_file: str):
        """分析D-E涨跌幅和成交量的组合条件"""
        print("🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股分析")
        print("=" * 80)
        
        # 加载数据
        df = self.analyzer.load_data(data_file)
        
        # 检查必要列
        required_cols = ['D-E涨跌幅', 'E点成交量/D点成交量', '5日成功选股']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺失必要列: {missing_cols}")
            return None
        
        # 预处理数据
        price_change = pd.to_numeric(df['D-E涨跌幅'], errors='coerce')
        volume_ratio = pd.to_numeric(df['E点成交量/D点成交量'], errors='coerce')
        
        valid_mask = pd.notna(price_change) & pd.notna(volume_ratio)
        valid_df = df[valid_mask].copy()
        valid_price = price_change[valid_mask]
        valid_volume = volume_ratio[valid_mask]
        
        print(f"✅ 有效数据: {len(valid_df)} 条")
        print(f"📊 数据概览:")
        print(f"   D-E涨跌幅: {valid_price.mean():.2f}% ± {valid_price.std():.2f}%")
        print(f"   成交量比率: {valid_volume.mean():.3f} ± {valid_volume.std():.3f}")
        
        # 分析所有组合
        combination_results = self.analyze_all_combinations(valid_df, valid_price, valid_volume)
        
        # 分析预定义策略
        strategy_results = self.analyze_premium_strategies(valid_df)
        
        # 生成可视化
        self.create_combination_charts(valid_df, valid_price, valid_volume, combination_results)
        
        return combination_results, strategy_results
    
    def analyze_all_combinations(self, df: pd.DataFrame, price_change: pd.Series, volume_ratio: pd.Series):
        """分析所有价格-成交量组合"""
        print(f"\n📊 价格-成交量组合分析:")
        print("-" * 100)
        print(f"{'价格区间':<12} {'成交量区间':<12} {'数量':<6} {'成功':<6} {'成功率':<8} {'平均涨幅':<10} {'综合评分':<8}")
        print("-" * 100)
        
        results = []
        
        for price_name, price_min, price_max in self.price_ranges:
            for volume_name, volume_min, volume_max in self.volume_ranges:
                # 筛选符合条件的数据
                price_mask = (price_change >= price_min) & (price_change < price_max)
                volume_mask = (volume_ratio >= volume_min) & (volume_ratio < volume_max)
                combined_mask = price_mask & volume_mask
                
                subset = df[combined_mask]
                
                if len(subset) >= 5:  # 至少5个样本
                    success_mask = subset['5日成功选股'] == "成功"
                    success_count = success_mask.sum()
                    total_count = len(subset)
                    success_rate = success_count / total_count
                    
                    # 计算平均涨幅
                    avg_gain = 0
                    if success_count > 0 and '5日最大涨幅' in subset.columns:
                        success_cases = subset[success_mask]
                        actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                        if len(actual_gains) > 0:
                            avg_gain = actual_gains.mean()
                    
                    # 计算综合评分 (成功率 * 0.7 + 样本量权重 * 0.3)
                    sample_weight = min(total_count / 50, 1.0)
                    composite_score = success_rate * 0.7 + sample_weight * 0.3
                    
                    print(f"{price_name:<12} {volume_name:<12} {total_count:<6} {success_count:<6} "
                          f"{success_rate:<8.1%} {avg_gain:<10.1%} {composite_score:<8.3f}")
                    
                    results.append({
                        'price_range': price_name,
                        'volume_range': volume_name,
                        'price_min': price_min,
                        'price_max': price_max,
                        'volume_min': volume_min,
                        'volume_max': volume_max,
                        'total_count': total_count,
                        'success_count': success_count,
                        'success_rate': success_rate,
                        'avg_gain': avg_gain,
                        'composite_score': composite_score,
                        'subset_df': subset
                    })
        
        # 按综合评分排序
        results.sort(key=lambda x: x['composite_score'], reverse=True)
        
        print(f"\n🏆 最佳组合 (前5名):")
        for i, result in enumerate(results[:5]):
            print(f"   {i+1}. {result['price_range']} + {result['volume_range']}")
            print(f"      成功率: {result['success_rate']:.1%}, 样本: {result['total_count']}, "
                  f"平均涨幅: {result['avg_gain']:.1%}, 评分: {result['composite_score']:.3f}")
        
        return results
    
    def analyze_premium_strategies(self, df: pd.DataFrame):
        """分析预定义的优质策略"""
        print(f"\n🎯 预定义优质策略分析:")
        print("-" * 80)
        print(f"{'策略名称':<15} {'描述':<20} {'数量':<6} {'成功':<6} {'成功率':<8} {'平均涨幅':<10}")
        print("-" * 80)
        
        strategy_results = []
        
        for key, strategy in self.premium_strategies.items():
            conditions = [
                {"feature": "D-E涨跌幅", "operator": "between", "value": list(strategy["price_range"])},
                {"feature": "E点成交量/D点成交量", "operator": "between", "value": list(strategy["volume_range"])}
            ]
            
            mask = self.analyzer.apply_conditions(df, conditions)
            subset = df[mask]
            
            if len(subset) > 0:
                success_mask = subset['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(subset)
                success_rate = success_count / total_count
                
                # 计算平均涨幅
                avg_gain = 0
                if success_count > 0 and '5日最大涨幅' in subset.columns:
                    success_cases = subset[success_mask]
                    actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                    if len(actual_gains) > 0:
                        avg_gain = actual_gains.mean()
                
                print(f"{strategy['name']:<15} {strategy['description']:<20} {total_count:<6} {success_count:<6} "
                      f"{success_rate:<8.1%} {avg_gain:<10.1%}")
                
                strategy_results.append({
                    'strategy_key': key,
                    'name': strategy['name'],
                    'description': strategy['description'],
                    'total_count': total_count,
                    'success_count': success_count,
                    'success_rate': success_rate,
                    'avg_gain': avg_gain,
                    'subset_df': subset
                })
        
        # 推荐最佳策略
        if strategy_results:
            best_strategy = max(strategy_results, key=lambda x: x['success_rate'])
            print(f"\n💡 推荐策略: {best_strategy['name']}")
            print(f"   成功率: {best_strategy['success_rate']:.1%}")
            print(f"   样本数: {best_strategy['total_count']}")
            print(f"   平均涨幅: {best_strategy['avg_gain']:.1%}")
            
            # 显示推荐股票
            if best_strategy['total_count'] > 0:
                self.show_recommended_stocks(best_strategy['subset_df'], best_strategy['name'])
        
        return strategy_results
    
    def show_recommended_stocks(self, subset_df: pd.DataFrame, strategy_name: str):
        """显示推荐的股票"""
        print(f"\n📋 {strategy_name} - 推荐股票 (前10只):")
        print("-" * 80)
        
        display_cols = ['股票', '买入日期', 'D-E涨跌幅', 'E点成交量/D点成交量', '5日成功选股', '5日最大涨幅']
        available_cols = [col for col in display_cols if col in subset_df.columns]
        
        # 按成功概率排序（成功的在前）
        sorted_df = subset_df.sort_values('5日成功选股', ascending=False)
        
        for i, (idx, row) in enumerate(sorted_df.head(10).iterrows()):
            stock_info = []
            for col in available_cols:
                value = row.get(col, 'N/A')
                if col in ['D-E涨跌幅', '5日最大涨幅']:
                    try:
                        value = f"{float(value):.1f}%"
                    except:
                        pass
                elif col == 'E点成交量/D点成交量':
                    try:
                        value = f"{float(value):.3f}"
                    except:
                        pass
                stock_info.append(f"{col}: {value}")
            
            status = "✅" if row.get('5日成功选股') == "成功" else "❌"
            print(f"   {i+1}. {status} {' | '.join(stock_info)}")
    
    def create_combination_charts(self, df: pd.DataFrame, price_change: pd.Series, 
                                volume_ratio: pd.Series, combination_results: list):
        """创建组合分析图表"""
        print(f"\n🎨 生成组合分析图表...")
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('D-E Price Change + Volume Ratio Combined Analysis', fontsize=16, fontweight='bold')
        
        # 1. 价格-成交量散点图
        ax1 = axes[0, 0]
        success_mask = df['5日成功选股'] == "成功"
        
        # 成功案例
        ax1.scatter(volume_ratio[success_mask], price_change[success_mask],
                   alpha=0.7, color='green', label='Success Cases', s=30)
        
        # 失败案例
        ax1.scatter(volume_ratio[~success_mask], price_change[~success_mask],
                   alpha=0.4, color='red', label='Failure Cases', s=20)
        
        ax1.set_title('Price Change vs Volume Ratio', fontweight='bold')
        ax1.set_xlabel('Volume Ratio (E/D)')
        ax1.set_ylabel('Price Change (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(0, color='black', linestyle='-', alpha=0.3)
        ax1.axvline(1.0, color='black', linestyle='-', alpha=0.3)
        
        # 2. 组合成功率热力图
        ax2 = axes[0, 1]
        if combination_results:
            # 创建成功率矩阵
            price_names = list(set([r['price_range'] for r in combination_results]))
            volume_names = list(set([r['volume_range'] for r in combination_results]))
            
            success_matrix = np.zeros((len(price_names), len(volume_names)))
            
            for result in combination_results:
                i = price_names.index(result['price_range'])
                j = volume_names.index(result['volume_range'])
                success_matrix[i, j] = result['success_rate'] * 100
            
            im = ax2.imshow(success_matrix, cmap='RdYlGn', aspect='auto')
            ax2.set_title('Success Rate Heatmap (%)', fontweight='bold')
            ax2.set_xticks(range(len(volume_names)))
            ax2.set_xticklabels(volume_names, rotation=45)
            ax2.set_yticks(range(len(price_names)))
            ax2.set_yticklabels(price_names)
            
            # 添加数值标签
            for i in range(len(price_names)):
                for j in range(len(volume_names)):
                    if success_matrix[i, j] > 0:
                        ax2.text(j, i, f'{success_matrix[i, j]:.1f}',
                               ha='center', va='center', fontsize=8)
            
            plt.colorbar(im, ax=ax2)
        
        # 3. 最佳组合柱状图
        ax3 = axes[1, 0]
        if combination_results:
            top_combinations = combination_results[:8]  # 前8个组合
            combo_names = [f"{r['price_range']}\n+{r['volume_range']}" for r in top_combinations]
            success_rates = [r['success_rate'] * 100 for r in top_combinations]
            
            bars = ax3.bar(range(len(combo_names)), success_rates,
                          color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in success_rates])
            ax3.set_title('Top Combinations Success Rate', fontweight='bold')
            ax3.set_xlabel('Price + Volume Combination')
            ax3.set_ylabel('Success Rate (%)')
            ax3.set_xticks(range(len(combo_names)))
            ax3.set_xticklabels(combo_names, rotation=45, ha='right', fontsize=8)
            
            # 添加数值标签
            for bar, count in zip(bars, [r['total_count'] for r in top_combinations]):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{height:.1f}%\n(n={count})', ha='center', va='bottom', fontsize=7)
        
        # 4. 样本分布饼图
        ax4 = axes[1, 1]
        if combination_results:
            # 按价格区间统计样本分布
            price_distribution = {}
            for result in combination_results:
                price_range = result['price_range']
                if price_range not in price_distribution:
                    price_distribution[price_range] = 0
                price_distribution[price_range] += result['total_count']
            
            if price_distribution:
                labels = list(price_distribution.keys())
                sizes = list(price_distribution.values())
                colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
                
                ax4.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
                ax4.set_title('Sample Distribution by Price Range', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        output_file = "combined_de_volume_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {output_file}")
        
        plt.show()
    
    def generate_selection_report(self, data_file: str, output_file: str = "combined_selection_report.txt"):
        """生成综合选股报告"""
        print(f"\n📝 生成综合选股报告...")
        
        combination_results, strategy_results = self.analyze_combined_conditions(data_file)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("D-E涨跌幅 + E点成交量/D点成交量 综合选股报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("一、分析方法说明\n")
            f.write("-" * 30 + "\n")
            f.write("本报告结合D-E点价格变化和成交量变化进行综合选股分析\n")
            f.write("通过价格-成交量组合矩阵找出最优的选股条件\n\n")
            
            if combination_results:
                f.write("二、最佳组合分析结果\n")
                f.write("-" * 30 + "\n")
                for i, result in enumerate(combination_results[:5]):
                    f.write(f"{i+1}. {result['price_range']} + {result['volume_range']}\n")
                    f.write(f"   成功率: {result['success_rate']:.1%}\n")
                    f.write(f"   样本数: {result['total_count']}\n")
                    f.write(f"   平均涨幅: {result['avg_gain']:.1%}\n")
                    f.write(f"   综合评分: {result['composite_score']:.3f}\n\n")
            
            if strategy_results:
                f.write("三、预定义策略结果\n")
                f.write("-" * 30 + "\n")
                for strategy in strategy_results:
                    f.write(f"{strategy['name']}: {strategy['description']}\n")
                    f.write(f"   成功率: {strategy['success_rate']:.1%}\n")
                    f.write(f"   样本数: {strategy['total_count']}\n")
                    f.write(f"   平均涨幅: {strategy['avg_gain']:.1%}\n\n")
            
            f.write(f"报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 报告已保存: {output_file}")

def main():
    """主函数"""
    print("🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股器")
    print("=" * 60)
    
    # 创建选股器
    selector = CombinedDEVolumeSelector()
    
    # 数据文件
    data_file = "../../选股分析结果/2025-01-01-2025-04-15.xlsx"
    
    # 执行综合分析
    selector.generate_selection_report(data_file)
    
    print(f"\n💡 综合选股要点:")
    print("1. 价格适度下跌 + 成交量适度缩量 = 最佳组合")
    print("2. 避免极端情况：大幅下跌+严重缩量，可能是死水")
    print("3. 关注突破候选：小幅上涨+适度缩量，可能是启动信号")
    print("4. 综合考虑成功率、样本量和平均涨幅")

if __name__ == "__main__":
    main()
