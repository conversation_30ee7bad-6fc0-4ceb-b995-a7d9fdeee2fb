{"timestamp": "20250803_013317", "config": {"version": "1.0.0", "description": "深度学习股票预测配置", "models": {"PrecisionMLP": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.3, "activation": "relu"}, "ResidualNet": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.2, "activation": "relu"}, "AttentionNet": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.2, "activation": "relu", "num_heads": 4}}, "training": {"optimizer": "adam", "learning_rate": 0.0005, "batch_size": 64, "epochs": 150, "early_stopping_patience": 15, "validation_split": 0.25, "weight_decay": 0.01, "class_weight": 3.0, "lr_scheduler": {"type": "reduce_on_plateau", "factor": 0.7, "patience": 10, "min_lr": 1e-06}, "device": "auto"}, "data": {"feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"], "target_column": "5日最大涨幅", "target_threshold": 0.05, "normalization": {"method": "standard_scaler", "feature_range": [-1, 1]}, "train_test_split": 0.8, "random_state": 42, "data_path": "选股分析结果/选股分析结果_20250730_225530.xlsx", "save_model": true, "model_path": "deeplearning/models/", "logs_path": "deeplearning/logs/", "checkpoints": "deeplearning/checkpoints/"}, "prediction": {"confidence_threshold": 0.5, "output_probabilities": true, "batch_prediction": true}, "output": {"results_path": "deeplearning/output/", "model_summary": true, "feature_importance": true, "confusion_matrix": true, "classification_report": true}}, "data_info": {"train_samples": 945, "test_samples": 237, "feature_count": 31, "positive_rate_train": 0.28994708994708995, "positive_rate_test": 0.2911392405063291}, "evaluation_metrics": {"PrecisionMLP": {"test_accuracy": 0.2911392405063291, "test_precision": 0.2911392405063291, "test_recall": 1.0, "f1_score": 0.45098039215686275, "auc_score": 0.6904761904761905, "confusion_matrix": [[0, 168], [0, 69]]}, "ResidualNet": {"test_accuracy": 0.29535864978902954, "test_precision": 0.28695652173913044, "test_recall": 0.9565217391304348, "f1_score": 0.4414715719063545, "auc_score": 0.65527950310559, "confusion_matrix": [[4, 164], [3, 66]]}, "AttentionNet": {"test_accuracy": 0.2911392405063291, "test_precision": 0.2911392405063291, "test_recall": 1.0, "f1_score": 0.45098039215686275, "auc_score": 0.5, "confusion_matrix": [[0, 168], [0, 69]]}}, "feature_importance": {"PrecisionMLP": [{"feature": "A点实体涨跌幅", "importance": 0.0}, {"feature": "A点价格振幅", "importance": 0.0}, {"feature": "B点成交量", "importance": 0.0}, {"feature": "B点实体涨跌幅", "importance": 0.0}, {"feature": "B点价格振幅", "importance": 0.0}, {"feature": "C点最低", "importance": 0.0}, {"feature": "C点成交量", "importance": 0.0}, {"feature": "C点实体涨跌幅", "importance": 0.0}, {"feature": "C点价格振幅", "importance": 0.0}, {"feature": "D点成交量", "importance": 0.0}, {"feature": "D点实体涨跌幅", "importance": 0.0}, {"feature": "D点价格振幅", "importance": 0.0}, {"feature": "E点成交量", "importance": 0.0}, {"feature": "E点实体涨跌幅", "importance": 0.0}, {"feature": "E点价格振幅", "importance": 0.0}, {"feature": "A-B涨幅", "importance": 0.0}, {"feature": "A-B天数", "importance": 0.0}, {"feature": "B-C跌幅", "importance": 0.0}, {"feature": "B-C天数", "importance": 0.0}, {"feature": "C-D涨幅", "importance": 0.0}, {"feature": "C-D天数", "importance": 0.0}, {"feature": "D-E涨幅", "importance": 0.0}, {"feature": "D-E天数", "importance": 0.0}, {"feature": "D点成交量/C-D均量", "importance": 0.0}, {"feature": "D点上影线涨幅", "importance": 0.0}, {"feature": "D点上影线/实体", "importance": 0.0}, {"feature": "E点成交量/C-D均量", "importance": 0.0}, {"feature": "E点成交量/D点成交量", "importance": 0.0}, {"feature": "E点J值", "importance": 0.0}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.0}, {"feature": "E点相对D点收盘价涨幅", "importance": 0.0}], "ResidualNet": [{"feature": "C点最低", "importance": 0.012658227848101278}, {"feature": "E点实体涨跌幅", "importance": 0.008438818565400852}, {"feature": "A-B涨幅", "importance": 0.008438818565400852}, {"feature": "D-E涨幅", "importance": 0.008438818565400852}, {"feature": "D点上影线涨幅", "importance": 0.008438818565400852}, {"feature": "C-D涨幅", "importance": 0.004219409282700426}, {"feature": "B点价格振幅", "importance": 0.004219409282700426}, {"feature": "B-C跌幅", "importance": 0.004219409282700426}, {"feature": "E点成交量/D点成交量", "importance": 0.004219409282700426}, {"feature": "C-D天数", "importance": 0.004219409282700426}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.0}, {"feature": "C点成交量", "importance": 0.0}, {"feature": "D点成交量", "importance": 0.0}, {"feature": "D点实体涨跌幅", "importance": 0.0}, {"feature": "B点实体涨跌幅", "importance": 0.0}, {"feature": "E点相对D点收盘价涨幅", "importance": 0.0}, {"feature": "E点成交量/C-D均量", "importance": -0.004219409282700426}, {"feature": "B-C天数", "importance": -0.004219409282700426}, {"feature": "D点上影线/实体", "importance": -0.004219409282700426}, {"feature": "D点成交量/C-D均量", "importance": -0.004219409282700426}, {"feature": "E点J值", "importance": -0.004219409282700426}, {"feature": "C点价格振幅", "importance": -0.004219409282700426}, {"feature": "E点成交量", "importance": -0.004219409282700426}, {"feature": "C点实体涨跌幅", "importance": -0.008438818565400852}, {"feature": "A点价格振幅", "importance": -0.008438818565400852}, {"feature": "D-E天数", "importance": -0.008438818565400852}, {"feature": "E点价格振幅", "importance": -0.008438818565400852}, {"feature": "A-B天数", "importance": -0.008438818565400852}, {"feature": "B点成交量", "importance": -0.012658227848101278}, {"feature": "A点实体涨跌幅", "importance": -0.012658227848101278}, {"feature": "D点价格振幅", "importance": -0.016877637130801704}], "AttentionNet": [{"feature": "A点实体涨跌幅", "importance": 0.0}, {"feature": "A点价格振幅", "importance": 0.0}, {"feature": "B点成交量", "importance": 0.0}, {"feature": "B点实体涨跌幅", "importance": 0.0}, {"feature": "B点价格振幅", "importance": 0.0}, {"feature": "C点最低", "importance": 0.0}, {"feature": "C点成交量", "importance": 0.0}, {"feature": "C点实体涨跌幅", "importance": 0.0}, {"feature": "C点价格振幅", "importance": 0.0}, {"feature": "D点成交量", "importance": 0.0}, {"feature": "D点实体涨跌幅", "importance": 0.0}, {"feature": "D点价格振幅", "importance": 0.0}, {"feature": "E点成交量", "importance": 0.0}, {"feature": "E点实体涨跌幅", "importance": 0.0}, {"feature": "E点价格振幅", "importance": 0.0}, {"feature": "A-B涨幅", "importance": 0.0}, {"feature": "A-B天数", "importance": 0.0}, {"feature": "B-C跌幅", "importance": 0.0}, {"feature": "B-C天数", "importance": 0.0}, {"feature": "C-D涨幅", "importance": 0.0}, {"feature": "C-D天数", "importance": 0.0}, {"feature": "D-E涨幅", "importance": 0.0}, {"feature": "D-E天数", "importance": 0.0}, {"feature": "D点成交量/C-D均量", "importance": 0.0}, {"feature": "D点上影线涨幅", "importance": 0.0}, {"feature": "D点上影线/实体", "importance": 0.0}, {"feature": "E点成交量/C-D均量", "importance": 0.0}, {"feature": "E点成交量/D点成交量", "importance": 0.0}, {"feature": "E点J值", "importance": 0.0}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.0}, {"feature": "E点相对D点收盘价涨幅", "importance": 0.0}]}}