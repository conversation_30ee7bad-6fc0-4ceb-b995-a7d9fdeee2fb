 python combined_de_volume_selector.py
 🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股器
============================================================

📝 生成综合选股报告...
🎯 D-E涨跌幅 + E点成交量/D点成交量 综合选股分析
================================================================================
📂 加载数据: ../../选股分析结果/2025-01-01-2025-04-15.xlsx
   总数据: 5126 条记录
   ✅ 自动计算D-E涨跌幅
✅ 有效数据: 5126 条
📊 数据概览:
   D-E涨跌幅: -2.11% ± 3.10%
   成交量比率: 0.541 ± 0.109

📊 优质组合分析 (基于历史数据筛选):
----------------------------------------------------------------------------------------------------
价格区间         成交量区间        数量     成功     成功率      平均涨幅       综合评分
----------------------------------------------------------------------------------------------------
大幅下跌         明显缩量         56     9      16.1%    14.8%      0.412
大幅下跌         适度缩量         17     5      29.4%    14.1%      0.308
中度下跌         严重缩量         130    20     15.4%    15.7%      0.408
中度下跌         明显缩量         306    53     17.3%    16.9%      0.421
中度下跌         适度缩量         157    26     16.6%    18.4%      0.416

🏆 当前数据表现排名:
   1. 中度下跌 + 明显缩量
      预期17.3%→实际17.3%, 样本: 306, 平均涨幅: 16.9%, 评分: 0.421
   2. 中度下跌 + 适度缩量
      预期16.6%→实际16.6%, 样本: 157, 平均涨幅: 18.4%, 评分: 0.416
   3. 大幅下跌 + 明显缩量
      预期16.1%→实际16.1%, 样本: 56, 平均涨幅: 14.8%, 评分: 0.412
   4. 中度下跌 + 严重缩量
      预期15.4%→实际15.4%, 样本: 130, 平均涨幅: 15.7%, 评分: 0.408
   5. 大幅下跌 + 适度缩量
      预期29.4%→实际29.4%, 样本: 17, 平均涨幅: 14.1%, 评分: 0.308
