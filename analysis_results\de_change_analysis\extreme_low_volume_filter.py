#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极致缩量股票筛选器
筛选出成交量极度萎缩的股票，这类股票往往处于变盘前夕
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from analyze_de_change_success import MultiConditionAnalyzer
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class ExtremeLowVolumeFilter:
    """极致缩量筛选器"""
    
    def __init__(self):
        self.analyzer = MultiConditionAnalyzer()
        
        # 极致缩量的判断标准
        self.extreme_criteria = {
            "level_1_ultra_low": {
                "name": "一级极致缩量",
                "description": "成交量萎缩到极致水平",
                "conditions": [
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.3},  # E点相对D点缩量70%+
                    {"feature": "E点成交量/C-D均量", "operator": "<=", "value": 0.4},   # 相对均量缩量60%+
                ]
            },
            "level_2_severe_low": {
                "name": "二级严重缩量", 
                "description": "成交量严重萎缩",
                "conditions": [
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.5},  # E点相对D点缩量50%+
                    {"feature": "E点成交量/C-D均量", "operator": "<=", "value": 0.6},   # 相对均量缩量40%+
                ]
            },
            "level_3_moderate_low": {
                "name": "三级中度缩量",
                "description": "成交量明显萎缩", 
                "conditions": [
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.7},  # E点相对D点缩量30%+
                    {"feature": "E点成交量/C-D均量", "operator": "<=", "value": 0.8},   # 相对均量缩量20%+
                ]
            }
        }
        
        # 组合筛选策略
        self.combination_strategies = {
            "shrinking_consolidation": {
                "name": "缩量整理",
                "description": "价格小幅波动 + 极致缩量",
                "conditions": [
                    {"feature": "D-E涨跌幅", "operator": "between", "value": [-3, 3]},      # 价格变化不大
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.4},      # 严重缩量
                    {"feature": "E点价格振幅", "operator": "<=", "value": 5}                # 振幅较小
                ]
            },
            "volume_dry_up": {
                "name": "成交量枯竭",
                "description": "连续缩量 + 低位盘整",
                "conditions": [
                    {"feature": "E点成交量/C-D均量", "operator": "<=", "value": 0.3},       # 相对均量极度缩量
                    {"feature": "D点成交量/C-D均量", "operator": "<=", "value": 0.6},       # D点也相对缩量
                    {"feature": "D-E涨跌幅", "operator": "between", "value": [-5, 2]}      # 价格相对稳定
                ]
            },
            "pre_breakout_setup": {
                "name": "突破前蓄势",
                "description": "极致缩量 + 技术形态良好",
                "conditions": [
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.3},      # 极致缩量
                    {"feature": "E点J值", "operator": "between", "value": [20, 80]},        # J值在合理区间
                    {"feature": "D-E涨跌幅", "operator": "between", "value": [-2, 1]},      # 价格稳定
                    {"feature": "E点实体涨跌幅", "operator": ">", "value": -3}              # 实体跌幅不大
                ]
            },
            "oversold_volume_dry": {
                "name": "超跌缩量",
                "description": "价格超跌 + 成交量枯竭",
                "conditions": [
                    {"feature": "D-E涨跌幅", "operator": "<", "value": -5},                 # 价格下跌
                    {"feature": "E点成交量/D点成交量", "operator": "<=", "value": 0.4},      # 缩量下跌
                    {"feature": "E点J值", "operator": "<", "value": 30}                     # J值较低
                ]
            }
        }
    
    def analyze_extreme_low_volume(self, data_file: str):
        """分析极致缩量股票"""
        print("🔍 极致缩量股票筛选分析")
        print("=" * 80)
        
        # 加载数据
        df = self.analyzer.load_data(data_file)
        
        if '5日成功选股' not in df.columns:
            print("❌ 未找到目标列")
            return None
        
        # 检查成交量相关列
        volume_columns = ["E点成交量", "D点成交量", "E点成交量/D点成交量", "E点成交量/C-D均量"]
        missing_columns = [col for col in volume_columns if col not in df.columns]
        
        if missing_columns:
            print(f"⚠️ 缺失成交量列: {missing_columns}")
            print("尝试使用可用的成交量数据...")
        
        # 分析各级别缩量情况
        results = {}
        
        print("\n📊 各级别缩量分析:")
        print("-" * 80)
        print(f"{'级别':<15} {'描述':<20} {'符合数量':<10} {'成功数量':<10} {'成功率':<10}")
        print("-" * 80)
        
        for level_key, criteria in self.extreme_criteria.items():
            mask = self.analyzer.apply_conditions(df, criteria["conditions"])
            filtered_df = df[mask]
            
            if len(filtered_df) > 0:
                success_mask = filtered_df['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(filtered_df)
                success_rate = success_count / total_count
                
                print(f"{criteria['name']:<15} {criteria['description']:<20} {total_count:<10} {success_count:<10} {success_rate:<10.1%}")
                
                results[level_key] = {
                    'name': criteria['name'],
                    'filtered_df': filtered_df,
                    'success_count': success_count,
                    'total_count': total_count,
                    'success_rate': success_rate
                }
            else:
                print(f"{criteria['name']:<15} {criteria['description']:<20} {'0':<10} {'0':<10} {'N/A':<10}")
        
        # 分析组合策略
        print(f"\n🎯 组合策略分析:")
        print("-" * 80)
        print(f"{'策略名称':<15} {'描述':<25} {'符合数量':<10} {'成功数量':<10} {'成功率':<10}")
        print("-" * 80)
        
        strategy_results = {}
        
        for strategy_key, strategy in self.combination_strategies.items():
            mask = self.analyzer.apply_conditions(df, strategy["conditions"])
            filtered_df = df[mask]
            
            if len(filtered_df) > 0:
                success_mask = filtered_df['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(filtered_df)
                success_rate = success_count / total_count
                
                print(f"{strategy['name']:<15} {strategy['description']:<25} {total_count:<10} {success_count:<10} {success_rate:<10.1%}")
                
                strategy_results[strategy_key] = {
                    'name': strategy['name'],
                    'filtered_df': filtered_df,
                    'success_count': success_count,
                    'total_count': total_count,
                    'success_rate': success_rate
                }
            else:
                print(f"{strategy['name']:<15} {strategy['description']:<25} {'0':<10} {'0':<10} {'N/A':<10}")
        
        # 详细分析最佳策略
        self.analyze_best_strategies(results, strategy_results)
        
        # 生成可视化
        self.create_volume_analysis_charts(df, results, strategy_results)
        
        return results, strategy_results
    
    def analyze_best_strategies(self, results: dict, strategy_results: dict):
        """分析最佳策略的详细情况"""
        print(f"\n🏆 最佳策略详细分析:")
        print("=" * 80)
        
        # 合并所有结果
        all_results = {**results, **strategy_results}
        
        # 按成功率排序，但要考虑样本量
        valid_results = [(k, v) for k, v in all_results.items() if v['total_count'] >= 5]
        valid_results.sort(key=lambda x: x[1]['success_rate'], reverse=True)
        
        if not valid_results:
            print("❌ 没有找到有效的策略结果")
            return
        
        # 分析前3个最佳策略
        for i, (key, result) in enumerate(valid_results[:3]):
            print(f"\n🥇 第{i+1}名: {result['name']}")
            print(f"   成功率: {result['success_rate']:.1%}")
            print(f"   样本数: {result['total_count']}")
            print(f"   成功数: {result['success_count']}")
            
            # 分析成功案例的特征
            if result['success_count'] > 0:
                success_cases = result['filtered_df'][result['filtered_df']['5日成功选股'] == "成功"]
                
                # 计算平均涨幅
                if '5日最大涨幅' in success_cases.columns:
                    actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                    if len(actual_gains) > 0:
                        print(f"   平均涨幅: {actual_gains.mean():.1%}")
                        print(f"   最大涨幅: {actual_gains.max():.1%}")
                
                # 显示成功案例
                print(f"   成功案例 (前5个):")
                display_cols = ['股票', '买入日期', '5日最大涨幅']
                available_cols = [col for col in display_cols if col in success_cases.columns]
                
                for j, (idx, row) in enumerate(success_cases.head(5).iterrows()):
                    stock_info = " | ".join([f"{col}: {row.get(col, 'N/A')}" for col in available_cols])
                    print(f"      {j+1}. {stock_info}")
    
    def create_volume_analysis_charts(self, df: pd.DataFrame, results: dict, strategy_results: dict):
        """创建成交量分析图表"""
        print(f"\n🎨 生成成交量分析图表...")
        
        # 检查必要的列
        if "E点成交量/D点成交量" not in df.columns:
            print("⚠️ 缺少成交量比率数据，跳过图表生成")
            return
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Extreme Low Volume Analysis', fontsize=16, fontweight='bold')
        
        # 1. 成交量比率分布
        ax1 = axes[0, 0]
        volume_ratio = pd.to_numeric(df["E点成交量/D点成交量"], errors='coerce').dropna()
        
        # 分成功和失败案例
        success_mask = df['5日成功选股'] == "成功"
        success_volume = pd.to_numeric(df[success_mask]["E点成交量/D点成交量"], errors='coerce').dropna()
        fail_volume = pd.to_numeric(df[~success_mask]["E点成交量/D点成交量"], errors='coerce').dropna()
        
        ax1.hist([success_volume, fail_volume], bins=30, alpha=0.7, 
                 label=['Success Cases', 'Failure Cases'], color=['green', 'red'])
        ax1.axvline(0.3, color='blue', linestyle='--', alpha=0.7, label='Ultra Low (0.3)')
        ax1.axvline(0.5, color='orange', linestyle='--', alpha=0.7, label='Severe Low (0.5)')
        ax1.axvline(0.7, color='purple', linestyle='--', alpha=0.7, label='Moderate Low (0.7)')
        ax1.set_title('Volume Ratio Distribution (E/D)', fontweight='bold')
        ax1.set_xlabel('Volume Ratio')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        
        # 2. 各级别缩量的成功率
        ax2 = axes[0, 1]
        if results:
            levels = []
            success_rates = []
            sample_counts = []
            
            for key, result in results.items():
                if result['total_count'] > 0:
                    levels.append(result['name'])
                    success_rates.append(result['success_rate'] * 100)
                    sample_counts.append(result['total_count'])
            
            if levels:
                bars = ax2.bar(levels, success_rates, 
                              color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in success_rates])
                ax2.set_title('Success Rate by Volume Level', fontweight='bold')
                ax2.set_xlabel('Volume Level')
                ax2.set_ylabel('Success Rate (%)')
                ax2.tick_params(axis='x', rotation=45)
                
                # 添加样本数标签
                for bar, count in zip(bars, sample_counts):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                            f'n={count}', ha='center', va='bottom', fontsize=9)
        
        # 3. 组合策略成功率
        ax3 = axes[1, 0]
        if strategy_results:
            strategies = []
            strategy_rates = []
            strategy_counts = []
            
            for key, result in strategy_results.items():
                if result['total_count'] > 0:
                    strategies.append(result['name'])
                    strategy_rates.append(result['success_rate'] * 100)
                    strategy_counts.append(result['total_count'])
            
            if strategies:
                bars = ax3.bar(strategies, strategy_rates,
                              color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in strategy_rates])
                ax3.set_title('Success Rate by Strategy', fontweight='bold')
                ax3.set_xlabel('Strategy')
                ax3.set_ylabel('Success Rate (%)')
                ax3.tick_params(axis='x', rotation=45)
                
                # 添加样本数标签
                for bar, count in zip(bars, strategy_counts):
                    height = bar.get_height()
                    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                            f'n={count}', ha='center', va='bottom', fontsize=9)
        
        # 4. 成交量vs价格变化散点图
        ax4 = axes[1, 1]
        if "D-E涨跌幅" in df.columns:
            price_change = pd.to_numeric(df["D-E涨跌幅"], errors='coerce')
            volume_change = pd.to_numeric(df["E点成交量/D点成交量"], errors='coerce')
            
            # 过滤有效数据
            valid_mask = pd.notna(price_change) & pd.notna(volume_change)
            price_valid = price_change[valid_mask]
            volume_valid = volume_change[valid_mask]
            success_valid = success_mask[valid_mask]
            
            # 绘制散点图
            ax4.scatter(volume_valid[success_valid], price_valid[success_valid],
                       alpha=0.6, color='green', label='Success Cases', s=20)
            ax4.scatter(volume_valid[~success_valid], price_valid[~success_valid],
                       alpha=0.6, color='red', label='Failure Cases', s=20)
            
            ax4.axvline(0.3, color='blue', linestyle='--', alpha=0.5)
            ax4.axvline(0.5, color='orange', linestyle='--', alpha=0.5)
            ax4.axhline(0, color='black', linestyle='-', alpha=0.3)
            
            ax4.set_title('Volume vs Price Change', fontweight='bold')
            ax4.set_xlabel('Volume Ratio (E/D)')
            ax4.set_ylabel('Price Change (%)')
            ax4.legend()
        
        plt.tight_layout()
        
        # 保存图表
        output_file = "extreme_low_volume_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {output_file}")
        
        plt.show()
    
    def generate_screening_report(self, data_file: str, output_file: str = "extreme_low_volume_report.txt"):
        """生成筛选报告"""
        print(f"\n📝 生成筛选报告...")
        
        results, strategy_results = self.analyze_extreme_low_volume(data_file)
        
        if not results and not strategy_results:
            print("❌ 没有结果可生成报告")
            return
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("极致缩量股票筛选报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("一、筛选标准说明\n")
            f.write("-" * 30 + "\n")
            f.write("极致缩量定义：\n")
            f.write("1. 一级极致缩量：E点成交量相对D点缩量70%+，相对均量缩量60%+\n")
            f.write("2. 二级严重缩量：E点成交量相对D点缩量50%+，相对均量缩量40%+\n")
            f.write("3. 三级中度缩量：E点成交量相对D点缩量30%+，相对均量缩量20%+\n\n")
            
            f.write("二、筛选结果汇总\n")
            f.write("-" * 30 + "\n")
            
            # 写入级别结果
            if results:
                for key, result in results.items():
                    f.write(f"{result['name']}: {result['total_count']}只股票, ")
                    f.write(f"成功{result['success_count']}只, 成功率{result['success_rate']:.1%}\n")
            
            # 写入策略结果
            if strategy_results:
                f.write("\n组合策略结果:\n")
                for key, result in strategy_results.items():
                    f.write(f"{result['name']}: {result['total_count']}只股票, ")
                    f.write(f"成功{result['success_count']}只, 成功率{result['success_rate']:.1%}\n")
            
            f.write(f"\n报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 报告已保存: {output_file}")

def main():
    """主函数"""
    print("🔍 极致缩量股票筛选器")
    print("=" * 50)
    
    # 创建筛选器
    filter_tool = ExtremeLowVolumeFilter()
    
    # 数据文件
    data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    # 执行分析
    results, strategy_results = filter_tool.analyze_extreme_low_volume(data_file)
    
    # 生成报告
    filter_tool.generate_screening_report(data_file)
    
    print(f"\n💡 极致缩量分析要点:")
    print("1. 极致缩量往往预示着变盘，可能向上突破或向下破位")
    print("2. 结合价格位置判断：低位缩量看涨，高位缩量看跌")
    print("3. 关注缩量后的首次放量，往往是方向选择的信号")
    print("4. 建议设置止损，缩量股票一旦破位可能加速下跌")

if __name__ == "__main__":
    main()
