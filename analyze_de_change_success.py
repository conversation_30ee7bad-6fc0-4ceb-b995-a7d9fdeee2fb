#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计D-E点涨跌幅与5日选股成功率的关系
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置字体 - 使用英文避免中文渲染问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def analyze_de_change_success():
    """统计D-E点涨跌幅与5日选股成功率的关系"""
    print("📊 统计D-E点涨跌幅与5日选股成功率的关系")
    print("=" * 80)
    
    # 加载数据
    data_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    print(f"📂 加载数据: {data_file}")
    
    df = pd.read_excel(data_file)
    print(f"   总数据: {len(df)} 条记录")
    
    # 检查关键列
    required_columns = ['D点收盘', 'E点收盘', '5日成功选股']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺失关键列: {missing_columns}")
        return
    
    # 计算D-E点涨跌幅
    d_prices = pd.to_numeric(df['D点收盘'], errors='coerce')
    e_prices = pd.to_numeric(df['E点收盘'], errors='coerce')
    
    # 过滤有效数据
    valid_mask = pd.notna(d_prices) & pd.notna(e_prices) & (d_prices > 0)
    valid_df = df[valid_mask].copy()
    d_prices_valid = d_prices[valid_mask]
    e_prices_valid = e_prices[valid_mask]
    
    # 计算D-E涨跌幅
    de_change = (e_prices_valid - d_prices_valid) / d_prices_valid * 100
    valid_df['de_change'] = de_change
    
    print(f"✅ 有效数据: {len(valid_df)} 条")
    
    # 基本统计
    print(f"\n📈 D-E点涨跌幅基本统计:")
    print(f"   平均涨跌幅: {de_change.mean():.2f}%")
    print(f"   中位数涨跌幅: {de_change.median():.2f}%")
    print(f"   标准差: {de_change.std():.2f}%")
    print(f"   最大涨幅: {de_change.max():.2f}%")
    print(f"   最大跌幅: {de_change.min():.2f}%")
    
    # 统计成功率
    success_mask = valid_df['5日成功选股'] == "成功"
    total_success = success_mask.sum()
    total_samples = len(valid_df)
    overall_success_rate = total_success / total_samples
    
    print(f"\n📊 整体成功率:")
    print(f"   总样本: {total_samples}")
    print(f"   成功案例: {total_success}")
    print(f"   整体成功率: {overall_success_rate:.1%}")
    
    # 按涨跌幅区间分析成功率
    analyze_by_change_ranges(valid_df)
    
    # 按涨跌幅百分位数分析
    analyze_by_percentiles(valid_df)
    
    # 寻找最优涨跌幅区间
    find_optimal_ranges(valid_df)
    
    # 可视化分析
    create_visualizations(valid_df)

def analyze_by_change_ranges(valid_df):
    """按涨跌幅区间分析成功率"""
    print(f"\n📊 按D-E涨跌幅区间分析成功率:")
    print("-" * 70)
    
    # 定义区间
    ranges = [
        ("大幅下跌", -float('inf'), -5),
        ("中度下跌", -5, -3),
        ("小幅下跌", -3, -1),
        ("微幅下跌", -1, 0),
        ("持平", 0, 0),
        ("微幅上涨", 0, 1),
        ("小幅上涨", 1, 3),
        ("中度上涨", 3, 5),
        ("大幅上涨", 5, float('inf'))
    ]
    
    print(f"{'区间':<12} {'涨跌幅范围':<15} {'总数量':<8} {'成功数量':<8} {'成功率':<8}")
    print("-" * 70)
    
    range_stats = []
    
    for range_name, min_val, max_val in ranges:
        if min_val == -float('inf'):
            mask = valid_df['de_change'] <= max_val
            range_str = f"≤{max_val}%"
        elif max_val == float('inf'):
            mask = valid_df['de_change'] > min_val
            range_str = f">{min_val}%"
        elif min_val == max_val:
            mask = valid_df['de_change'] == min_val
            range_str = f"={min_val}%"
        else:
            mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
            range_str = f"{min_val}%~{max_val}%"
        
        range_data = valid_df[mask]
        total_count = len(range_data)
        
        if total_count > 0:
            success_count = (range_data['5日成功选股'] == "成功").sum()
            success_rate = success_count / total_count
            
            print(f"{range_name:<12} {range_str:<15} {total_count:<8} {success_count:<8} {success_rate:<8.1%}")
            
            range_stats.append({
                'range_name': range_name,
                'range_str': range_str,
                'total_count': total_count,
                'success_count': success_count,
                'success_rate': success_rate,
                'min_val': min_val,
                'max_val': max_val
            })
    
    # 找出成功率最高的区间
    if range_stats:
        best_ranges = sorted([r for r in range_stats if r['total_count'] >= 10], 
                           key=lambda x: x['success_rate'], reverse=True)
        
        print(f"\n🏆 成功率最高的区间 (样本≥10):")
        for i, range_info in enumerate(best_ranges[:5]):
            print(f"   {i+1}. {range_info['range_name']} ({range_info['range_str']}): "
                  f"{range_info['success_rate']:.1%} ({range_info['success_count']}/{range_info['total_count']})")

def analyze_by_percentiles(valid_df):
    """按涨跌幅百分位数分析"""
    print(f"\n📊 按D-E涨跌幅百分位数分析:")
    print("-" * 60)
    
    # 计算百分位数
    percentiles = [10, 20, 30, 40, 50, 60, 70, 80, 90]
    thresholds = np.percentile(valid_df['de_change'], percentiles)
    
    print(f"{'百分位':<8} {'阈值':<8} {'≤阈值数量':<10} {'≤阈值成功率':<12} {'>阈值成功率':<12}")
    print("-" * 60)
    
    for percentile, threshold in zip(percentiles, thresholds):
        below_mask = valid_df['de_change'] <= threshold
        above_mask = valid_df['de_change'] > threshold
        
        below_data = valid_df[below_mask]
        above_data = valid_df[above_mask]
        
        below_success_rate = (below_data['5日成功选股'] == "成功").mean() if len(below_data) > 0 else 0
        above_success_rate = (above_data['5日成功选股'] == "成功").mean() if len(above_data) > 0 else 0
        
        print(f"{percentile:<8}% {threshold:<8.2f}% {len(below_data):<10} {below_success_rate:<12.1%} {above_success_rate:<12.1%}")

def find_optimal_ranges(valid_df):
    """寻找最优涨跌幅区间"""
    print(f"\n🎯 寻找最优D-E涨跌幅区间:")
    
    # 测试不同的阈值组合
    test_ranges = [
        (-10, -5), (-5, -3), (-3, -1), (-1, 0),
        (0, 1), (1, 3), (3, 5), (5, 10),
        (-5, 0), (-3, 1), (-1, 1), (0, 3)
    ]
    
    optimal_ranges = []
    
    for min_val, max_val in test_ranges:
        mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
        range_data = valid_df[mask]
        
        if len(range_data) >= 20:  # 至少20个样本
            success_count = (range_data['5日成功选股'] == "成功").sum()
            success_rate = success_count / len(range_data)
            
            optimal_ranges.append({
                'range': f"{min_val}%~{max_val}%",
                'count': len(range_data),
                'success_count': success_count,
                'success_rate': success_rate
            })
    
    # 按成功率排序
    optimal_ranges.sort(key=lambda x: x['success_rate'], reverse=True)
    
    print(f"   最优区间 (样本≥20, 按成功率排序):")
    for i, range_info in enumerate(optimal_ranges[:10]):
        print(f"   {i+1}. {range_info['range']}: {range_info['success_rate']:.1%} "
              f"({range_info['success_count']}/{range_info['count']})")
    
    # 分析最优区间的特征
    if optimal_ranges:
        best_range = optimal_ranges[0]
        print(f"\n✅ 推荐区间: {best_range['range']}")
        print(f"   成功率: {best_range['success_rate']:.1%}")
        print(f"   样本数: {best_range['count']}")
        
        # 提取该区间的数据进行详细分析
        range_parts = best_range['range'].replace('%', '').split('~')
        min_val, max_val = float(range_parts[0]), float(range_parts[1])
        
        best_mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
        best_data = valid_df[best_mask]
        
        print(f"\n📈 推荐区间详细分析:")
        print(f"   平均D-E涨跌幅: {best_data['de_change'].mean():.2f}%")
        print(f"   中位数D-E涨跌幅: {best_data['de_change'].median():.2f}%")
        
        # 分析成功案例的实际涨幅
        success_cases = best_data[best_data['5日成功选股'] == "成功"]
        if len(success_cases) > 0 and '5日最大涨幅' in success_cases.columns:
            actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
            if len(actual_gains) > 0:
                print(f"   成功案例平均实际涨幅: {actual_gains.mean():.1%}")
                print(f"   成功案例中位数实际涨幅: {actual_gains.median():.1%}")

def create_visualizations(valid_df):
    """创建可视化图表"""
    print(f"\n🎨 生成可视化图表...")

    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('D-E Change vs 5-Day Success Rate Analysis', fontsize=16, fontweight='bold')

    # 1. D-E涨跌幅分布直方图
    ax1 = axes[0, 0]
    success_data = valid_df[valid_df['5日成功选股'] == "成功"]['de_change']
    fail_data = valid_df[valid_df['5日成功选股'] != "成功"]['de_change']

    ax1.hist([success_data, fail_data], bins=30, alpha=0.7,
             label=['Success Cases', 'Failure Cases'], color=['green', 'red'])
    ax1.set_title('D-E Change Distribution Comparison', fontweight='bold')
    ax1.set_xlabel('D-E Change (%)')
    ax1.set_ylabel('Frequency')
    ax1.legend()
    ax1.axvline(0, color='black', linestyle='--', alpha=0.5)
    
    # 2. 按区间统计成功率
    ax2 = axes[0, 1]
    ranges = [(-10, -5), (-5, -3), (-3, -1), (-1, 0), (0, 1), (1, 3), (3, 5), (5, 10)]
    range_names = ['<=-5%', '-5%~-3%', '-3%~-1%', '-1%~0%', '0%~1%', '1%~3%', '3%~5%', '>5%']
    success_rates = []
    sample_counts = []

    for min_val, max_val in ranges:
        if min_val == -10:
            mask = valid_df['de_change'] <= max_val
        elif max_val == 10:
            mask = valid_df['de_change'] > min_val
        else:
            mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)

        range_data = valid_df[mask]
        if len(range_data) > 0:
            success_rate = (range_data['5日成功选股'] == "成功").mean()
            success_rates.append(success_rate * 100)
            sample_counts.append(len(range_data))
        else:
            success_rates.append(0)
            sample_counts.append(0)

    bars = ax2.bar(range_names, success_rates,
                   color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in success_rates])
    ax2.set_title('Success Rate by D-E Change Range', fontweight='bold')
    ax2.set_xlabel('D-E Change Range')
    ax2.set_ylabel('Success Rate (%)')
    ax2.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for i, (bar, count) in enumerate(zip(bars, sample_counts)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%\n({count})', ha='center', va='bottom', fontsize=8)
    
    # 3. 样本数量分布
    ax3 = axes[1, 0]
    bars = ax3.bar(range_names, sample_counts, alpha=0.7, color='skyblue')
    ax3.set_title('Sample Count Distribution by Range', fontweight='bold')
    ax3.set_xlabel('D-E Change Range')
    ax3.set_ylabel('Sample Count')
    ax3.tick_params(axis='x', rotation=45)

    # 4. 散点图：D-E涨跌幅 vs 实际涨幅
    ax4 = axes[1, 1]
    if '5日最大涨幅' in valid_df.columns:
        actual_gains = pd.to_numeric(valid_df['5日最大涨幅'], errors='coerce')
        success_mask = valid_df['5日成功选股'] == "成功"

        # 成功案例
        ax4.scatter(valid_df[success_mask]['de_change'],
                   actual_gains[success_mask] * 100,
                   alpha=0.6, color='green', label='Success Cases', s=20)

        # 失败案例
        ax4.scatter(valid_df[~success_mask]['de_change'],
                   actual_gains[~success_mask] * 100,
                   alpha=0.6, color='red', label='Failure Cases', s=20)

        ax4.set_title('D-E Change vs Actual 5-Day Gain', fontweight='bold')
        ax4.set_xlabel('D-E Change (%)')
        ax4.set_ylabel('5-Day Actual Gain (%)')
        ax4.legend()
        ax4.axhline(10, color='blue', linestyle='--', alpha=0.5, label='10% Baseline')
        ax4.axvline(0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = "de_change_success_analysis.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {output_file}")
    
    plt.show()

def main():
    """主函数"""
    analyze_de_change_success()

if __name__ == "__main__":
    main()
