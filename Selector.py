from typing import Dict, List, Optional, Any

from scipy.signal import find_peaks
import numpy as np
import pandas as pd
import datetime
import os


# --------------------------- 通用指标 --------------------------- #

def compute_kdj(df: pd.DataFrame, n: int = 9) -> pd.DataFrame:
    if df.empty:
        return df.assign(K=np.nan, D=np.nan, J=np.nan)

    low_n = df["low"].rolling(window=n, min_periods=1).min()
    high_n = df["high"].rolling(window=n, min_periods=1).max()
    rsv = (df["close"] - low_n) / (high_n - low_n + 1e-9) * 100

    K = np.zeros_like(rsv, dtype=float)
    D = np.zeros_like(rsv, dtype=float)
    for i in range(len(df)):
        if i == 0:
            K[i] = D[i] = 50.0
        else:
            K[i] = 2 / 3 * K[i - 1] + 1 / 3 * rsv.iloc[i]
            D[i] = 2 / 3 * D[i - 1] + 1 / 3 * K[i]
    J = 3 * K - 2 * D
    return df.assign(K=K, D=D, J=J)


def compute_bbi(df: pd.DataFrame) -> pd.Series:
    ma3 = df["close"].rolling(3).mean()
    ma6 = df["close"].rolling(6).mean()
    ma12 = df["close"].rolling(12).mean()
    ma24 = df["close"].rolling(24).mean()
    return (ma3 + ma6 + ma12 + ma24) / 4


def compute_rsv(
    df: pd.DataFrame,
    n: int,
) -> pd.Series:
    """
    按公式：RSV(N) = 100 × (C - LLV(L,N)) ÷ (HHV(C,N) - LLV(L,N))
    - C 用收盘价最高值 (HHV of close)
    - L 用最低价最低值 (LLV of low)
    """
    low_n = df["low"].rolling(window=n, min_periods=1).min()
    high_close_n = df["close"].rolling(window=n, min_periods=1).max()
    rsv = (df["close"] - low_n) / (high_close_n - low_n + 1e-9) * 100.0
    return rsv


def compute_dif(df: pd.DataFrame, fast: int = 12, slow: int = 26) -> pd.Series:
    """计算 MACD 指标中的 DIF (EMA fast - EMA slow)。"""
    ema_fast = df["close"].ewm(span=fast, adjust=False).mean()
    ema_slow = df["close"].ewm(span=slow, adjust=False).mean()
    return ema_fast - ema_slow


def bbi_deriv_uptrend(
    bbi: pd.Series,
    *,
    min_window: int,
    max_window: int | None = None,
    q_threshold: float = 0.0,
) -> bool:
    """
    判断 BBI 是否“整体上升”。

    令最新交易日为 T，在区间 [T-w+1, T]（w 自适应，w ≥ min_window 且 ≤ max_window）
    内，先将 BBI 归一化：BBI_norm(t) = BBI(t) / BBI(T-w+1)。

    再计算一阶差分 Δ(t) = BBI_norm(t) - BBI_norm(t-1)。  
    若 Δ(t) 的前 q_threshold 分位数 ≥ 0，则认为该窗口通过；只要存在
    **最长** 满足条件的窗口即可返回 True。q_threshold=0 时退化为
    “全程单调不降”（旧版行为）。

    Parameters
    ----------
    bbi : pd.Series
        BBI 序列（最新值在最后一位）。
    min_window : int
        检测窗口的最小长度。
    max_window : int | None
        检测窗口的最大长度；None 表示不设上限。
    q_threshold : float, default 0.0
        允许一阶差分为负的比例（0 ≤ q_threshold ≤ 1）。
    """
    if not 0.0 <= q_threshold <= 1.0:
        raise ValueError("q_threshold 必须位于 [0, 1] 区间内")

    bbi = bbi.dropna()
    if len(bbi) < min_window:
        return False

    longest = min(len(bbi), max_window or len(bbi))

    # 自最长窗口向下搜索，找到任一满足条件的区间即通过
    for w in range(longest, min_window - 1, -1):
        seg = bbi.iloc[-w:]                # 区间 [T-w+1, T]
        norm = seg / seg.iloc[0]           # 归一化
        diffs = np.diff(norm.values)       # 一阶差分
        if np.quantile(diffs, q_threshold) >= 0:
            return True
    return False


def _find_peaks(
    df: pd.DataFrame,
    *,
    column: str = "high",
    distance: Optional[int] = None,
    prominence: Optional[float] = None,
    height: Optional[float] = None,
    width: Optional[float] = None,
    rel_height: float = 0.5,
    **kwargs: Any,
) -> pd.DataFrame:
    
    if column not in df.columns:
        raise KeyError(f"'{column}' not found in DataFrame columns: {list(df.columns)}")

    y = df[column].to_numpy()

    indices, props = find_peaks(
        y,
        distance=distance,
        prominence=prominence,
        height=height,
        width=width,
        rel_height=rel_height,
        **kwargs,
    )

    peaks_df = df.iloc[indices].copy()
    peaks_df["is_peak"] = True

    # Flatten SciPy arrays into columns (only those with same length as indices)
    for key, arr in props.items():
        if isinstance(arr, (list, np.ndarray)) and len(arr) == len(indices):
            peaks_df[f"peak_{key}"] = arr

    return peaks_df


# --------------------------- Selector 类 --------------------------- #
class EnhancedBBIKDJSelector:
    """
    自适应 *BBI(导数)* + *KDJ* 选股器
        • BBI: 允许 bbi_q_threshold 比例的回撤
        • KDJ: J < threshold ；或位于历史 J 的 j_q_threshold 分位及以下
        • MACD: DIF > 0
        • 收盘价波动幅度 ≤ price_range_pct
    """

    def __init__(
        self,
        j_threshold: float = -5,
        bbi_min_window: int = 90,
        max_window: int = 90,
        price_range_pct: float = 100.0,
        bbi_q_threshold: float = 0.05,
        j_q_threshold: float = 0.10,
    ) -> None:
        self.j_threshold = j_threshold
        self.bbi_min_window = bbi_min_window
        self.max_window = max_window
        self.price_range_pct = price_range_pct
        self.bbi_q_threshold = bbi_q_threshold  # ← 原 q_threshold
        self.j_q_threshold = j_q_threshold      # ← 新增

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        hist = hist.copy()
        hist["BBI"] = compute_bbi(hist)

        # 0. 收盘价波动幅度约束（最近 max_window 根 K 线）
        win = hist.tail(self.max_window)
        high, low = win["close"].max(), win["close"].min()
        if low <= 0 or (high / low - 1) > self.price_range_pct:
            return False

        # 1. BBI 上升（允许部分回撤）
        if not bbi_deriv_uptrend(
            hist["BBI"],
            min_window=self.bbi_min_window,
            max_window=self.max_window,
            q_threshold=self.bbi_q_threshold,
        ):
            return False

        # 2. KDJ 过滤 —— 双重条件
        kdj = compute_kdj(hist)
        j_today = float(kdj.iloc[-1]["J"])

        # 最近 max_window 根 K 线的 J 分位
        j_window = kdj["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))

        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False

        # 3. MACD：DIF > 0
        hist["DIF"] = compute_dif(hist)
        return hist["DIF"].iloc[-1] > 0

    # ---------- 多股票批量 ---------- #
    def select(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 额外预留 20 根 K 线缓冲
            hist = hist.tail(self.max_window + 20)
            if self._passes_filters(hist):
                picks.append(code)
        return picks


class BBIKDJSelector:
    """
    自适应 *BBI(导数)* + *KDJ* 选股器
        • BBI: 允许 bbi_q_threshold 比例的回撤
        • KDJ: J < threshold ；或位于历史 J 的 j_q_threshold 分位及以下
        • MACD: DIF > 0
        • 收盘价波动幅度 ≤ price_range_pct
    """

    def __init__(
        self,
        j_threshold: float = -5,
        bbi_min_window: int = 90,
        max_window: int = 90,
        price_range_pct: float = 100.0,
        bbi_q_threshold: float = 0.05,
        j_q_threshold: float = 0.10,
    ) -> None:
        self.j_threshold = j_threshold
        self.bbi_min_window = bbi_min_window
        self.max_window = max_window
        self.price_range_pct = price_range_pct
        self.bbi_q_threshold = bbi_q_threshold  # ← 原 q_threshold
        self.j_q_threshold = j_q_threshold      # ← 新增

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        hist = hist.copy()
        hist["BBI"] = compute_bbi(hist)

        # 0. 收盘价波动幅度约束（最近 max_window 根 K 线）
        win = hist.tail(self.max_window)
        high, low = win["close"].max(), win["close"].min()
        if low <= 0 or (high / low - 1) > self.price_range_pct:
            return False

        # 1. BBI 上升（允许部分回撤）
        if not bbi_deriv_uptrend(
            hist["BBI"],
            min_window=self.bbi_min_window,
            max_window=self.max_window,
            q_threshold=self.bbi_q_threshold,
        ):
            return False

        # 2. KDJ 过滤 —— 双重条件
        kdj = compute_kdj(hist)
        j_today = float(kdj.iloc[-1]["J"])

        # 最近 max_window 根 K 线的 J 分位
        j_window = kdj["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))

        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False

        # 3. MACD：DIF > 0
        hist["DIF"] = compute_dif(hist)
        return hist["DIF"].iloc[-1] > 0

    # ---------- 多股票批量 ---------- #
    def select(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 额外预留 20 根 K 线缓冲
            hist = hist.tail(self.max_window + 20)
            if self._passes_filters(hist):
                picks.append(code)
        return picks



class PeakKDJSelector:
    """
    Peaks + KDJ 选股器    
    """

    def __init__(
        self,
        j_threshold: float = -5,
        max_window: int = 90,
        fluc_threshold: float = 0.03,
        gap_threshold: float = 0.02,
        j_q_threshold: float = 0.10,
    ) -> None:
        self.j_threshold = j_threshold
        self.max_window = max_window
        self.fluc_threshold = fluc_threshold  # 当日↔peak_(t-n) 波动率上限
        self.gap_threshold = gap_threshold    # oc_prev 必须高于区间最低收盘价的比例
        self.j_q_threshold = j_q_threshold

    # ---------- 单支股票过滤 ---------- #
        # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        if hist.empty:
            return False

        hist = hist.copy().sort_values("date")
        hist["oc_max"] = hist[["open", "close"]].max(axis=1)

        # 1. 提取 peaks
        peaks_df = _find_peaks(
            hist,
            column="oc_max",
            distance=6,
            prominence=0.5,
        )
        
        # 至少两个峰      
        date_today = hist.iloc[-1]["date"]
        peaks_df = peaks_df[peaks_df["date"] < date_today]
        if len(peaks_df) < 2:               
            return False

        peak_t = peaks_df.iloc[-1]          # 最新一个峰
        peaks_list = peaks_df.reset_index(drop=True)
        oc_t = peak_t.oc_max
        total_peaks = len(peaks_list)

        # 2. 回溯寻找 peak_(t-n)
        target_peak = None        
        for idx in range(total_peaks - 2, -1, -1):
            peak_prev = peaks_list.loc[idx]
            oc_prev = peak_prev.oc_max
            if oc_t <= oc_prev:             # 要求 peak_t > peak_(t-n)
                continue

            # 只有当“总峰数 ≥ 3”时才检查区间内其他峰 oc_max
            if total_peaks >= 3 and idx < total_peaks - 2:
                inter_oc = peaks_list.loc[idx + 1 : total_peaks - 2, "oc_max"]
                if not (inter_oc < oc_prev).all():
                    continue

            # 新增： oc_prev 高于区间最低收盘价 gap_threshold
            date_prev = peak_prev.date
            mask = (hist["date"] > date_prev) & (hist["date"] < peak_t.date)
            min_close = hist.loc[mask, "close"].min()
            if pd.isna(min_close):
                continue                    # 区间无数据
            if oc_prev <= min_close * (1 + self.gap_threshold):
                continue

            target_peak = peak_prev
            
            break

        if target_peak is None:
            return False

        # 3. 当日收盘价波动率
        close_today = hist.iloc[-1]["close"]
        fluc_pct = abs(close_today - target_peak.close) / target_peak.close
        if fluc_pct > self.fluc_threshold:
            return False

        # 4. KDJ 过滤
        kdj = compute_kdj(hist)
        j_today = float(kdj.iloc[-1]["J"])
        j_window = kdj["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))
        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False

        return True

    # ---------- 多股票批量 ---------- #
    def select(
        self,
        date: pd.Timestamp,
        data: Dict[str, pd.DataFrame],
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            hist = hist.tail(self.max_window + 20)  # 额外缓冲
            if self._passes_filters(hist):
                picks.append(code)
        return picks
    

class BBIShortLongSelector:
    """
    BBI 上升 + 短/长期 RSV 条件 + DIF > 0 选股器
    """
    def __init__(
        self,
        n_short: int = 3,
        n_long: int = 21,
        m: int = 3,
        bbi_min_window: int = 90,
        max_window: int = 150,
        bbi_q_threshold: float = 0.05,
    ) -> None:
        if m < 2:
            raise ValueError("m 必须 ≥ 2")
        self.n_short = n_short
        self.n_long = n_long
        self.m = m
        self.bbi_min_window = bbi_min_window
        self.max_window = max_window
        self.bbi_q_threshold = bbi_q_threshold   # 新增参数

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        hist = hist.copy()
        hist["BBI"] = compute_bbi(hist)

        # 1. BBI 上升（允许部分回撤）
        if not bbi_deriv_uptrend(
            hist["BBI"],
            min_window=self.bbi_min_window,
            max_window=self.max_window,
            q_threshold=self.bbi_q_threshold,
        ):
            return False

        # 2. 计算短/长期 RSV -----------------
        hist["RSV_short"] = compute_rsv(hist, self.n_short)
        hist["RSV_long"] = compute_rsv(hist, self.n_long)

        if len(hist) < self.m:
            return False                        # 数据不足

        win = hist.iloc[-self.m :]              # 最近 m 天
        long_ok = (win["RSV_long"] >= 80).all() # 长期 RSV 全 ≥ 80

        short_series = win["RSV_short"]
        short_start_end_ok = (
            short_series.iloc[0] >= 80 and short_series.iloc[-1] >= 80
        )
        short_has_below_20 = (short_series < 20).any()

        if not (long_ok and short_start_end_ok and short_has_below_20):
            return False

        # 3. MACD：DIF > 0 -------------------
        hist["DIF"] = compute_dif(hist)
        return hist["DIF"].iloc[-1] > 0

    # ---------- 多股票批量 ---------- #
    def select(
        self,
        date: pd.Timestamp,
        data: Dict[str, pd.DataFrame],
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 预留足够长度：RSV 计算窗口 + BBI 检测窗口 + m
            need_len = (
                max(self.n_short, self.n_long)
                + self.bbi_min_window
                + self.m
            )
            hist = hist.tail(max(need_len, self.max_window))
            if self._passes_filters(hist):
                picks.append(code)
        return picks


class BreakoutVolumeKDJSelector:
    """
    放量突破 + KDJ + DIF>0 + 收盘价波动幅度 选股器   
    """

    def __init__(
        self,
        j_threshold: float = 0.0,
        up_threshold: float = 3.0,
        volume_threshold: float = 2.0 / 3,
        offset: int = 15,
        max_window: int = 120,
        price_range_pct: float = 10.0,
        j_q_threshold: float = 0.10,        # ← 新增
    ) -> None:
        self.j_threshold = j_threshold
        self.up_threshold = up_threshold
        self.volume_threshold = volume_threshold
        self.offset = offset
        self.max_window = max_window
        self.price_range_pct = price_range_pct
        self.j_q_threshold = j_q_threshold  # ← 新增

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        if len(hist) < self.offset + 2:
            return False

        hist = hist.tail(self.max_window).copy()

        # ---- 收盘价波动幅度约束 ----
        high, low = hist["close"].max(), hist["close"].min()
        if low <= 0 or (high / low - 1) > self.price_range_pct:
            return False

        # ---- 技术指标 ----
        hist = compute_kdj(hist)
        hist["pct_chg"] = hist["close"].pct_change() * 100
        hist["DIF"] = compute_dif(hist)

        # 0) 指定日约束：J < j_threshold 或位于历史分位；且 DIF > 0
        j_today = float(hist["J"].iloc[-1])

        j_window = hist["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))

        # 若不满足任一 J 条件，则淘汰
        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False
        if hist["DIF"].iloc[-1] <= 0:
            return False

        # ---- 放量突破条件 ----
        n = len(hist)
        wnd_start = max(0, n - self.offset - 1)
        last_idx = n - 1

        for t_idx in range(wnd_start, last_idx):  # 探索突破日 T
            row = hist.iloc[t_idx]

            # 1) 单日涨幅
            if row["pct_chg"] < self.up_threshold:
                continue

            # 2) 相对放量
            vol_T = row["volume"]
            if vol_T <= 0:
                continue
            vols_except_T = hist["volume"].drop(index=hist.index[t_idx])
            if not (vols_except_T <= self.volume_threshold * vol_T).all():
                continue

            # 3) 创新高
            if row["close"] <= hist["close"].iloc[:t_idx].max():
                continue

            # 4) T 之后 J 值维持高位
            if not (hist["J"].iloc[t_idx:last_idx] > hist["J"].iloc[-1] - 10).all():
                continue

            return True  # 满足所有条件

        return False

    # ---------- 多股票批量 ---------- #
    def select(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            if self._passes_filters(hist):
                picks.append(code)
        return picks


# --------------------------- 极致缩量策略 --------------------------- #
class JZVolumeShrinkSelector:
    """
    极致缩量策略：
    1. A点-B点：N1日内最低点到最高点涨幅≥rise_pct
    2. B点-C点：B点后N2日内最高点到最低点跌幅≥fall_pct
    3. D点：C点后出现小放量上影线K线
    4. E点：D点后出现极致缩量阴线，成交量低于均量和D点，收盘价<开盘价
    所有参数均可配置。
    """
    _instance = None
    _log_file = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(JZVolumeShrinkSelector, cls).__new__(cls)
            # 创建logs文件夹
            cls._instance.log_dir = 'logs'
            if not os.path.exists(cls._instance.log_dir):
                os.makedirs(cls._instance.log_dir)
            # 创建日志文件
            now = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            cls._instance._log_file = os.path.join(cls._instance.log_dir, f'JZVolumeShrinkSelector_{now}.log')
        return cls._instance
    
    def __init__(
        self,
        rise_pct, # A点-B点：N1日内最低点到最高点涨幅≥rise_pct
        fall_pct, # B点-C点：B点-E后最高点到最低点跌幅≥fall_pct
        n1,
        a_consolidation_days,
        a_consolidation_range,
        a_downward_min_pct,
        a_downward_max_pct,
        a_downward_range_multiplier,
        d_vol_ratio,
        d_vol_max_ratio,
        upper_shadow_ratio,
        e_vol_vs_cd_avg_ratio,
        e_vs_d_vol_ratio,
        de_max_days,
        e_yang_threshold,
        shadow_ratio,
        ce_rise_ratio,
        ab_max_days,
        cd_max_distance_trade_days: int,  # C-D点最大距离天数
        d_lookback_trade_days: int,  # D点放量比较的向前查找天数
    ) -> None:
        # 参数有效性检查
        params = locals()
        for k, v in params.items():
            if k != 'self' and v is None:
                raise ValueError(f"参数 {k} 不能为空")
        self.rise_pct = rise_pct
        self.fall_pct = fall_pct
        self.n1 = n1
        self.a_consolidation_days = a_consolidation_days
        self.a_consolidation_range = a_consolidation_range
        self.a_downward_min_pct = a_downward_min_pct
        self.a_downward_max_pct = a_downward_max_pct
        self.a_downward_range_multiplier = a_downward_range_multiplier
        self.d_vol_ratio = d_vol_ratio
        self.d_vol_max_ratio = d_vol_max_ratio
        self.upper_shadow_ratio = upper_shadow_ratio
        self.e_vol_vs_cd_avg_ratio = e_vol_vs_cd_avg_ratio
        self.e_vs_d_vol_ratio = e_vs_d_vol_ratio
        self.de_max_days = de_max_days
        self.e_yang_threshold = e_yang_threshold
        self.shadow_ratio = shadow_ratio
        self.ce_rise_ratio = ce_rise_ratio
        self.ab_max_days = ab_max_days
        self.cd_max_distance_trade_days = cd_max_distance_trade_days
        self.d_lookback_trade_days = d_lookback_trade_days

    def log(self, msg: str):
        try:
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with open(self._log_file, 'a', encoding='utf-8') as f:
                f.write(f'{timestamp} - {msg}\n')
        except Exception as e:
            print(f"日志写入错误: {e}, 文件: {self._log_file}")
            print(f"消息: {msg}")

    def _check_ce_max_rise(self, hist: pd.DataFrame, b_idx: int, c_idx: int, e_idx: int) -> bool:
        """
        检查C-E区间价格最大值不能超过B点最大值减去(B点最大值- C点最小值)*ce_rise_ratio + C点最小值
        """
        c_price = hist.loc[c_idx, 'low']  # C点最小值
        b_price = hist.loc[b_idx, 'high']  # B点最大值
        ce_data = hist.loc[c_idx+1:e_idx]
        if ce_data.empty or c_price <= 0 or b_price <= 0:
            return True

        # C-E区间实体最大值
        peak_price = max(ce_data['close'].max(), ce_data['open'].max())

        # 计算阈值
        ce_max_allowed_price = c_price + (b_price - c_price) * self.ce_rise_ratio
        self.log(f'C-E区间检查: B点最大值={b_price:.4f}, C点最小值={c_price:.4f}, 区间最高点={peak_price:.4f}, 允许阈值={ce_max_allowed_price:.4f}, 涨幅比例={self.ce_rise_ratio:.4f}')
        return peak_price <= ce_max_allowed_price

    def _find_d_point(self, hist: pd.DataFrame, c_idx: int, e_idx: int) -> tuple[int, int]:
        """
        在C点和E点之间查找轻微放量试盘上影线D点
        如果D点距离C点太远，则使用D点前N天的均值作为比较基准
        返回: (d_idx, new_c_idx)
        """
        d_idx = None
        new_c_idx = c_idx  # 默认使用原始C点
        # D点和E点间隔不能超过de_max_days天
        d_search_start = e_idx - 1
        d_search_end = max(c_idx + 1, d_search_start - self.de_max_days + 1)
        
        for i in range(d_search_start, d_search_end - 1, -1):
            if i < c_idx + 1:
                break

            d_idx = i
            row = hist.loc[d_idx]
            d_date = row['date']
            entity = abs(row['close'] - row['open'])
            # 当天K线内部比例
            upper_shadow = (row['high'] - max(row['close'], row['open'])) / row['open'] if row['open'] != 0 else 0
            lower_shadow = (min(row['close'], row['open']) - row['low']) / row['open'] if row['open'] != 0 else 0
            entity_pct = entity / row['open'] if row['open'] != 0 else 0

            # 计算D点距离C点的天数
            cd_distance = d_idx - c_idx
            
            # 根据距离决定使用哪个区间的均值
            if cd_distance > self.cd_max_distance_trade_days:
                # 距离太远，使用D点前d_lookback_days天的均值
                lookback_start = max(0, d_idx - self.d_lookback_trade_days)
                vol_data = hist.loc[lookback_start:d_idx-1]
                vol_mean = vol_data['volume'].mean()
                # 更新C点为D点前d_lookback_days天的位置
                new_c_idx = lookback_start
                self.log(f'D点候选idx: {d_idx}, 日期: {d_date}, 距离C点{cd_distance}天 > {self.cd_max_distance_trade_days}天，使用前{self.d_lookback_trade_days}天均值，更新C点idx为{new_c_idx},新C点为{hist.loc[new_c_idx, "date"]}')
                self.log(f'  D成交量: {row["volume"]:.0f}, [{vol_data.iloc[0]["date"]} - {vol_data.iloc[-1]["date"]}]均量: {vol_mean:.0f}')
            else:
                # 距离合适，使用C-D区间的均值
                between_c_d = hist.loc[c_idx:d_idx-1]
                vol_mean = between_c_d['volume'].mean()
                self.log(f'D点候选idx: {d_idx}, 日期: {d_date}, 距离C点{cd_distance}天 <= {self.cd_max_distance_trade_days}天，使用C-D区间均值')
                self.log(f'  D成交量: {row["volume"]:.0f}, [{between_c_d.iloc[0]["date"]} - {between_c_d.iloc[-1]["date"]}]均量: {vol_mean:.0f}')

            # 判断各个条件
            vol_condition1 = row['volume'] > vol_mean * self.d_vol_ratio
            vol_condition2 = row['volume'] < vol_mean * self.d_vol_max_ratio
            shadow_condition1 = upper_shadow >= entity_pct * self.upper_shadow_ratio
            shadow_condition2 = upper_shadow >= lower_shadow * self.shadow_ratio  # 上影线比例是下影线比例的N倍
            
            self.log(f'  成交量条件1(>均量* {self.d_vol_ratio:.0f}): {vol_condition1}, 条件2(<均量* {self.d_vol_max_ratio:.0f}): {vol_condition2}')
            self.log(f'  实体比例: {entity_pct:.8f}, 上影线比例: {upper_shadow:.8f}, 下影线比例: {lower_shadow:.8f}')
            self.log(f'  上影线条件(>={entity_pct * self.upper_shadow_ratio:.8f}): {shadow_condition1}, 上影线比例>下影线比例*{self.shadow_ratio:.2f}: {shadow_condition2}')
            
            if vol_condition1 and vol_condition2 and shadow_condition1 and shadow_condition2:
                self.log(f'  ✓ 找到D点: {d_idx}, 日期: {d_date}')
                return d_idx, new_c_idx
            else:
                self.log(f'  ✗ 不符合D点条件')
                if not vol_condition1:
                    self.log(f'    原因: 成交量不足 (需要>{vol_mean * self.d_vol_ratio:.0f})')
                elif not vol_condition2:
                    self.log(f'    原因: 成交量过大 (需要<{vol_mean * self.d_vol_max_ratio:.0f})')
                elif not shadow_condition1:
                    self.log(f'    原因: 上影线比例不足 (需要>={entity_pct * self.upper_shadow_ratio:.4f})')
                elif not shadow_condition2:
                    self.log(f'    原因: 上影线比例不足下影线比例的{self.shadow_ratio:.2f}倍 (上影线比例={upper_shadow:.8f}, 下影线比例*{self.shadow_ratio:.2f}={lower_shadow*self.shadow_ratio:.8f})')
        
        return None, None

    def _check_e_price_condition(self, hist: pd.DataFrame, e_idx: int) -> bool:
        """
        检查E点的价格条件
        1. 阴线或小阳线（收盘价<开盘价 或 涨幅<e_yang_threshold）
        2. 今日收盘价比昨日收盘价的涨幅不超过e_yang_threshold
        """
        e_row = hist.loc[e_idx]
        
        # 条件1：阴线或小阳线
        price_condition1 = e_row['close'] < e_row['open'] or (e_row['close'] - e_row['open']) / e_row['open'] < self.e_yang_threshold
        
        # 条件2：今日收盘价比昨日收盘价的涨幅不超过e_yang_threshold
        price_condition2 = (e_row['close'] - hist.loc[e_idx-1, 'close']) / hist.loc[e_idx-1, 'close'] < self.e_yang_threshold
        
        price_condition = price_condition1 or price_condition2
        
        if not price_condition:
            self.log(f'E点价格条件不满足: 日期={e_row["date"]}, 开盘={e_row["open"]:.4f}, 收盘={e_row["close"]:.4f}, 昨日收盘={hist.loc[e_idx-1, "close"]:.4f}')
            self.log(f'  条件1(阴线或小阳): {price_condition1}, 条件2(涨幅限制): {price_condition2}')
        
        return price_condition

    def _find_e_point(self, hist: pd.DataFrame, c_idx: int, d_idx: int, e_idx: int) -> bool:
        """
        判断E点是否是缩量洗盘线
        使用更新后的C点计算C-D区间均值
        """
        e_row = hist.loc[e_idx]
        e_date = hist.loc[e_idx, 'date']
        
        # 重新计算C-D区间的均量（用于E点判断）
        c2d_data = hist.loc[c_idx+1:d_idx]
        c2d_vol_mean = c2d_data['volume'].mean()
        d_vol = hist.loc[d_idx, 'volume']
        
        self.log(f'E点检查: idx={e_idx}, 日期={e_date}, 成交量={e_row["volume"]:.0f}, C-D均量={c2d_vol_mean:.0f}, D点量={d_vol:.0f}, 收盘={e_row["close"]:.4f}, 开盘={e_row["open"]:.4f}')

        # E点缩量条件改为与C-D均量比较
        # 判断E点条件
        vol_condition1 = e_row['volume'] < c2d_vol_mean * self.e_vol_vs_cd_avg_ratio
        vol_condition2 = e_row['volume'] < d_vol * self.e_vs_d_vol_ratio
        price_condition = self._check_e_price_condition(hist, e_idx)
        
        self.log(f'E点条件检查: 缩量1(C-D成交量均值*{self.e_vol_vs_cd_avg_ratio:.2f}：{c2d_vol_mean * self.e_vol_vs_cd_avg_ratio:.0f})={vol_condition1}, 缩量2(D点成交量*{self.e_vs_d_vol_ratio:.2f}：{d_vol * self.e_vs_d_vol_ratio:.0f})={vol_condition2}, 价格条件={price_condition}')
    
        # 判断E点为缩量涨停线（最高价和最低价涨跌比例小于0.1%）
        if e_row['low'] > 0:
            limit_line = (e_row['high'] - e_row['low']) / e_row['low']
            if limit_line < 0.001:
                self.log(f'E点为缩量涨停线（高低价涨跌比例仅{limit_line:.4%}），跳过。')
                return False

        if vol_condition1 and vol_condition2 and price_condition:
            self.log(f'✓ E点符合条件: idx={e_idx}, 日期={e_date}')
            return True
        else:
            self.log(f'✗ E点不符合条件')
            if not vol_condition1:
                self.log(f'  原因: 成交量过大 (需要<{c2d_vol_mean * self.e_vol_vs_cd_avg_ratio:.0f})')
            elif not vol_condition2:
                self.log(f'  原因: 成交量过大 (需要<{d_vol * self.e_vs_d_vol_ratio:.0f})')
            elif not price_condition:
                self.log(f'  原因: 不是阴线或小阳线')
            return False

    def _is_consolidation(self, data: pd.DataFrame) -> bool:
        """
        判断一段数据是否为区间震荡
        条件：最高价/最低价 - 1 <= a_consolidation_range
        """
        if len(data) < 3:  # 至少需要3天数据
            return False
        high = data['high'].max()
        low = data['low'].min()
        if low <= 0:
            return False
        range_pct = (high / low - 1)
        self.log(f'区间震荡检查: 日期[{data.iloc[0]["date"]} - {data.iloc[-1]["date"]}], 最高价={high:.4f}, 最低价={low:.4f}, 震荡幅度={range_pct:.4f}, 阈值={self.a_consolidation_range}')
        return range_pct <= self.a_consolidation_range

    def _is_consolidation_downward(self, data: pd.DataFrame) -> bool:
        """
        判断一段数据是否为震荡下行区间
        条件：
        1. 整体趋势向下（收盘价趋势）
        2. 但波动幅度仍然可控（最高价/最低价 - 1 <= a_consolidation_range * 1.5）
        3. 下跌幅度不超过阈值（首日收盘价/末日收盘价 - 1 <= 0.2）
        """
        if len(data) < 3:  # 至少需要3天数据
            return False
            
        # 计算整体趋势（首日到末日的收盘价变化）
        first_close = data.iloc[0]['close']
        last_close = data.iloc[-1]['close']
        if first_close <= 0:
            return False
            
        trend_pct = (first_close - last_close) / first_close  # 下跌幅度
        
        # 计算波动幅度
        high = data['high'].max()
        low = data['low'].min()
        if low <= 0:
            return False
        range_pct = (high / low - 1)
        
        # 判断条件：
        # 1. 整体趋势向下（下跌幅度在配置范围内）
        # 2. 波动幅度在可接受范围内
        is_downward = trend_pct > self.a_downward_min_pct and trend_pct <= self.a_downward_max_pct
        is_controlled_range = range_pct <= self.a_consolidation_range * self.a_downward_range_multiplier
        
        self.log(f'震荡下行检查: 趋势跌幅={trend_pct:.4f}, 波动幅度={range_pct:.4f}, 阈值={self.a_consolidation_range * self.a_downward_range_multiplier:.4f}, 是否下行={is_downward}, 是否可控={is_controlled_range}')
        
        return is_downward and is_controlled_range

    def _find_uptrend_start(self, hist: pd.DataFrame, b_idx: int) -> int:
        """
        从B点往前遍历，找到主升浪的起点A点
        A点的特征：A点前N天是震荡，A点后开始上涨，且A-B涨幅满足要求
        """
        self.log(f'开始寻找A点: 从B点({b_idx})往前搜索...')
        
        b_price = hist.loc[b_idx, 'high']  # B点价格
        self.log(f'B点价格: {b_price:.4f}')
       
        # 从B点往前遍历，寻找A点
        # 搜索范围：从B点往前最多搜索n1天，确保能找到n1区间内的A点
        max_lookback = min(b_idx, self.n1)  # 最多往前搜索n1天，但不能超过数据边界
        
        self.log(f'A点搜索区间: [{hist.loc[max(0, b_idx - max_lookback), "date"]} - {hist.loc[b_idx, "date"]}]')
        
        for i in range(b_idx - 1, max(0, b_idx - max_lookback), -1):
            # 检查i点前a_consolidation_days天是否为震荡
            if i < self.a_consolidation_days:
                continue
            
            # 如果 A 点当日涨幅大于 9%，认为A点涨幅过大可能是第一个涨停板不能作为主升浪起点，则跳过
            # 如果 A 点最高涨幅和昨日收盘比涨幅大于 9%，认为A点涨幅过大可能是第一个涨停板不能作为主升浪起点，则跳过
            if hist.loc[i, 'high'] > hist.loc[i, 'low'] * 1.09 or (hist.loc[i, 'high'] - hist.loc[i-1, 'close']) / hist.loc[i-1, 'close'] > 0.09:
                self.log(f'候选点 A点 {hist.loc[i, "date"]} 涨幅过大，可能是第一个涨停板不能作为主升浪起点，跳过')
                continue
            
            
            consolidation_data = hist.loc[i-self.a_consolidation_days:i]
            self.log(f'  检查候选点{i}: 区间长度={len(consolidation_data)}, 日期[{hist.loc[i-self.a_consolidation_days, "date"]} - {hist.loc[i, "date"]}]')

            is_consolidation = self._is_consolidation(consolidation_data)
            is_consolidation_downward = self._is_consolidation_downward(consolidation_data)
            
            self.log(f'  检查结果: 区间震荡={is_consolidation}, 震荡下行={is_consolidation_downward}')

            if is_consolidation or is_consolidation_downward:
                # 找到满足震荡条件的候选A点，现在检查A-B涨幅
                a_price = hist.loc[i, 'low']  # 使用当日最低价作为A点价格
                ab_rise = (b_price - a_price) / a_price
                
                self.log(f'  候选A点涨幅检查: A点价格={a_price:.4f}, B点价格={b_price:.4f}, 涨幅={ab_rise:.4f}, 阈值={self.rise_pct}')
                
                if ab_rise >= self.rise_pct:
                    # 检查A-B点之间的间隔天数
                    a_date = hist.loc[i, 'date']
                    b_date = hist.loc[b_idx, 'date']
                    ab_days = (b_date - a_date).days
                    
                    self.log(f'  候选A点间隔检查: A点日期={a_date}, B点日期={b_date}, 间隔天数={ab_days}, 最大允许天数={self.ab_max_days}')
                    
                    if ab_days <= self.ab_max_days:
                        pattern_type = "区间震荡" if is_consolidation else "震荡下行"
                        self.log(f'找到满足条件的A点: idx={i}, 日期={a_date}, 前区间类型={pattern_type}, A-B涨幅={ab_rise:.4f}, 间隔天数={ab_days}')
                        return i
                    else:
                        self.log(f'  候选A点间隔过长: {ab_days}天 > {self.ab_max_days}天')
                else:
                    self.log(f'  候选A点涨幅不达标: {ab_rise:.4f} < {self.rise_pct}')

        self.log('未找到满足条件的A点')
        return -1

    def _passes_filters(self, hist: pd.DataFrame, code: str = '', date: str = '') -> bool:
        # 添加调用次数统计，避免重复处理
        if not hasattr(self, '_call_count'):
            self._call_count = 0
        self._call_count += 1
        
        self.log(f'\n=== 第{self._call_count}次调用 ===')
        self.log(f'股票: {code} 日期: {date} 总K线数: {len(hist)}')
        
        if len(hist) < self.n1 + 10:
            self.log(f'K线数量不足: {len(hist)} < {self.n1 + 10}')
            return False
        
        hist = hist.copy().reset_index(drop=True)
        
        # E点就是传入的日期（今天）
        e_idx = len(hist) - 1  # 最后一天
        e_date = hist.loc[e_idx, 'date']
        self.log(f'E点idx: {e_idx}, 日期: {e_date} (传入的日期)')
        if str(date) != str(e_date):
            self.log(f'传入日期不存在，传入日期 {date} 与 E点日期 {e_date} 不匹配')
            return False
        
        # 提前检查E点价格条件
        if not self._check_e_price_condition(hist, e_idx):
            self.log(f'E点价格条件不满足，跳过')
            return False
        
        # 1. 获取E点前60个交易日的数据
        search_window = hist.tail(self.n1 + 1)  # 包含E点在内的n1+1天
        search_window = search_window.iloc[:-1]  # 排除E点，只取前n1天
        
        self.log(f'搜索窗口: 从{search_window.iloc[0]["date"]}到{search_window.iloc[-1]["date"]}, 共{len(search_window)}天')
        
        # 2. 在搜索窗口内找B点（最高点）
        b_idx = search_window['high'].idxmax()
        b_date = search_window.loc[b_idx, 'date']
        b_price = search_window.loc[b_idx, 'high']
        self.log(f'B点idx: {b_idx}, 日期: {b_date}, 价格: {b_price:.4f} (最高点)')
        
        # 3. 从B点往前寻找主升浪起点A点（包含A-B涨幅检查）
        a_idx = self._find_uptrend_start(hist, b_idx)
        if a_idx == -1:
            return False
            
        a_date = hist.loc[a_idx, 'date']
        a_price = hist.loc[a_idx, 'low']  # 使用当日最低价作为A点价格
        self.log(f'A点idx: {a_idx}, 日期: {a_date}, 价格: {a_price:.4f} (主升浪起点)')
            
        # 4. 在B点和E点之间寻找最低点C点
        between_b_e = hist.loc[b_idx+1:e_idx-1]  # B点后到E点前
        if between_b_e.empty:
            self.log('B点到E点间无数据')
            return False
            
        c_idx = between_b_e['low'].idxmin()
        c_date = between_b_e.loc[c_idx, 'date']
        c_price = between_b_e.loc[c_idx, 'low']
        self.log(f'C点idx: {c_idx}, 日期: {c_date}, 价格: {c_price:.4f} (B到E间最低点)')
        
        # # 如果C点最低价小于A点最高价，认为没有主力维护股价，下跌趋势没有企稳，直接跳过
        # a_high = max(hist.loc[a_idx, 'open'], hist.loc[a_idx, 'close'])
        # if c_price < a_high:
        #     self.log(f'C点最低价({c_price:.4f}) < A点最高价({a_high:.4f})，无主力维护，趋势未企稳，跳过')
        #     return False

        # 5. 判断C点和B点之间是否有一定的跌幅
        bc_fall = (b_price - c_price) / b_price
        self.log(f'B-C跌幅检查: B点价格={b_price:.4f}, C点价格={c_price:.4f}, 跌幅={bc_fall:.4f}, 阈值={self.fall_pct}')
        
        if bc_fall < self.fall_pct:
            self.log('B-C跌幅不达标')
            return False
        
        # 6. 检查C-E区间最高点涨幅，避免第二次主升浪
        if not self._check_ce_max_rise(hist, b_idx, c_idx, e_idx):
            self.log('C-E区间最高点涨幅过大，可能形成第二次主升浪')
            return False
        else:
            self.log('C-E区间最高点涨幅在合理范围内')        

        # 7. 在C点和E点之间查找轻微放量试盘上影线D点
        d_idx, new_c_idx = self._find_d_point(hist, c_idx, e_idx)
        # 检查D点是否是阳线
        if d_idx is None or d_idx is not None and hist.loc[d_idx, 'close'] > hist.loc[d_idx, 'open']:
            return False
        
        # # 检查 C点-D点之间是否有涨停
        # for i in range(c_idx, d_idx):
        #     if hist.loc[i, 'high'] > hist.loc[i, 'low'] * 1.09 or (hist.loc[i, 'high'] - hist.loc[i-1, 'close']) / hist.loc[i-1, 'close'] > 0.09:
        #         self.log(f'C点-D点之间有涨停，不考虑: {hist.loc[i, "date"]}')
        #         return False

        # 如果C点被更新，记录日志
        if new_c_idx != c_idx:
            self.log(f'C点已更新: 原始C点idx={c_idx}, 新C点idx={new_c_idx}, 日期={hist.loc[new_c_idx, "date"]}')
            c_idx = new_c_idx
            c_date = hist.loc[c_idx, 'date']
            
        # 8. 判断E点是否是缩量洗盘线
        if not self._find_e_point(hist, c_idx, d_idx, e_idx):
            self.log('E点条件不满足')
            return False
            
        self.log(f'选出E点: idx={e_idx}, 日期={e_date}, 股票={code}')
        self.log(f'完整路径: A点({a_date}) -> B点({b_date}) -> C点({c_date}) -> D点({hist.loc[d_idx, "date"]}) -> E点({e_date})')
        
        # 添加详细的参数信息用于进一步筛选
        self.log(f'=== 详细参数信息 ===')
        
        self.log(f'股票代码: {code}')

        # 各点基本信息
        a_row = hist.loc[a_idx]
        b_row = hist.loc[b_idx]
        c_row = hist.loc[c_idx]
        d_row = hist.loc[d_idx]
        e_row = hist.loc[e_idx]
        
        # A点信息
        a_entity_pct = (a_row['close'] - a_row['open']) / a_row['open'] if a_row['open'] != 0 else 0
        a_amplitude = (a_row['high'] - a_row['low']) / a_row['low'] if a_row['low'] != 0 else 0
        self.log(f'A点: 日期={a_date}, 开盘={a_row["open"]:.4f}, 收盘={a_row["close"]:.4f}, 最高={a_row["high"]:.4f}, 最低={a_row["low"]:.4f}, 成交量={a_row["volume"]:.0f}, 实体涨跌幅={a_entity_pct:.4f}, 价格振幅={a_amplitude:.4f}')
        
        # B点信息
        b_entity_pct = (b_row['close'] - b_row['open']) / b_row['open'] if b_row['open'] != 0 else 0
        b_amplitude = (b_row['high'] - b_row['low']) / b_row['low'] if b_row['low'] != 0 else 0
        self.log(f'B点: 日期={b_date}, 开盘={b_row["open"]:.4f}, 收盘={b_row["close"]:.4f}, 最高={b_row["high"]:.4f}, 最低={b_row["low"]:.4f}, 成交量={b_row["volume"]:.0f}, 实体涨跌幅={b_entity_pct:.4f}, 价格振幅={b_amplitude:.4f}')
        
        # C点信息
        c_entity_pct = (c_row['close'] - c_row['open']) / c_row['open'] if c_row['open'] != 0 else 0
        c_amplitude = (c_row['high'] - c_row['low']) / c_row['low'] if c_row['low'] != 0 else 0
        self.log(f'C点: 日期={c_date}, 开盘={c_row["open"]:.4f}, 收盘={c_row["close"]:.4f}, 最高={c_row["high"]:.4f}, 最低={c_row["low"]:.4f}, 成交量={c_row["volume"]:.0f}, 实体涨跌幅={c_entity_pct:.4f}, 价格振幅={c_amplitude:.4f}')
        
        # D点信息
        d_date = hist.loc[d_idx, 'date']
        d_entity_pct = (d_row['close'] - d_row['open']) / d_row['open'] if d_row['open'] != 0 else 0
        d_amplitude = (d_row['high'] - d_row['low']) / d_row['low'] if d_row['low'] != 0 else 0
        self.log(f'D点: 日期={d_date}, 开盘={d_row["open"]:.4f}, 收盘={d_row["close"]:.4f}, 最高={d_row["high"]:.4f}, 最低={d_row["low"]:.4f}, 成交量={d_row["volume"]:.0f}, 实体涨跌幅={d_entity_pct:.4f}, 价格振幅={d_amplitude:.4f}')
        
        # E点信息
        e_entity_pct = (e_row['close'] - e_row['open']) / e_row['open'] if e_row['open'] != 0 else 0
        e_amplitude = (e_row['high'] - e_row['low']) / e_row['low'] if e_row['low'] != 0 else 0
        self.log(f'E点: 日期={e_date}, 开盘={e_row["open"]:.4f}, 收盘={e_row["close"]:.4f}, 最高={e_row["high"]:.4f}, 最低={e_row["low"]:.4f}, 成交量={e_row["volume"]:.0f}, 实体涨跌幅={e_entity_pct:.4f}, 价格振幅={e_amplitude:.4f}')
        
        # 区间信息
        # A-B区间
        ab_rise = (b_price - a_price) / a_price
        ab_days = (b_date - a_date).days
        self.log(f'A-B区间: 涨幅={ab_rise:.4f}, 时间间隔={ab_days}天')
        
        # B-C区间
        bc_fall = (b_price - c_price) / b_price
        bc_days = (c_date - b_date).days
        self.log(f'B-C区间: 跌幅={bc_fall:.4f}, 时间间隔={bc_days}天')
        
        # C-D区间
        cd_rise = (d_row['close'] - c_row['close']) / c_row['close'] if c_row['close'] != 0 else 0
        cd_days = (d_date - c_date).days
        self.log(f'C-D区间: 涨幅={cd_rise:.4f}, 时间间隔={cd_days}天')
        
        # D-E区间
        de_rise = (e_row['close'] - d_row['close']) / d_row['close'] if d_row['close'] != 0 else 0
        de_days = (e_date - d_date).days
        self.log(f'D-E区间: 涨幅={de_rise:.4f}, 时间间隔={de_days}天')
        
        # 成交量比例信息
        # 重新计算C-D区间成交量均值
        c2d_data = hist.loc[c_idx+1:d_idx]
        c2d_vol_mean = c2d_data['volume'].mean()
        
        # D点成交量比例
        d_vol_ratio_vs_cd = d_row['volume'] / c2d_vol_mean if c2d_vol_mean != 0 else 0
        self.log(f'D点成交量/C-D均量={d_vol_ratio_vs_cd:.2f}')
              
        # D点上影线比例
        d_upper_shadow = (d_row['high'] - max(d_row['close'], d_row['open'])) / d_row['open'] if d_row['open'] != 0 else 0
        self.log(f'D点上影线涨幅={d_upper_shadow:.2f}')

        # D点上影线比例
        d_upper_shadow_vs_entity = d_upper_shadow / d_entity_pct if d_entity_pct != 0 else 0
        self.log(f'D点上影线和实体比例上影线/实体={d_upper_shadow_vs_entity:.2f}')

        # E点成交量比例
        e_vol_ratio_vs_cd = e_row['volume'] / c2d_vol_mean if c2d_vol_mean != 0 else 0
        e_vol_ratio_vs_d = e_row['volume'] / d_row['volume'] if d_row['volume'] != 0 else 0
        self.log(f'E点成交量/C-D均量={e_vol_ratio_vs_cd:.2f}, E点成交量/D点成交量={e_vol_ratio_vs_d:.2f}')
        
        # E点的 KDJ的J值
        kdj = compute_kdj(hist)
        self.log(f'E点的J值={kdj.loc[e_idx, "J"]:.2f}')
        
        # E点J值相对D点J值的涨幅
        e_vs_d_j_pct = (kdj.loc[e_idx, "J"] - kdj.loc[d_idx, "J"]) / kdj.loc[d_idx, "J"] if kdj.loc[d_idx, "J"] != 0 else 0
        self.log(f'E点J值相对D点J值的涨幅={e_vs_d_j_pct:.2f}')
        
        # E点相对D点收盘价的涨幅
        e_vs_d_pct = (e_row['close'] - d_row['close']) / d_row['close'] if d_row['close'] != 0 else 0
        self.log(f'E点相对D点收盘价的涨幅={e_vs_d_pct:.2f}')
        
        self.log(f'=== 参数信息结束 ===')
        
        return True

    def select(self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]) -> List[str]:
        picks: List[str] = []

        # # 第一次调用时打印全部参数
        # self.log(f'\n=== JZVolumeShrinkSelector 参数配置 ===')
        # self.log(f'rise_pct: {self.rise_pct}')
        # self.log(f'fall_pct: {self.fall_pct}')
        # self.log(f'n1: {self.n1}')
        # self.log(f'a_consolidation_days: {self.a_consolidation_days}')
        # self.log(f'a_consolidation_range: {self.a_consolidation_range}')
        # self.log(f'a_downward_min_pct: {self.a_downward_min_pct}')
        # self.log(f'a_downward_max_pct: {self.a_downward_max_pct}')
        # self.log(f'a_downward_range_multiplier: {self.a_downward_range_multiplier}')
        # self.log(f'd_vol_ratio: {self.d_vol_ratio}')
        # self.log(f'd_vol_max_ratio: {self.d_vol_max_ratio}')
        # self.log(f'upper_shadow_ratio: {self.upper_shadow_ratio}')
        # self.log(f'e_vol_vs_cd_avg_ratio: {self.e_vol_vs_cd_avg_ratio}')
        # self.log(f'e_vs_d_vol_ratio: {self.e_vs_d_vol_ratio}')
        # self.log(f'de_max_days: {self.de_max_days}')
        # self.log(f'e_yang_threshold: {self.e_yang_threshold}')
        # self.log(f'shadow_ratio: {self.shadow_ratio}')
        # self.log(f'ce_rise_ratio: {self.ce_rise_ratio}')
        # self.log(f'ab_max_days: {self.ab_max_days}')
        # self.log(f'cd_max_distance_trade_days: {self.cd_max_distance_trade_days}')
        # self.log(f'd_lookback_trade_days: {self.d_lookback_trade_days}')
        # self.log(f'=== 参数配置结束 ===\n')

        for code, df in data.items():
            hist = df[df['date'] <= date]
            if hist.empty:
                continue
            res = self._passes_filters(hist, code=code, date=str(date))
            if res:
                self.log(f'最终选出: {code} {date}')
                picks.append(code)
            else:
                self.log(f'未选出: {code} {date}')
        return picks
