#!/usr/bin/env python3
"""
高涨幅股票筛选模块
基于预设规则筛选高潜力股票，支持置信度预测
"""

import pandas as pd
import numpy as np

def quick_filter_high_gain_stocks(df, custom_rules=None):
    """
    快速筛选高涨幅潜力股票

    Args:
        df: 包含股票特征数据的DataFrame
        custom_rules: 自定义选股规则字典，如果为None则使用默认规则

    Returns:
        DataFrame: 筛选后的高潜力股票
    """
    if df.empty:
        return df

    # 复制数据避免修改原始数据
    filtered_df = df.copy()

    print(f"   原始股票数量: {len(filtered_df)}")

    # 使用自定义规则或默认规则
    if custom_rules:
        selection_rules = custom_rules
    else:
        # 基础选股规则（默认规则）
        selection_rules = {
            'E点价格振幅': {'threshold': 3.968, 'operator': '>='},
            'D点上影线/实体': {'threshold': 8.0, 'operator': '>='},
            'A-B涨幅': {'threshold': 78.62, 'operator': '>='},
            'E点实体涨跌幅': {'threshold': -2.4, 'operator': '<='}
        }
    
    # 应用筛选规则
    for rule_name, rule_config in selection_rules.items():
        if rule_name in filtered_df.columns:
            threshold = rule_config['threshold']
            operator = rule_config['operator']
            
            before_count = len(filtered_df)
            
            if operator == '>=':
                filtered_df = filtered_df[filtered_df[rule_name] >= threshold]
            elif operator == '<=':
                filtered_df = filtered_df[filtered_df[rule_name] <= threshold]
            elif operator == '>':
                filtered_df = filtered_df[filtered_df[rule_name] > threshold]
            elif operator == '<':
                filtered_df = filtered_df[filtered_df[rule_name] < threshold]
            elif operator == '==':
                filtered_df = filtered_df[filtered_df[rule_name] == threshold]
            
            after_count = len(filtered_df)
            print(f"   {rule_name} {operator} {threshold}: {before_count} → {after_count}")
        else:
            print(f"   ⚠️ 缺失特征列: {rule_name}")
    
    print(f"   最终筛选结果: {len(filtered_df)} 只股票")
    
    return filtered_df

def apply_custom_rules(df, custom_rules):
    """
    应用自定义筛选规则
    
    Args:
        df: 股票数据DataFrame
        custom_rules: 自定义规则字典
        
    Returns:
        DataFrame: 筛选后的数据
    """
    if df.empty or not custom_rules:
        return df
    
    filtered_df = df.copy()
    
    for rule_name, rule_config in custom_rules.items():
        if rule_name in filtered_df.columns:
            threshold = rule_config.get('threshold', 0)
            operator = rule_config.get('operator', '>=')
            
            if operator == '>=':
                filtered_df = filtered_df[filtered_df[rule_name] >= threshold]
            elif operator == '<=':
                filtered_df = filtered_df[filtered_df[rule_name] <= threshold]
            elif operator == '>':
                filtered_df = filtered_df[filtered_df[rule_name] > threshold]
            elif operator == '<':
                filtered_df = filtered_df[filtered_df[rule_name] < threshold]
            elif operator == '==':
                filtered_df = filtered_df[filtered_df[rule_name] == threshold]
    
    return filtered_df

def get_high_potential_features(df, target_column='5日最大涨幅', threshold=0.10):
    """
    分析高潜力股票的特征
    
    Args:
        df: 股票数据DataFrame
        target_column: 目标涨幅列名
        threshold: 高涨幅阈值
        
    Returns:
        dict: 特征分析结果
    """
    if target_column not in df.columns:
        return {}
    
    # 筛选高涨幅股票
    high_gain_stocks = df[df[target_column] >= threshold]
    
    if len(high_gain_stocks) == 0:
        return {}
    
    # 分析特征统计
    feature_stats = {}
    
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        if col != target_column:
            feature_stats[col] = {
                'mean': high_gain_stocks[col].mean(),
                'median': high_gain_stocks[col].median(),
                'std': high_gain_stocks[col].std(),
                'min': high_gain_stocks[col].min(),
                'max': high_gain_stocks[col].max()
            }
    
    return {
        'high_gain_count': len(high_gain_stocks),
        'total_count': len(df),
        'success_rate': len(high_gain_stocks) / len(df),
        'feature_stats': feature_stats
    }

if __name__ == "__main__":
    # 测试模块
    print("高涨幅股票筛选模块测试")
    
    # 创建测试数据
    test_data = {
        'E点价格振幅': [2.0, 4.5, 3.0, 5.2],
        'D点上影线/实体': [5.0, 10.0, 7.5, 12.0],
        'A-B涨幅': [60.0, 85.0, 70.0, 90.0],
        'E点实体涨跌幅': [-1.0, -3.0, -2.0, -4.0],
        '5日最大涨幅': [0.05, 0.15, 0.08, 0.20]
    }
    
    test_df = pd.DataFrame(test_data)
    print(f"测试数据: {len(test_df)} 行")
    
    # 测试筛选
    filtered_df = quick_filter_high_gain_stocks(test_df)
    print(f"筛选结果: {len(filtered_df)} 行")
    
    # 测试特征分析
    features = get_high_potential_features(test_df)
    print(f"特征分析: {features}")
