#!/usr/bin/env python3
"""
深度学习模型训练脚本
训练神经网络进行股票涨跌预测
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加当前目录到路径
sys.path.append('.')
sys.path.append('deeplearning')

from data_preprocessor import StockDataPreprocessor
from neural_network import StockNeuralNetwork
from html_renderer import HTMLRenderer

class DeepLearningTrainer:
    """深度学习训练器"""
    
    def __init__(self, config_path="deeplearning/config.json"):
        """初始化训练器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.preprocessor = StockDataPreprocessor(config_path)
        self.model = StockNeuralNetwork(config_path)
        self.training_results = {}

        # 创建时间戳子文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"deeplearning/output/training_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def train_model(self, train_data_path=None, test_data_path=None):
        """训练模型的完整流程"""
        print("🎯 深度学习股票预测模型训练")
        print("=" * 80)

        # 使用配置中的数据路径或传入的路径
        if train_data_path is None:
            train_data_path = self.config['data']['data_path']

        print(f"📊 训练数据: {train_data_path}")
        if test_data_path:
            print(f"📊 测试数据: {test_data_path}")

        # 1. 数据预处理
        print(f"\n🔄 步骤1: 数据预处理")
        print("-" * 40)

        if test_data_path:
            # 分别处理训练和测试数据
            processed_data = self.preprocessor.preprocess_separate_datasets(train_data_path, test_data_path)
        else:
            # 使用单一数据集进行分割
            processed_data = self.preprocessor.preprocess_training_data(train_data_path)

        if processed_data is None:
            print("❌ 数据预处理失败")
            return False
        
        # 2. 模型构建和训练
        print(f"\n🚀 步骤2: 模型训练")
        print("-" * 40)
        
        # 设置特征名称
        self.model.feature_names = processed_data['feature_names']

        # 构建模型
        self.model.build_models(processed_data['X_train'].shape[1])

        # 训练所有模型
        training_results = self.model.train_all_models(
            processed_data['X_train'],
            processed_data['y_train'],
            processed_data['X_test'],
            processed_data['y_test']
        )

        if not training_results:
            print("❌ 模型训练失败")
            return False
        
        # 3. 模型评估
        print(f"\n📊 步骤3: 模型评估")
        print("-" * 40)

        evaluation_results = self.model.evaluate_all_models(
            processed_data['X_test'],
            processed_data['y_test']
        )

        # 4. 特征重要性分析
        print(f"\n🔍 步骤4: 特征重要性分析")
        print("-" * 40)

        feature_importance = self.model.get_all_feature_importance(
            processed_data['X_test'],
            processed_data['y_test']
        )

        if feature_importance:
            for model_name, importance in feature_importance.items():
                print(f"\n🏆 {model_name} Top5 重要特征:")
                for i, row in importance.head(5).iterrows():
                    print(f"   {i+1:2d}. {row['feature']}: {row['importance']:.4f}")

        # 5. 保存模型和结果
        print(f"\n💾 步骤5: 保存模型和结果")
        print("-" * 40)

        # 保存所有模型
        model_paths = self.model.save_all_models()
        
        # 保存训练结果
        self.save_training_results(evaluation_results, feature_importance, processed_data)
        
        # 生成可视化
        self.generate_visualizations(evaluation_results, feature_importance)
        
        print(f"\n✅ 深度学习模型训练完成！")
        print(f"📁 模型文件: {model_paths}")

        # 生成HTML训练报告
        print(f"\n🎨 生成HTML训练报告...")
        try:
            renderer = HTMLRenderer()
            training_results_file = os.path.join(self.output_dir, f"training_results_{self.timestamp}.json")
            html_path = renderer.create_training_html(training_results_file)
            print(f"📄 HTML训练报告: {html_path}")
        except Exception as e:
            print(f"⚠️ HTML报告生成失败: {e}")

        return True
    
    def save_training_results(self, evaluation_results, feature_importance, processed_data):
        """保存训练结果"""
        # 保存评估结果
        results_file = os.path.join(self.output_dir, f"training_results_{self.timestamp}.json")
        
        # 准备保存的结果
        results_to_save = {
            'timestamp': self.timestamp,
            'config': self.config,
            'data_info': {
                'train_samples': len(processed_data['y_train']),
                'test_samples': len(processed_data['y_test']),
                'feature_count': len(processed_data['feature_names']),
                'positive_rate_train': float(processed_data['y_train'].mean()),
                'positive_rate_test': float(processed_data['y_test'].mean())
            },
            'evaluation_metrics': {},
            'feature_importance': {}
        }

        # 保存每个模型的评估结果
        for model_name, result in evaluation_results.items():
            results_to_save['evaluation_metrics'][model_name] = {
                'test_accuracy': float(result['test_accuracy']),
                'test_precision': float(result['test_precision']),
                'test_recall': float(result['test_recall']),
                'f1_score': float(result['f1_score']),
                'auc_score': float(result['auc_score']),
                'confusion_matrix': result['confusion_matrix'].tolist()
            }

        # 保存特征重要性
        if feature_importance:
            for model_name, importance in feature_importance.items():
                results_to_save['feature_importance'][model_name] = importance.to_dict('records')
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_to_save, f, ensure_ascii=False, indent=2)
        
        print(f"💾 训练结果已保存: {results_file}")
        
        # 保存特征重要性CSV
        if feature_importance:
            for model_name, importance_df in feature_importance.items():
                importance_file = os.path.join(self.output_dir, f"feature_importance_{model_name}_{self.timestamp}.csv")
                importance_df.to_csv(importance_file, index=False, encoding='utf-8')
                print(f"💾 {model_name} 特征重要性已保存: {importance_file}")
    
    def generate_visualizations(self, evaluation_results, feature_importance):
        """生成可视化图表"""
        
        # 1. 训练历史图
        if self.model.history:
            history_plot_path = os.path.join(self.output_dir, f"training_history_{self.timestamp}.png")
            self.model.plot_training_history(save_path=history_plot_path)
        else:
            print("⚠️ 没有训练历史数据，跳过训练历史图生成")
        
        # 2. 特征重要性图
        if feature_importance:
            for model_name, importance_df in feature_importance.items():
                plt.figure(figsize=(12, 8))
                top_features = importance_df.head(10)

                plt.barh(range(len(top_features)), top_features['importance'])
                plt.yticks(range(len(top_features)), top_features['feature'])
                plt.xlabel('重要性分数')
                plt.title(f'{model_name} Top10 特征重要性')
                plt.gca().invert_yaxis()

                importance_plot_path = os.path.join(self.output_dir, f"feature_importance_{model_name}_{self.timestamp}.png")
                plt.tight_layout()
                plt.savefig(importance_plot_path, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"📊 {model_name} 特征重要性图已保存: {importance_plot_path}")
        
        # 3. 混淆矩阵图
        if evaluation_results:
            for model_name, result in evaluation_results.items():
                plt.figure(figsize=(8, 6))
                cm = result['confusion_matrix']

                plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
                plt.title(f'{model_name} 混淆矩阵')
                plt.colorbar()

                classes = ['未涨超10%', '涨超10%']
                tick_marks = np.arange(len(classes))
                plt.xticks(tick_marks, classes)
                plt.yticks(tick_marks, classes)

                # 添加数值标签
                thresh = cm.max() / 2.
                for i in range(cm.shape[0]):
                    for j in range(cm.shape[1]):
                        plt.text(j, i, format(cm[i, j], 'd'),
                                ha="center", va="center",
                                color="white" if cm[i, j] > thresh else "black")

                plt.ylabel('真实标签')
                plt.xlabel('预测标签')

                cm_plot_path = os.path.join(self.output_dir, f"confusion_matrix_{model_name}_{self.timestamp}.png")
                plt.tight_layout()
                plt.savefig(cm_plot_path, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"📊 {model_name} 混淆矩阵图已保存: {cm_plot_path}")
    
    def display_summary(self):
        """显示训练总结"""
        print(f"\n📋 深度学习训练总结:")
        print("-" * 50)
        print(f"   模型类型: PrecisionMLP, ResidualNet, AttentionNet")
        print(f"   特征数量: {len(self.config['data']['feature_columns'])}")
        print(f"   训练轮数: {self.config['training']['epochs']}")
        print(f"   批次大小: {self.config['training']['batch_size']}")
        print(f"   学习率: {self.config['training']['learning_rate']}")
        print(f"   设备: {self.config['training']['device']}")

def main():
    """主函数"""
    print("🎯 深度学习股票预测系统")
    print("=" * 80)
    
    # 创建训练器
    trainer = DeepLearningTrainer()
    
    # 检查配置
    if trainer.config is None:
        print("❌ 配置文件加载失败，退出程序")
        return
    
    # 显示配置信息
    trainer.display_summary()
    
    # 开始训练
    success = trainer.train_model()
    
    if success:
        print(f"\n🎉 训练成功完成！")
        print(f"📁 结果保存在: {trainer.output_dir}")
        print(f"🚀 现在可以使用 predict_dl_stocks.py 进行预测")
    else:
        print(f"\n❌ 训练失败")

if __name__ == "__main__":
    main()
