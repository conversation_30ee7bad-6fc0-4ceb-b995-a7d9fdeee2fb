#!/usr/bin/env python3
"""
深度学习模型训练脚本
训练神经网络进行股票涨跌预测
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加当前目录到路径
sys.path.append('.')
sys.path.append('deeplearning')

from data_preprocessor import StockDataPreprocessor
from neural_network import StockNeuralNetwork
from html_renderer import HTMLRenderer

class DeepLearningTrainer:
    """深度学习训练器"""
    
    def __init__(self, config_path="deeplearning/config.json"):
        """初始化训练器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.preprocessor = StockDataPreprocessor(config_path)
        self.model = StockNeuralNetwork(config_path)
        self.training_results = {}

        # 创建时间戳子文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"deeplearning/output/training_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def train_model(self, train_data_path=None, test_data_path=None):
        """训练模型的完整流程 - 优化精确率"""
        print("🎯 深度学习股票预测模型训练 (精确率优化)")
        print("=" * 80)

        # 使用配置中的数据路径或传入的路径
        if train_data_path is None:
            train_data_path = self.config['data'].get('train_data_path', self.config['data']['data_path'])
        if test_data_path is None:
            test_data_path = self.config['data'].get('test_data_path')

        print(f"📊 训练数据: {train_data_path}")
        print(f"📊 测试数据: {test_data_path}")
        print(f"🎯 目标: 精确率 ≥ 80%，宁愿低召回率")

        # 1. 数据预处理
        print(f"\n🔄 步骤1: 数据预处理")
        print("-" * 40)

        if test_data_path:
            # 分别处理训练和测试数据
            processed_data = self.preprocessor.preprocess_separate_datasets(train_data_path, test_data_path)
        else:
            # 使用单一数据集进行分割
            processed_data = self.preprocessor.preprocess_training_data(train_data_path)

        if processed_data is None:
            print("❌ 数据预处理失败")
            return False
        
        # 2. 模型构建和训练
        print(f"\n🚀 步骤2: 模型训练")
        print("-" * 40)
        
        # 设置特征名称
        self.model.feature_names = processed_data['feature_names']

        # 构建模型
        self.model.build_models(processed_data['X_train'].shape[1])

        # 训练所有模型
        training_results = self.model.train_all_models(
            processed_data['X_train'],
            processed_data['y_train'],
            processed_data['X_test'],
            processed_data['y_test']
        )

        if not training_results:
            print("❌ 模型训练失败")
            return False
        
        # 3. 模型评估 - 精确率优化
        print(f"\n📊 步骤3: 模型评估 (精确率优化)")
        print("-" * 40)

        evaluation_results = self.evaluate_models_for_precision(
            processed_data['X_test'],
            processed_data['y_test']
        )

        # 4. 特征重要性分析
        print(f"\n🔍 步骤4: 特征重要性分析")
        print("-" * 40)

        feature_importance = self.model.get_all_feature_importance(
            processed_data['X_test'],
            processed_data['y_test']
        )

        if feature_importance:
            for model_name, importance in feature_importance.items():
                print(f"\n🏆 {model_name} Top5 重要特征:")
                for i, row in importance.head(5).iterrows():
                    print(f"   {i+1:2d}. {row['feature']}: {row['importance']:.4f}")

        # 5. 保存模型和结果
        print(f"\n💾 步骤5: 保存模型和结果")
        print("-" * 40)

        # 保存所有模型
        model_paths = self.model.save_all_models()
        
        # 保存训练结果
        self.save_training_results(evaluation_results, feature_importance, processed_data)
        
        # 生成可视化
        self.generate_visualizations(evaluation_results, feature_importance)
        
        print(f"\n✅ 深度学习模型训练完成！")
        print(f"📁 模型文件: {model_paths}")

        # 生成HTML训练报告
        print(f"\n🎨 生成HTML训练报告...")
        try:
            renderer = HTMLRenderer()
            training_results_file = os.path.join(self.output_dir, f"training_results_{self.timestamp}.json")
            html_path = renderer.create_training_html(training_results_file)
            print(f"📄 HTML训练报告: {html_path}")
        except Exception as e:
            print(f"⚠️ HTML报告生成失败: {e}")

        return True

    def evaluate_models_for_precision(self, X_test, y_test):
        """精确率优化的模型评估"""
        print("🎯 精确率优化评估...")

        evaluation_results = {}
        target_precision = self.config['training'].get('precision_threshold', 0.8)

        for model_name in self.model.models.keys():
            print(f"\n📊 评估 {model_name} (目标精确率: {target_precision:.1%}):")

            model = self.model.models[model_name]
            model.eval()

            # 获取预测概率
            import torch
            X_test_tensor = torch.FloatTensor(X_test).to(self.model.device)

            with torch.no_grad():
                y_pred_logits = model(X_test_tensor)
                y_pred_proba = torch.sigmoid(y_pred_logits).cpu().numpy().flatten()

            # 寻找最优阈值以达到目标精确率
            best_threshold = self.find_optimal_threshold_for_precision(
                y_test, y_pred_proba, target_precision
            )

            # 使用最优阈值进行预测
            y_pred = (y_pred_proba >= best_threshold).astype(int)

            # 计算评估指标
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix

            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            auc = roc_auc_score(y_test, y_pred_proba)
            cm = confusion_matrix(y_test, y_pred)

            # 计算预测的股票数量
            predicted_positive = np.sum(y_pred)
            total_positive = np.sum(y_test)

            print(f"   最优阈值: {best_threshold:.4f}")
            print(f"   精确率: {precision:.1%} (目标: {target_precision:.1%})")
            print(f"   召回率: {recall:.1%}")
            print(f"   F1分数: {f1:.4f}")
            print(f"   AUC: {auc:.4f}")
            print(f"   预测正样本: {predicted_positive} / {len(y_test)} ({predicted_positive/len(y_test):.1%})")
            print(f"   实际正样本: {total_positive} / {len(y_test)} ({total_positive/len(y_test):.1%})")

            evaluation_results[model_name] = {
                'test_accuracy': accuracy,
                'test_precision': precision,
                'test_recall': recall,
                'f1_score': f1,
                'auc_score': auc,
                'confusion_matrix': cm,
                'optimal_threshold': best_threshold,
                'predicted_positive': predicted_positive,
                'total_positive': total_positive,
                'precision_target_met': precision >= target_precision
            }

        return evaluation_results

    def find_optimal_threshold_for_precision(self, y_true, y_proba, target_precision):
        """寻找达到目标精确率的最优阈值"""
        from sklearn.metrics import precision_score

        # 测试不同的阈值
        thresholds = np.arange(0.1, 1.0, 0.01)
        best_threshold = 0.5
        best_precision = 0

        for threshold in thresholds:
            y_pred = (y_proba >= threshold).astype(int)

            # 如果没有预测为正样本，跳过
            if np.sum(y_pred) == 0:
                continue

            precision = precision_score(y_true, y_pred, zero_division=0)

            # 如果达到目标精确率，选择召回率最高的阈值
            if precision >= target_precision:
                recall = np.sum((y_pred == 1) & (y_true == 1)) / np.sum(y_true)
                if precision > best_precision or (precision == best_precision and recall > best_precision):
                    best_threshold = threshold
                    best_precision = precision

        # 如果没有找到满足条件的阈值，选择精确率最高的
        if best_precision < target_precision:
            print(f"   ⚠️ 无法达到目标精确率 {target_precision:.1%}，选择最高精确率阈值")
            for threshold in thresholds:
                y_pred = (y_proba >= threshold).astype(int)
                if np.sum(y_pred) == 0:
                    continue
                precision = precision_score(y_true, y_pred, zero_division=0)
                if precision > best_precision:
                    best_threshold = threshold
                    best_precision = precision

        return best_threshold

    def save_training_results(self, evaluation_results, feature_importance, processed_data):
        """保存训练结果"""
        # 保存评估结果
        results_file = os.path.join(self.output_dir, f"training_results_{self.timestamp}.json")
        
        # 准备保存的结果
        results_to_save = {
            'timestamp': self.timestamp,
            'config': self.config,
            'data_info': {
                'train_samples': len(processed_data['y_train']),
                'test_samples': len(processed_data['y_test']),
                'feature_count': len(processed_data['feature_names']),
                'positive_rate_train': float(processed_data['y_train'].mean()),
                'positive_rate_test': float(processed_data['y_test'].mean())
            },
            'evaluation_metrics': {},
            'feature_importance': {}
        }

        # 保存每个模型的评估结果
        for model_name, result in evaluation_results.items():
            results_to_save['evaluation_metrics'][model_name] = {
                'test_accuracy': float(result['test_accuracy']),
                'test_precision': float(result['test_precision']),
                'test_recall': float(result['test_recall']),
                'f1_score': float(result['f1_score']),
                'auc_score': float(result['auc_score']),
                'confusion_matrix': result['confusion_matrix'].tolist(),
                'optimal_threshold': float(result['optimal_threshold']),
                'predicted_positive': int(result['predicted_positive']),
                'total_positive': int(result['total_positive']),
                'precision_target_met': bool(result['precision_target_met'])
            }

        # 保存特征重要性
        if feature_importance:
            for model_name, importance in feature_importance.items():
                results_to_save['feature_importance'][model_name] = importance.to_dict('records')
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_to_save, f, ensure_ascii=False, indent=2)
        
        print(f"💾 训练结果已保存: {results_file}")
        
        # 保存特征重要性CSV
        if feature_importance:
            for model_name, importance_df in feature_importance.items():
                importance_file = os.path.join(self.output_dir, f"feature_importance_{model_name}_{self.timestamp}.csv")
                importance_df.to_csv(importance_file, index=False, encoding='utf-8')
                print(f"💾 {model_name} 特征重要性已保存: {importance_file}")
    
    def generate_visualizations(self, evaluation_results, feature_importance):
        """生成可视化图表"""
        
        # 1. 训练历史图
        if self.model.history:
            history_plot_path = os.path.join(self.output_dir, f"training_history_{self.timestamp}.png")
            self.model.plot_training_history(save_path=history_plot_path)
        else:
            print("⚠️ 没有训练历史数据，跳过训练历史图生成")
        
        # 2. 特征重要性图
        if feature_importance:
            for model_name, importance_df in feature_importance.items():
                plt.figure(figsize=(12, 8))
                top_features = importance_df.head(10)

                plt.barh(range(len(top_features)), top_features['importance'])
                plt.yticks(range(len(top_features)), top_features['feature'])
                plt.xlabel('重要性分数')
                plt.title(f'{model_name} Top10 特征重要性')
                plt.gca().invert_yaxis()

                importance_plot_path = os.path.join(self.output_dir, f"feature_importance_{model_name}_{self.timestamp}.png")
                plt.tight_layout()
                plt.savefig(importance_plot_path, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"📊 {model_name} 特征重要性图已保存: {importance_plot_path}")
        
        # 3. 混淆矩阵图
        if evaluation_results:
            for model_name, result in evaluation_results.items():
                plt.figure(figsize=(8, 6))
                cm = result['confusion_matrix']

                plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
                plt.title(f'{model_name} 混淆矩阵')
                plt.colorbar()

                classes = ['未涨超10%', '涨超10%']
                tick_marks = np.arange(len(classes))
                plt.xticks(tick_marks, classes)
                plt.yticks(tick_marks, classes)

                # 添加数值标签
                thresh = cm.max() / 2.
                for i in range(cm.shape[0]):
                    for j in range(cm.shape[1]):
                        plt.text(j, i, format(cm[i, j], 'd'),
                                ha="center", va="center",
                                color="white" if cm[i, j] > thresh else "black")

                plt.ylabel('真实标签')
                plt.xlabel('预测标签')

                cm_plot_path = os.path.join(self.output_dir, f"confusion_matrix_{model_name}_{self.timestamp}.png")
                plt.tight_layout()
                plt.savefig(cm_plot_path, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"📊 {model_name} 混淆矩阵图已保存: {cm_plot_path}")
    
    def display_summary(self):
        """显示训练总结"""
        print(f"\n📋 深度学习训练总结 (精确率优化):")
        print("-" * 50)
        print(f"   模型类型: PrecisionMLP, ResidualNet, AttentionNet")
        print(f"   特征数量: {len(self.config['data']['feature_columns'])}")
        print(f"   目标列: {self.config['data']['target_column']}")
        print(f"   目标精确率: {self.config['training'].get('precision_threshold', 0.8):.1%}")
        print(f"   训练轮数: {self.config['training']['epochs']}")
        print(f"   批次大小: {self.config['training']['batch_size']}")
        print(f"   学习率: {self.config['training']['learning_rate']}")
        print(f"   损失函数: {self.config['training'].get('loss_type', 'focal')}")
        print(f"   类别权重: {self.config['training'].get('class_weight', 8.0)}")
        print(f"   设备: {self.config['training']['device']}")
        print(f"   策略: 宁愿低召回率，也要高精确率")

def main():
    """主函数 - 精确率优化训练"""
    print("🎯 深度学习股票预测系统 (精确率优化)")
    print("=" * 80)

    # 创建训练器
    trainer = DeepLearningTrainer()

    # 检查配置
    if trainer.config is None:
        print("❌ 配置文件加载失败，退出程序")
        return

    # 显示配置信息
    trainer.display_summary()

    # 指定训练和测试数据集 - 使用绝对路径确保正确
    train_data_path = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    test_data_path = "选股分析结果/2025-05-01-2025-06-01.xlsx"

    print(f"\n📊 数据集配置:")
    print(f"   训练集: {train_data_path}")
    print(f"   测试集: {test_data_path}")
    print(f"   目标: 5日成功选股")
    print(f"   策略: 宁愿1%召回率，也要80%+精确率")

    # 验证数据文件是否存在
    if not os.path.exists(train_data_path):
        print(f"❌ 训练数据文件不存在: {train_data_path}")
        return
    if not os.path.exists(test_data_path):
        print(f"❌ 测试数据文件不存在: {test_data_path}")
        return

    print(f"✅ 数据文件验证通过")

    # 开始训练
    success = trainer.train_model(train_data_path, test_data_path)

    if success:
        print(f"\n🎉 精确率优化训练成功完成！")
        print(f"📁 结果保存在: {trainer.output_dir}")
        print(f"🚀 现在可以使用 predict_dl_stocks.py 进行预测")
        print(f"💡 模型已优化为高精确率、低召回率模式")

        # 显示最终总结
        print(f"\n📋 训练总结:")
        print(f"   🎯 目标: 精确率 ≥ 80%，宁愿低召回率")
        print(f"   📊 训练集: {train_data_path}")
        print(f"   📊 测试集: {test_data_path}")
        print(f"   🔧 特征数量: {len(trainer.config['data']['feature_columns'])}")
        print(f"   🎯 目标列: {trainer.config['data']['target_column']}")
    else:
        print(f"\n❌ 训练失败")

if __name__ == "__main__":
    main()
