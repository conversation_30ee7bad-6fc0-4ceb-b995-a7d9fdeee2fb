#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双损失函数深度学习股票预测脚本
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler, LabelEncoder
import argparse
import os
import pickle
import json
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 复制模型定义
class PrecisionMLP(nn.Module):
    """深度精确率导向的MLP模型 - 5层深度网络"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(PrecisionMLP, self).__init__()

        # 更深的网络架构，专注于精确率提升
        self.model = nn.Sequential(
            # 第1层：输入扩展
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第2层：特征学习
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第3层：深度特征提取
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第4层：特征压缩
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.BatchNorm1d(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第5层：最终分类
            nn.Linear(hidden_dim // 4, 2)
        )

    def forward(self, x):
        return self.model(x)

class ResidualBlock(nn.Module):
    """残差块"""
    def __init__(self, dim, dropout_rate=0.3):
        super(ResidualBlock, self).__init__()
        self.block = nn.Sequential(
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim)
        )
        self.relu = nn.ReLU()
        
    def forward(self, x):
        residual = x
        out = self.block(x)
        out += residual
        return self.relu(out)

class ResidualNet(nn.Module):
    """残差网络模型"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(ResidualNet, self).__init__()
        
        # 输入层
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # 更多残差块，增强学习能力
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate * 0.8),  # 逐渐降低dropout
            ResidualBlock(hidden_dim, dropout_rate * 0.6)
        ])

        # 中间压缩层
        self.compression = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5)
        )

        # 额外的残差块用于压缩后的特征
        self.final_blocks = nn.ModuleList([
            ResidualBlock(hidden_dim // 2, dropout_rate * 0.4),
            ResidualBlock(hidden_dim // 2, dropout_rate * 0.3)
        ])

        # 输出层
        self.output_layer = nn.Linear(hidden_dim // 2, 2)
    
    def forward(self, x):
        x = self.input_layer(x)

        # 通过所有残差块
        for block in self.residual_blocks:
            x = block(x)

        # 压缩特征
        x = self.compression(x)

        # 最终残差块
        for block in self.final_blocks:
            x = block(x)

        return self.output_layer(x)

class AttentionNet(nn.Module):
    """注意力网络模型"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(AttentionNet, self).__init__()
        
        # 特征嵌入
        self.feature_embedding = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # 多头注意力
        self.attention_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 4),
                nn.Tanh(),
                nn.Linear(hidden_dim // 4, 1)
            ) for _ in range(4)
        ])
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, 2)
        )
    
    def forward(self, x):
        # 特征嵌入
        embedded = self.feature_embedding(x)
        
        # 多头注意力
        attention_outputs = []
        for head in self.attention_heads:
            attention_weights = torch.softmax(head(embedded), dim=1)
            attention_output = embedded * attention_weights
            attention_outputs.append(attention_output)
        
        # 注意力融合
        fused_features = torch.stack(attention_outputs, dim=0).mean(dim=0)
        fused_features = self.feature_fusion(fused_features)
        
        return self.classifier(fused_features)

class EnsembleModel(nn.Module):
    """集成模型 - 融合三个模型的预测结果"""
    def __init__(self, input_dim, hidden_dim=128, dropout_rate=0.3):
        super(EnsembleModel, self).__init__()

        # 三个基础模型
        self.precision_mlp = PrecisionMLP(input_dim, hidden_dim, dropout_rate)
        self.residual_net = ResidualNet(input_dim, hidden_dim, dropout_rate)
        self.attention_net = AttentionNet(input_dim, hidden_dim, dropout_rate)

        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(6, 32),  # 3个模型 * 2个输出
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(32, 16),
            nn.BatchNorm1d(16),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(16, 2)
        )

    def forward(self, x):
        # 获取三个模型的输出
        mlp_out = self.precision_mlp(x)
        residual_out = self.residual_net(x)
        attention_out = self.attention_net(x)

        # 拼接所有输出
        combined = torch.cat([mlp_out, residual_out, attention_out], dim=1)

        # 通过融合层
        return self.fusion_layer(combined)

class DualLossPredictor:
    """双损失函数预测器"""
    
    def __init__(self, model_folder=None, reverse_probability=False):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_folder = model_folder
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.reverse_probability = reverse_probability  # 是否反转预测概率

        # 如果指定了模型文件夹，从中加载配置
        if model_folder and os.path.exists(model_folder):
            self.load_config_from_folder(model_folder)
        else:
            # 使用默认配置
            self.use_default_config()
    
    def find_latest_model_folder(self):
        """查找最新的训练文件夹"""
        # 首先在ml文件夹中查找
        pattern = "ml/dual_loss_training_*"
        folders = glob.glob(pattern)
        
        # 如果ml文件夹中没有，再在当前目录查找
        if not folders:
            pattern = "dual_loss_training_*"
            folders = glob.glob(pattern)
        
        if folders:
            # 按时间戳排序，返回最新的
            folders.sort(reverse=True)
            return folders[0]
        return None
    
    def load_config_from_folder(self, model_folder):
        """从训练文件夹加载配置"""
        print(f"📁 从文件夹加载配置: {model_folder}")
        
        # 加载配置文件
        config_path = f"{model_folder}/config/ml_config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print(f"   ✅ 配置文件加载成功")
        else:
            print(f"   ⚠️ 配置文件不存在，使用默认配置")
            self.use_default_config()
            return
        
        # 加载预处理器
        scaler_path = f"{model_folder}/models/scaler.pkl"
        if os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
            print(f"   ✅ 标准化器加载成功")
        
        label_encoder_path = f"{model_folder}/models/label_encoder.pkl"
        if os.path.exists(label_encoder_path):
            with open(label_encoder_path, 'rb') as f:
                self.label_encoder = pickle.load(f)
            print(f"   ✅ 标签编码器加载成功")
        
        # 设置特征列表
        self.feature_columns = self.config["data"]["feature_columns"]
        
        # 设置模型配置
        self.model_configs = self.config["models"]
    
    def use_default_config(self):
        """使用默认配置"""
        self.feature_columns = [
            "A点实体涨跌幅", "A点价格振幅",
            "B点成交量", "B点实体涨跌幅", "B点价格振幅",
            "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量",
            "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"
        ]
        
        self.model_configs = {
            'PrecisionMLP': {
                'threshold': 0.5,
                'hidden_dim': 128,
                'dropout_rate': 0.3
            },
            'ResidualNet': {
                'threshold': 0.5,
                'hidden_dim': 128,
                'dropout_rate': 0.3
            },
            'AttentionNet': {
                'threshold': 0.5,
                'hidden_dim': 128,
                'dropout_rate': 0.3
            },
            'EnsembleModel': {
                'threshold': 0.5,
                'hidden_dim': 128,
                'dropout_rate': 0.3
            }
        }

    def preprocess_data(self, df):
        """预处理数据"""
        print("🔄 数据预处理...")

        # 检查特征列
        missing_features = [col for col in self.feature_columns if col not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features}")
            return None

        available_features = [col for col in self.feature_columns if col in df.columns]
        print(f"   ✅ 可用特征: {len(available_features)}/{len(self.feature_columns)}")

        # 提取特征
        X = df[available_features].copy()

        # 处理百分号格式的数据
        for col in X.columns:
            if X[col].dtype == 'object':
                # 尝试转换百分号格式
                try:
                    X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                except:
                    # 如果转换失败，尝试直接转换为数值
                    try:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                    except:
                        pass

        # 处理缺失值
        X = X.fillna(X.median())

        # 移除无效行
        valid_mask = ~X.isnull().any(axis=1)
        X = X[valid_mask]

        print(f"   ✅ 预处理完成: {X.shape}")

        return X.values

    def load_models(self):
        """加载训练好的模型"""
        print("🔄 加载训练好的模型...")

        # 确定模型文件夹
        if self.model_folder is None:
            self.model_folder = self.find_latest_model_folder()
            if self.model_folder:
                print(f"   📁 自动找到最新训练文件夹: {self.model_folder}")
                self.load_config_from_folder(self.model_folder)
            else:
                print("   ⚠️ 未找到训练文件夹，使用当前目录")
                self.model_folder = "."

        # 模型文件路径 - 修复拼写错误
        if self.model_folder != ".":
            model_files = {
                'PrecisionMLP': f'{self.model_folder}/models/best_precisionmlp_model.pth',
                'ResidualNet': f'{self.model_folder}/models/best_residualnet_model.pth',
                'AttentionNet': f'{self.model_folder}/models/best_attentionnet_model.pth',
                'EnsembleModel': f'{self.model_folder}/models/best_ensemblemodel_model.pth'
            }
        else:
            # 兼容旧版本，在当前目录查找
            model_files = {
                'PrecisionMLP': 'best_precisionmlp_model.pth',
                'ResidualNet': 'best_residualnet_model.pth',
                'AttentionNet': 'best_attentionnet_model.pth',
                'EnsembleModel': 'best_ensemblemodel_model.pth'
            }

        self.models = {}
        input_dim = len(self.feature_columns)

        for model_name, config in self.model_configs.items():
            try:
                # 创建模型
                if model_name == 'PrecisionMLP':
                    model = PrecisionMLP(
                        input_dim=input_dim,
                        hidden_dim=config['hidden_dim'],
                        dropout_rate=config['dropout_rate']
                    )
                elif model_name == 'ResidualNet':
                    model = ResidualNet(
                        input_dim=input_dim,
                        hidden_dim=config['hidden_dim'],
                        dropout_rate=config['dropout_rate']
                    )
                elif model_name == 'AttentionNet':
                    model = AttentionNet(
                        input_dim=input_dim,
                        hidden_dim=config['hidden_dim'],
                        dropout_rate=config['dropout_rate']
                    )
                elif model_name == 'EnsembleModel':
                    model = EnsembleModel(
                        input_dim=input_dim,
                        hidden_dim=config['hidden_dim'],
                        dropout_rate=config['dropout_rate']
                    )

                # 尝试加载权重
                model_file = model_files[model_name]
                if os.path.exists(model_file):
                    model.load_state_dict(torch.load(model_file, map_location=self.device))
                    model.to(self.device)
                    model.eval()
                    self.models[model_name] = model
                    print(f"   ✅ {model_name} 加载成功: {model_file}")
                else:
                    print(f"   ⚠️ {model_name} 模型文件不存在: {model_file}")
            except Exception as e:
                print(f"   ❌ {model_name} 加载失败: {e}")

        if not self.models:
            print("❌ 没有成功加载任何模型")
            return False

        return True

    def predict(self, input_file, output_file=None):
        """进行预测"""
        print(f"\n🎯 开始预测: {input_file}")
        print("=" * 60)

        # 加载数据
        try:
            df = pd.read_excel(input_file)
            print(f"📁 加载数据: {df.shape}")

            # 检查是否有实际涨幅数据
            has_actual_data = '5日最大涨幅' in df.columns
            if has_actual_data:
                print("   ✅ 发现实际涨幅数据，将在报告中显示预测准确性")

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return

        # 预处理
        X = self.preprocess_data(df)
        if X is None:
            print("❌ 数据预处理失败")
            return

        # 加载模型（这会自动处理配置和预处理器）
        if not self.load_models():
            print("❌ 模型加载失败")
            return

        # 标准化（使用训练时的scaler）
        X_scaled = self.scaler.transform(X)

        # 预测
        predictions_results = []

        for model_name, model in self.models.items():
            print(f"\n🔄 使用 {model_name} 进行预测...")

            with torch.no_grad():
                X_tensor = torch.FloatTensor(X_scaled).to(self.device)
                outputs = model(X_tensor)
                probabilities = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()

            # 使用配置的阈值
            threshold = self.model_configs[model_name].get('threshold', 0.5)
            predictions = (probabilities > threshold).astype(int)

            print(f"   📊 预测阈值: {threshold:.3f}")
            print(f"   📈 预测成功数量: {predictions.sum()} / {len(predictions)}")

            if predictions.sum() == 0:
                print(f"   ⚠️ 没有预测为成功的股票")
                continue

            # 获取预测为成功的股票
            success_indices = np.where(predictions == 1)[0]

            for idx in success_indices:
                prediction_data = {
                    '模型': model_name,
                    '行号': idx + 1,
                    '股票代码': df.iloc[idx].get('股票', f'股票_{idx+1}'),
                    '预测概率': probabilities[idx],
                    '预测阈值': threshold,
                    '买入日期': df.iloc[idx].get('买入日期', 'N/A')
                }

                # 如果有实际涨幅数据，添加到结果中
                if has_actual_data:
                    print( df.iloc[idx]['5日最大涨幅'] * 100)
                    actual_gain = df.iloc[idx]['5日最大涨幅']*100
                    prediction_data['实际涨幅'] = actual_gain
                    # 判断预测是否成功（假设10%为成功阈值）
                    if isinstance(actual_gain, (int, float)):
                        prediction_data['预测结果'] = '✅ 大10%成功' if actual_gain >= 10.0 else '❌ 失败'
                    else:
                        prediction_data['预测结果'] = '❓ 未知'

                predictions_results.append(prediction_data)

        # 保存预测结果
        if predictions_results:
            results_df = pd.DataFrame(predictions_results)

            # 按预测概率排序
            results_df = results_df.sort_values(['模型', '预测概率'], ascending=[True, False])

            # 创建预测结果文件夹
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            prediction_folder = f'ml/dual_loss_prediction_{timestamp}'
            os.makedirs(prediction_folder, exist_ok=True)

            # 生成输出文件名
            if output_file is None:
                output_file = f'{prediction_folder}/预测结果_{timestamp}.xlsx'
            else:
                # 如果指定了输出文件，也放到预测文件夹中
                filename = os.path.basename(output_file)
                output_file = f'{prediction_folder}/{filename}'

            # 保存到Excel
            results_df.to_excel(output_file, index=False)

            # 计算预测准确性统计
            accuracy_stats = {}
            if has_actual_data and '预测结果' in results_df.columns:
                total_predictions = len(results_df)
                successful_predictions = len(results_df[results_df['预测结果'] == '✅ 成功'])
                accuracy_rate = successful_predictions / total_predictions if total_predictions > 0 else 0
                accuracy_stats = {
                    'total_predictions': total_predictions,
                    'successful_predictions': successful_predictions,
                    'accuracy_rate': accuracy_rate
                }
                print(f"📊 预测准确性: {successful_predictions}/{total_predictions} = {accuracy_rate:.1%}")

            # 保存预测信息
            prediction_info = {
                'prediction_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'input_file': input_file,
                'output_file': output_file,
                'model_folder': self.model_folder,
                'total_predictions': len(predictions_results),
                'models_used': list(self.models.keys()),
                'prediction_folder': prediction_folder,
                'has_actual_data': has_actual_data,
                'accuracy_stats': accuracy_stats
            }

            info_file = f'{prediction_folder}/prediction_info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(prediction_info, f, ensure_ascii=False, indent=2)

            print(f"\n💾 预测结果已保存:")
            print(f"   📁 预测文件夹: {prediction_folder}")
            print(f"   📊 预测结果: {output_file}")
            print(f"   📋 预测信息: {info_file}")
            print(f"📊 总预测数量: {len(predictions_results)} 个")

            # 按模型统计
            model_stats = results_df.groupby('模型').size()
            print(f"📈 各模型预测数量:")
            for model, count in model_stats.items():
                print(f"   {model}: {count} 个")

            # 生成HTML可视化报告
            self.generate_prediction_html(results_df, prediction_folder, input_file, has_actual_data)

            return prediction_folder
        else:
            print("⚠️ 没有任何预测结果")
            return None

    def generate_prediction_html(self, results_df, prediction_folder, input_file, has_actual_data=False):
        """生成预测结果的HTML可视化报告"""
        print("📊 生成预测结果HTML可视化...")

        # 统计信息
        total_predictions = len(results_df)
        model_stats = results_df.groupby('模型').agg({
            '预测概率': ['count', 'mean', 'max', 'min']
        }).round(3)

        # HTML模板
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双损失函数深度学习股票预测结果</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2196F3;
        }}
        .header h1 {{
            color: #1976D2;
            margin-bottom: 10px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }}
        .summary-card .value {{
            font-size: 1.8em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 10px;
        }}
        .chart-title {{
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
        }}
        .predictions-table {{
            margin: 30px 0;
            overflow-x: auto;
        }}
        .predictions-table table {{
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }}
        .predictions-table th {{
            background-color: #1976D2;
            color: white;
            padding: 15px;
            text-align: left;
        }}
        .predictions-table td {{
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }}
        .predictions-table tr:hover {{
            background-color: #f5f5f5;
        }}
        .model-tag {{
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .model-precisionmlp {{ background-color: #4CAF50; }}
        .model-residualnet {{ background-color: #FF9800; }}
        .model-attentionnet {{ background-color: #9C27B0; }}
        .model-ensemblemodel {{ background-color: #F44336; }}
        .prob-bar {{
            width: 100%;
            height: 20px;
            background-color: #eee;
            border-radius: 10px;
            overflow: hidden;
        }}
        .prob-fill {{
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #FFC107 50%, #F44336 100%);
            border-radius: 10px;
        }}
        canvas {{
            max-height: 400px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 双损失函数深度学习股票预测结果</h1>
            <p>预测时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>输入文件: {os.path.basename(input_file)}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>📊 总预测数量</h3>
                <div class="value">{total_predictions}</div>
                <p>个股票</p>
            </div>
            <div class="summary-card">
                <h3>🤖 使用模型</h3>
                <div class="value">{len(results_df['模型'].unique())}</div>
                <p>个模型</p>
            </div>
            <div class="summary-card">
                <h3>📈 平均概率</h3>
                <div class="value">{results_df['预测概率'].mean():.1%}</div>
                <p>预测置信度</p>
            </div>
            <div class="summary-card">
                <h3>🎯 最高概率</h3>
                <div class="value">{results_df['预测概率'].max():.1%}</div>
                <p>最佳预测</p>
            </div>"""

        # 如果有实际数据，添加准确性统计卡片
        if has_actual_data and '预测结果' in results_df.columns:
            total_predictions = len(results_df)
            successful_predictions = len(results_df[results_df['预测结果'] == '✅ 成功'])
            accuracy_rate = successful_predictions / total_predictions if total_predictions > 0 else 0

            html_content += f"""
            <div class="summary-card" style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);">
                <h3>✅ 预测成功</h3>
                <div class="value">{successful_predictions}</div>
                <p>个股票</p>
            </div>
            <div class="summary-card" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                <h3>🎯 成功率</h3>
                <div class="value">{accuracy_rate:.1%}</div>
                <p>预测准确性</p>
            </div>"""

        html_content += """
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 各模型预测数量分布</div>
            <canvas id="modelChart"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-title">📈 预测概率分布</div>
            <canvas id="probChart"></canvas>
        </div>

        <div class="predictions-table">
            <h2>📋 详细预测结果</h2>
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>模型</th>
                        <th>股票代码</th>
                        <th>预测概率</th>
                        <th>概率条</th>
                        <th>买入日期</th>"""

        # 如果有实际数据，添加额外的列
        if has_actual_data:
            html_content += """
                        <th>实际涨幅</th>
                        <th>预测结果</th>"""

        html_content += """
                    </tr>
                </thead>
                <tbody>
"""

        # 添加预测结果表格
        for idx, row in results_df.head(50).iterrows():  # 只显示前50个
            model_class = f"model-{row['模型'].lower()}"
            prob_width = row['预测概率'] * 100

            html_content += f"""
                    <tr>
                        <td>{idx + 1}</td>
                        <td><span class="model-tag {model_class}">{row['模型']}</span></td>
                        <td><strong>{row['股票代码']}</strong></td>
                        <td>{row['预测概率']:.1%}</td>
                        <td>
                            <div class="prob-bar">
                                <div class="prob-fill" style="width: {prob_width}%"></div>
                            </div>
                        </td>
                        <td>{row['买入日期']}</td>"""

            # 如果有实际数据，添加额外的列
            if has_actual_data:
                actual_gain = row.get('实际涨幅', 'N/A')
                prediction_result = row.get('预测结果', '❓ 未知')

                # 格式化实际涨幅
                if isinstance(actual_gain, (int, float)):
                    gain_str = f"{actual_gain:.1f}%"
                    gain_color = "color: green;" if actual_gain >= 10.0 else "color: red;"
                else:
                    gain_str = str(actual_gain)
                    gain_color = ""

                html_content += f"""
                        <td style="{gain_color}"><strong>{gain_str}</strong></td>
                        <td>{prediction_result}</td>"""

            html_content += """
                    </tr>
"""

        if len(results_df) > 50:
            html_content += f"""
                    <tr>
                        <td colspan="6" style="text-align: center; color: #666; font-style: italic;">
                            ... 还有 {len(results_df) - 50} 个预测结果
                        </td>
                    </tr>
"""

        html_content += """
                </tbody>
            </table>
        </div>

        <script>
"""

        # 添加图表数据
        model_names = results_df['模型'].value_counts().index.tolist()
        model_counts = results_df['模型'].value_counts().values.tolist()

        # 概率分布数据
        prob_bins = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        prob_counts = []
        for i in range(len(prob_bins) - 1):
            count = len(results_df[(results_df['预测概率'] >= prob_bins[i]) &
                                 (results_df['预测概率'] < prob_bins[i+1])])
            prob_counts.append(count)
        # 最后一个区间包含1.0
        prob_counts.append(len(results_df[results_df['预测概率'] >= prob_bins[-1]]))

        html_content += f"""
        // 模型分布图表
        const ctx1 = document.getElementById('modelChart').getContext('2d');
        new Chart(ctx1, {{
            type: 'doughnut',
            data: {{
                labels: {model_names},
                datasets: [{{
                    data: {model_counts},
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(76, 175, 80, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '各模型预测数量占比'
                    }},
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});

        // 概率分布图表
        const ctx2 = document.getElementById('probChart').getContext('2d');
        new Chart(ctx2, {{
            type: 'bar',
            data: {{
                labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                datasets: [{{
                    label: '预测数量',
                    data: {prob_counts[:-1]},  // 去掉最后一个，因为通常为0
                    backgroundColor: 'rgba(33, 150, 243, 0.8)',
                    borderColor: 'rgba(33, 150, 243, 1)',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: '预测数量'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: '预测概率区间'
                        }}
                    }}
                }},
                plugins: {{
                    title: {{
                        display: true,
                        text: '预测概率分布'
                    }}
                }}
            }}
        }});
        </script>
    </div>
</body>
</html>
"""

        # 保存HTML文件
        html_path = f'{prediction_folder}/prediction_report.html'
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"   ✅ 预测HTML报告已生成: {html_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='双损失函数深度学习股票预测')
    parser.add_argument('--input', '-i', required=True, help='输入Excel文件路径')
    parser.add_argument('--output', '-o', help='输出Excel文件路径（可选）')
    parser.add_argument('--model_folder', '-m', help='模型文件夹路径（可选，默认使用最新的训练文件夹）')

    args = parser.parse_args()

    print("🎯 双损失函数深度学习股票预测脚本")
    print("=" * 60)

    # 创建预测器
    predictor = DualLossPredictor(model_folder=args.model_folder)

    # 进行预测
    output_folder = predictor.predict(args.input, args.output)

    if output_folder:
        print(f"\n✅ 预测完成！")
        print(f"📁 结果文件夹: {output_folder}")
    else:
        print(f"\n❌ 预测失败！")

if __name__ == "__main__":
    main()
