# -*- coding: utf-8 -*-
"""
股票数据管理模块
"""

import pandas as pd
import os
import json
import time
from datetime import datetime, timedelta
import akshare as ak
from config import *

class StockDataManager:
    """股票数据管理器"""
    
    def __init__(self):
        self.stock_data_cache = {}
        self.stock_list = []
        self.load_stock_list()
        self.load_existing_data()
    
    def get_stock_list(self):
        """获取股票列表"""
        try:
            if not self.stock_list:
                stock_info = ak.stock_info_a_code_name()
                self.stock_list = stock_info.to_dict('records')
                self.save_stock_list()
            return self.stock_list
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def save_stock_list(self):
        """保存股票列表到本地"""
        try:
            with open(STOCK_LIST_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.stock_list, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存股票列表失败: {e}")
    
    def load_stock_list(self):
        """从本地加载股票列表"""
        try:
            if os.path.exists(STOCK_LIST_FILE):
                with open(STOCK_LIST_FILE, 'r', encoding='utf-8') as f:
                    self.stock_list = json.load(f)
        except Exception as e:
            print(f"加载股票列表失败: {e}")
            self.stock_list = []
    
    def fetch_stock_data(self, stock_code, start_date=None):
        """获取单个股票数据"""
        try:
            # 确保start_date不为None，默认获取最近一年的数据
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=DEFAULT_DAYS)).strftime('%Y%m%d')
                
            stock_data = ak.stock_zh_a_hist(
                symbol=stock_code, 
                period="daily", 
                start_date=start_date, 
                adjust="qfq"
            )
            
            if stock_data.empty:
                return pd.DataFrame()
            
            # 重命名列
            stock_data.columns = ['日期', '开盘价', '收盘价', '最高价', '最低价', 
                                '交易量', '交易额', '振幅', '涨跌幅', '涨跌额', '换手率']
            
            # 重新排列列顺序
            stock_data = stock_data[['日期', '涨跌幅', '开盘价', '收盘价', '最高价', 
                                   '最低价', '换手率', '交易量', '交易额']]
            
            return stock_data
        except Exception as e:
            print(f"获取股票 {stock_code} 数据失败: {e}")
            return pd.DataFrame()
    
    def update_stock_data(self, stock_code, stock_name):
        """更新单个股票数据"""
        file_path = os.path.join(DATA_DIR, f"{stock_code}.csv")
        
        if os.path.exists(file_path):
            # 读取现有数据
            existing_data = pd.read_csv(file_path)
            existing_data['日期'] = pd.to_datetime(existing_data['日期'])
            
            # 获取最新日期
            latest_date = existing_data['日期'].max()
            start_date = (latest_date + timedelta(days=1)).strftime('%Y%m%d')
        else:
            # 新股票，获取最近一年的数据
            start_date = (datetime.now() - timedelta(days=DEFAULT_DAYS)).strftime('%Y%m%d')
        
        # 获取新数据
        new_data = self.fetch_stock_data(stock_code, start_date)
        
        if not new_data.empty:
            if os.path.exists(file_path):
                # 合并数据
                combined_data = pd.concat([existing_data, new_data], ignore_index=True)
            else:
                combined_data = new_data
            
            # 保存到本地
            combined_data.to_csv(file_path, index=False)
            
            # 更新缓存
            self.stock_data_cache[stock_code] = {
                'name': stock_name,
                'data': combined_data
            }
            
            return True
        
        return False
    
    def update_all_stock_data(self):
        """更新所有股票数据"""
        stock_list = self.get_stock_list()
        if not stock_list:
            return False
        
        success_count = 0
        for stock in stock_list[:MAX_STOCKS]:
            stock_code = stock['code']
            stock_name = stock['name']
            
            if self.update_stock_data(stock_code, stock_name):
                success_count += 1
            
            time.sleep(REQUEST_DELAY)
        
        print(f"成功更新 {success_count} 只股票的数据")
        return success_count > 0
    
    def load_existing_data(self):
        """从CSV文件加载现有股票数据"""
        if not os.path.exists(DATA_DIR):
            return
        
        for filename in os.listdir(DATA_DIR):
            if filename.endswith('.csv'):
                stock_code = filename.replace('.csv', '')
                file_path = os.path.join(DATA_DIR, filename)
                
                try:
                    # 读取CSV文件
                    data = pd.read_csv(file_path)
                    
                    # 重命名列以匹配我们的格式
                    column_mapping = {
                        'date': '日期',
                        'open': '开盘价',
                        'close': '收盘价', 
                        'high': '最高价',
                        'low': '最低价',
                        'volume': '交易量'
                    }
                    
                    # 重命名存在的列
                    for old_col, new_col in column_mapping.items():
                        if old_col in data.columns:
                            data[new_col] = data[old_col]
                    
                    # 确保日期列存在并转换格式
                    if '日期' in data.columns:
                        data['日期'] = pd.to_datetime(data['日期'])
                    else:
                        print(f"警告：股票 {stock_code} 缺少日期列")
                        continue
                    
                    # 为缺失的字段生成mock数据
                    if '涨跌幅' not in data.columns:
                        # 计算涨跌幅
                        data['涨跌幅'] = data['收盘价'].pct_change() * 100
                        data['涨跌幅'] = data['涨跌幅'].fillna(0)
                    
                    if '换手率' not in data.columns:
                        # 生成mock换手率 (0.1% - 5%)
                        import random
                        data['换手率'] = [random.uniform(0.1, 5.0) for _ in range(len(data))]
                    
                    if '交易额' not in data.columns:
                        # 计算交易额 = 收盘价 * 交易量
                        data['交易额'] = data['收盘价'] * data['交易量']
                    
                    # 确保所有必需列都存在
                    required_columns = ['日期', '涨跌幅', '开盘价', '收盘价', '最高价', '最低价', '换手率', '交易量', '交易额']
                    for col in required_columns:
                        if col not in data.columns:
                            print(f"警告：股票 {stock_code} 缺少列 {col}，使用默认值")
                            if col == '涨跌幅':
                                data[col] = 0.0
                            elif col in ['开盘价', '收盘价', '最高价', '最低价']:
                                data[col] = data['收盘价'] if '收盘价' in data.columns else 10.0
                            elif col == '换手率':
                                data[col] = 1.0
                            elif col == '交易量':
                                data[col] = 1000000
                            elif col == '交易额':
                                data[col] = data['收盘价'] * data['交易量'] if '收盘价' in data.columns and '交易量' in data.columns else 10000000
                    
                    # 获取股票名称
                    stock_name = self.get_stock_name(stock_code)
                    
                    self.stock_data_cache[stock_code] = {
                        'name': stock_name,
                        'data': data
                    }
                    
                    print(f"成功加载股票 {stock_code} 数据，共 {len(data)} 条记录")
                    
                except Exception as e:
                    print(f"加载股票 {stock_code} 数据失败: {e}")
    
    def get_stock_name(self, stock_code):
        """根据股票代码获取股票名称"""
        # 先从股票列表中查找
        for stock in self.stock_list:
            if stock['code'] == stock_code:
                return stock['name']
        
        # 如果找不到，生成一个mock名称
        # 根据股票代码前缀判断类型
        if stock_code.startswith('000'):
            return f"深市A股{stock_code}"
        elif stock_code.startswith('002'):
            return f"中小板{stock_code}"
        elif stock_code.startswith('300'):
            return f"创业板{stock_code}"
        elif stock_code.startswith('600'):
            return f"沪市A股{stock_code}"
        elif stock_code.startswith('688'):
            return f"科创板{stock_code}"
        else:
            return f"股票{stock_code}"
    
    def get_latest_stock_data(self):
        """获取所有股票的最新数据"""
        latest_data = []
        
        for stock_code, stock_info in self.stock_data_cache.items():
            data = stock_info['data']
            if not data.empty:
                # 获取最新日期
                latest_date = data.iloc[-1]['日期']
                if isinstance(latest_date, str):
                    latest_date_str = latest_date
                else:
                    latest_date_str = latest_date.strftime('%Y-%m-%d')
                
                latest_data.append({
                    '股票代码': stock_code,
                    '股票名称': stock_info['name'],
                    '最新日期': latest_date_str,
                    '最新涨幅': f"{data.iloc[-1]['涨跌幅']:.2f}%",
                    '开盘价': f"{data.iloc[-1]['开盘价']:.2f}",
                    '收盘价': f"{data.iloc[-1]['收盘价']:.2f}",
                    '最高价': f"{data.iloc[-1]['最高价']:.2f}",
                    '最低价': f"{data.iloc[-1]['最低价']:.2f}",
                    '换手率': f"{data.iloc[-1]['换手率']:.2f}%",
                    '交易量': f"{data.iloc[-1]['交易量']:,.0f}",
                    '交易额': f"{data.iloc[-1]['交易额']:,.0f}"
                })
        
        return latest_data
    
    def get_stock_data(self, stock_code, timeframe='D'):
        """获取股票数据（简化版本）"""
        if stock_code not in self.stock_data_cache:
            return None
        
        stock_info = self.stock_data_cache[stock_code]
        data = stock_info['data']
        
        if data.empty:
            return None
        
        if timeframe == 'D':
            # 日线数据，显示全部数据
            display_data = data.copy()
            display_data.set_index('日期', inplace=True)
        elif timeframe == 'W':
            # 周线数据，需要重新采样
            data_copy = data.copy()
            data_copy.set_index('日期', inplace=True)
            display_data = data_copy.resample('W').agg({
                '开盘价': 'first',
                '收盘价': 'last',
                '最高价': 'max',
                '最低价': 'min',
                '交易量': 'sum',
                '交易额': 'sum',
                '换手率': 'mean',
                '涨跌幅': 'sum'
            }).dropna()
        else:  # 月线
            data_copy = data.copy()
            data_copy.set_index('日期', inplace=True)
            display_data = data_copy.resample('M').agg({
                '开盘价': 'first',
                '收盘价': 'last',
                '最高价': 'max',
                '最低价': 'min',
                '交易量': 'sum',
                '交易额': 'sum',
                '换手率': 'mean',
                '涨跌幅': 'sum'
            }).dropna()
        
        return display_data
    
    def get_stock_data_for_chart(self, stock_code, timeframe='D'):
        """获取用于图表显示的股票数据"""
        if stock_code not in self.stock_data_cache:
            return None, ""
        
        stock_info = self.stock_data_cache[stock_code]
        data = stock_info['data']
        
        if data.empty:
            return None, ""
        
        if timeframe == 'D':
            # 日线数据，显示全部数据
            display_data = data.copy()
            period_text = "日线"
        elif timeframe == 'W':
            # 周线数据，需要重新采样
            data_copy = data.copy()
            data_copy.set_index('日期', inplace=True)
            weekly_data = data_copy.resample('W').agg({
                '开盘价': 'first',
                '收盘价': 'last',
                '最高价': 'max',
                '最低价': 'min',
                '交易量': 'sum',
                '交易额': 'sum',
                '换手率': 'mean',
                '涨跌幅': 'sum'
            }).dropna()
            display_data = weekly_data
            period_text = "周线"
        else:  # 月线
            data_copy = data.copy()
            data_copy.set_index('日期', inplace=True)
            monthly_data = data_copy.resample('M').agg({
                '开盘价': 'first',
                '收盘价': 'last',
                '最高价': 'max',
                '最低价': 'min',
                '交易量': 'sum',
                '交易额': 'sum',
                '换手率': 'mean',
                '涨跌幅': 'sum'
            }).dropna()
            display_data = monthly_data
            period_text = "月线"
        
        return display_data, period_text
    
    def get_available_stocks(self):
        """获取可用的股票列表"""
        return list(self.stock_data_cache.keys())
    
    def load_specific_stock_data(self, stock_code):
        """只加载特定股票的数据，适用于URL参数查看"""
        # 检查股票数据是否已加载
        if stock_code in self.stock_data_cache:
            print(f"股票 {stock_code} 数据已加载")
            return True
            
        # 检查本地是否有该股票的数据文件
        file_path = os.path.join(DATA_DIR, f"{stock_code}.csv")
        if os.path.exists(file_path):
            try:
                # 读取CSV文件
                data = pd.read_csv(file_path)
                
                # 处理日期列
                if '日期' in data.columns:
                    data['日期'] = pd.to_datetime(data['日期'])
                else:
                    print(f"警告：股票 {stock_code} 缺少日期列")
                    return False
                
                # 确保所有必需列都存在
                required_columns = ['日期', '涨跌幅', '开盘价', '收盘价', '最高价', '最低价', '换手率', '交易量', '交易额']
                for col in required_columns:
                    if col not in data.columns:
                        print(f"警告：股票 {stock_code} 缺少列 {col}，使用默认值")
                        if col == '涨跌幅':
                            data[col] = 0.0
                        elif col in ['开盘价', '收盘价', '最高价', '最低价']:
                            data[col] = data['收盘价'] if '收盘价' in data.columns else 10.0
                        elif col == '换手率':
                            data[col] = 1.0
                        elif col == '交易量':
                            data[col] = 1000000
                        elif col == '交易额':
                            data[col] = data['收盘价'] * data['交易量'] if '收盘价' in data.columns and '交易量' in data.columns else 10000000
                
                # 获取股票名称
                stock_name = self.get_stock_name(stock_code)
                
                # 更新缓存
                self.stock_data_cache[stock_code] = {
                    'name': stock_name,
                    'data': data
                }
                
                print(f"成功加载股票 {stock_code} 数据，共 {len(data)} 条记录")
                return True
                
            except Exception as e:
                print(f"加载股票 {stock_code} 数据失败: {e}")
                return False
        else:
            # 文件不存在，尝试从远程获取
            try:
                # 查找股票名称
                stock_name = self.get_stock_name(stock_code)
                
                # 获取最近一年的数据
                start_date = (datetime.now() - timedelta(days=DEFAULT_DAYS)).strftime('%Y%m%d')
                
                # 获取新数据
                new_data = self.fetch_stock_data(stock_code, start_date)
                
                if not new_data.empty:
                    # 保存到本地
                    new_data.to_csv(file_path, index=False)
                    
                    # 更新缓存
                    self.stock_data_cache[stock_code] = {
                        'name': stock_name,
                        'data': new_data
                    }
                    
                    print(f"成功从远程获取股票 {stock_code} 数据，共 {len(new_data)} 条记录")
                    return True
                else:
                    print(f"无法获取股票 {stock_code} 数据")
                    return False
                    
            except Exception as e:
                print(f"获取股票 {stock_code} 数据失败: {e}")
                return False

# 全局数据管理器实例（延迟初始化）
_data_manager = None

def get_data_manager():
    """获取数据管理器实例"""
    global _data_manager
    if _data_manager is None:
        _data_manager = StockDataManager()
    return _data_manager

# 为了兼容性，提供一个属性
class DataManagerProxy:
    def __getattr__(self, name):
        return getattr(get_data_manager(), name)

data_manager = DataManagerProxy() 