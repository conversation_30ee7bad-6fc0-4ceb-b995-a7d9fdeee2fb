# 🎯 双损失函数深度学习股票预测系统使用指南

## 📋 系统概述

双损失函数深度学习股票预测系统是一个专门为股票选择优化的深度学习框架，通过创新的双损失函数设计，专注于提升预测精确率，帮助您从大量候选股票中筛选出最有潜力的投资标的。

## 🚀 快速开始

### 1. 训练模型

```bash
python train_dual_loss_models.py
```

**输入要求**：
- 选择包含历史股票数据的Excel文件
- 文件必须包含"5日成功选股"列作为目标变量
- 推荐使用：`选股分析结果/选股分析结果_20250730_225530.xlsx`

**输出结果**：
- 训练好的模型文件保存在 `ml/dual_loss_training_YYYYMMDD_HHMMSS/` 文件夹
- 包含4种深度学习模型：PrecisionMLP、ResidualNet、AttentionNet、EnsembleModel
- 生成详细的训练报告和性能评估

### 2. 预测股票

```bash
python predict_dual_loss.py -i [预测数据文件] --model_folder [模型文件夹]
```

**示例**：
```bash
python predict_dual_loss.py -i 选股分析结果/选股分析结果_20250730_225041.xlsx --model_folder ml/dual_loss_training_20250803_020215
```

## 📊 功能特点

### ✨ 智能预测验证
- **自动检测实际涨幅数据**：如果预测数据中包含"5日最大涨幅"列，系统会自动显示预测准确性
- **实时成功率计算**：自动计算预测成功率（默认以10%涨幅为成功标准）
- **可视化结果展示**：生成包含实际涨幅对比的HTML报告

### 🎯 HTML报告内容

预测完成后会生成详细的HTML可视化报告，包含：

1. **统计概览卡片**：
   - 📈 总预测数量
   - 🤖 使用模型数量  
   - 📈 平均预测概率
   - 🎯 最高预测概率
   - ✅ 预测成功数量（如有实际数据）
   - 🎯 预测成功率（如有实际数据）

2. **详细预测表格**：
   - 排名、模型、股票代码、预测概率、概率条、买入日期
   - **实际涨幅**（如有数据）：显示实际涨幅百分比，绿色表示成功，红色表示失败
   - **预测结果**（如有数据）：✅ 成功 / ❌ 失败 / ❓ 未知

3. **图表分析**：
   - 各模型预测数量分布
   - 预测概率分布直方图

## 🔧 技术架构

### 双损失函数设计
- **精确率损失**：专门优化预测为正样本且实际为正样本的概率
- **平衡损失**：避免模型过度保守或激进，保持预测数量的合理性

### 深度学习模型
1. **PrecisionMLP**：5层深度网络，专注精确率提升
2. **ResidualNet**：增强残差网络，5个残差块+压缩层
3. **AttentionNet**：8头注意力机制，深度特征融合
4. **EnsembleModel**：集成多个模型的预测结果

## 📈 性能表现

### 训练结果示例
- **PrecisionMLP**: 精确率26.09%，预测23个股票
- **ResidualNet**: 精确率22.58%，预测31个股票  
- **AttentionNet**: 精确率19.05%，预测21个股票

### 相比随机选择的优势
- 随机选择期望成功率：9.6%
- 模型预测精确率：19-26%（提升2-3倍）
- 预测数量可控：可以调整为选择Top10、Top20等

## 📁 输出文件结构

```
ml/dual_loss_prediction_YYYYMMDD_HHMMSS/
├── prediction_report.html          # 可视化HTML报告
├── 预测结果_YYYYMMDD_HHMMSS.xlsx    # Excel预测结果
└── prediction_info.json            # 预测信息和统计数据
```

## 🎯 使用建议

1. **训练数据选择**：使用包含足够历史数据的文件进行训练
2. **预测验证**：优先使用包含"5日最大涨幅"列的数据进行预测验证
3. **阈值调整**：可以根据需要调整成功标准（默认10%涨幅）
4. **模型选择**：ResidualNet通常表现最稳定，建议优先使用

## 🔍 故障排除

### 常见问题
1. **模型加载失败**：确保模型文件夹路径正确，模型架构匹配
2. **数据格式错误**：确保Excel文件包含必要的列（股票代码、买入日期等）
3. **内存不足**：如果数据量过大，可以考虑分批处理

### 性能优化
- 使用GPU加速训练（如果可用）
- 调整batch_size和学习率参数
- 增加训练数据量以提升模型泛化能力

---

**🎉 恭喜！您现在已经掌握了双损失函数深度学习股票预测系统的使用方法。通过这个系统，您可以显著提升股票选择的成功率，实现更精准的投资决策！**
