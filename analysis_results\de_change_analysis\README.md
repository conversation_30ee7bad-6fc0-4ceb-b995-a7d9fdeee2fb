# 多条件股票筛选与成功率分析系统

## 📊 分析概述

本文件夹包含了一个完整的多条件股票筛选与成功率分析系统，支持D-E点涨跌幅、J值、成交量等多种技术指标的自定义组合分析。

## 📁 文件说明

### 核心分析脚本
- `analyze_de_change_success.py` - 🔥 **主要分析脚本**，支持多条件筛选和交互式分析
- `config_based_analysis.py` - 基于配置文件的批量策略分析
- `example_usage.py` - 使用示例和演示脚本

### 配置文件
- `custom_conditions.json` - 预定义的筛选策略和条件模板

### 分析结果
- `de_change_success_analysis.png` - 可视化分析图表
- `analysis_summary.md` - 分析结果总结

## 🔍 主要发现

### 核心统计数据
- **总样本**: 5,126条数据
- **整体成功率**: 12.2%
- **D-E平均涨跌幅**: -2.11%

### 最佳区间
1. **极大下跌 (≤-15%)**: 成功率20.0% (20个样本)
2. **大幅下跌 (-10%~-5%)**: 成功率16.7% (593个样本)
3. **很大下跌 (-15%~-10%)**: 成功率14.9% (94个样本)

### 关键洞察
- **反直觉现象**: D-E点下跌幅度越大，后续成功率越高
- **逢低买入效应**: 技术调整后的股票更容易反弹
- **最优策略**: 重点关注D-E点下跌5%-15%的股票

## 🚀 使用方法

### 1. 交互式分析（推荐）
```bash
cd analysis_results/de_change_analysis
python analyze_de_change_success.py
```
启动交互式界面，支持：
- 预定义条件分析
- 自定义条件创建
- 批量条件测试
- 原始D-E点分析

### 2. 命令行模式
```bash
# 预定义条件分析
python analyze_de_change_success.py predefined

# 交互式模式
python analyze_de_change_success.py interactive

# 原始D-E分析
python analyze_de_change_success.py original
```

### 3. 配置文件模式
```bash
# 分析所有预定义策略
python config_based_analysis.py all

# 分析指定策略
python config_based_analysis.py strategy:value_hunting

# 快速筛选分析
python config_based_analysis.py quick
```

### 4. 使用示例
```bash
# 运行所有使用示例
python example_usage.py
```

### 数据要求
- Excel文件包含以下关键列：
  - `D点收盘`, `E点收盘` (必需)
  - `5日成功选股` (目标列)
  - `E点J值`, `D点J值` (可选)
  - `E点成交量`, `D点成交量` (可选)
  - 其他技术指标列 (可选)

## 🎯 支持的筛选条件

### 主要技术指标
- **价格变化**: D-E涨跌幅、各点实体涨跌幅、价格振幅
- **J值指标**: E点J值、D点J值、J值相对变化
- **成交量**: E点成交量、D点成交量、成交量比率
- **时间周期**: A-B天数、B-C天数、C-D天数、D-E天数
- **技术形态**: 上影线涨幅、上影线/实体比率

### 操作符支持
- `>`, `>=`, `<`, `<=` - 数值比较
- `==` - 等于
- `between` - 区间筛选
- `not_between` - 区间外筛选

### 预定义策略
1. **超保守策略** - 极低风险，追求稳定收益
2. **价值挖掘策略** - 寻找被低估的优质股票
3. **动量反转策略** - 捕捉超跌反弹机会
4. **技术突破策略** - 寻找技术形态突破点
5. **成交量异动策略** - 关注成交量异常放大
6. **超跌反弹策略** - 专门捕捉超跌后的反弹
7. **均衡配置策略** - 平衡风险与收益

## 📈 实际应用

### 自定义条件示例
```python
# 示例1: 技术调整买入
conditions = [
    {"feature": "D-E涨跌幅", "operator": "between", "value": [-10, -3]},
    {"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": [-5, 0]},
    {"feature": "E点成交量/D点成交量", "operator": ">", "value": 1.2}
]

# 示例2: 成交量异动
conditions = [
    {"feature": "E点成交量/D点成交量", "operator": ">", "value": 2.0},
    {"feature": "D-E涨跌幅", "operator": ">", "value": -5}
]
```

### 策略建议
1. **保守型**: 使用`ultra_conservative`策略
2. **平衡型**: 使用`balanced_approach`策略
3. **激进型**: 使用`momentum_reversal`或`oversold_bounce`策略
4. **自定义**: 根据市场环境组合多个条件

### 风险控制
- 建议同时使用多个条件降低风险
- 定期回测验证策略有效性
- 根据市场环境调整参数
- 设置合理的止损和止盈点位

## 📊 图表说明

生成的图表包含：
1. D-E涨跌幅分布对比（成功vs失败案例）
2. 各区间成功率统计
3. 样本数量分布
4. D-E涨跌幅与实际涨幅散点图

## 🔄 更新记录

- 2025-08-03: 初始版本，包含详细区间分析
- 支持15%和10%范围的细分统计
- 修复中文字体渲染问题
