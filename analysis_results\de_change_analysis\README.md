# D-E点涨跌幅与5日选股成功率分析

## 📊 分析概述

本文件夹包含了对D-E点涨跌幅与5日选股成功率关系的详细分析。

## 📁 文件说明

### 分析脚本
- `analyze_de_change_success.py` - 主要分析脚本，统计D-E点涨跌幅与成功率的关系
- `analyze_de_ma_stability.py` - 分析D-E点移动平均线企稳情况

### 分析结果
- `de_change_success_analysis.png` - 可视化分析图表
- `analysis_summary.md` - 分析结果总结

## 🔍 主要发现

### 核心统计数据
- **总样本**: 5,126条数据
- **整体成功率**: 12.2%
- **D-E平均涨跌幅**: -2.11%

### 最佳区间
1. **极大下跌 (≤-15%)**: 成功率20.0% (20个样本)
2. **大幅下跌 (-10%~-5%)**: 成功率16.7% (593个样本)
3. **很大下跌 (-15%~-10%)**: 成功率14.9% (94个样本)

### 关键洞察
- **反直觉现象**: D-E点下跌幅度越大，后续成功率越高
- **逢低买入效应**: 技术调整后的股票更容易反弹
- **最优策略**: 重点关注D-E点下跌5%-15%的股票

## 🚀 使用方法

### 运行分析
```bash
cd analysis_results/de_change_analysis
python analyze_de_change_success.py
```

### 数据要求
- 需要包含D点收盘、E点收盘、5日成功选股等列的Excel文件
- 数据路径在脚本中可配置

## 📈 实际应用

### 选股建议
1. **首选**: D-E涨跌幅在-15%~-5%区间的股票
2. **次选**: D-E涨跌幅在0%~1%区间的股票
3. **避免**: D-E涨跌幅在3%~5%区间的股票

### 风险提示
- 历史数据分析结果，不构成投资建议
- 市场环境变化可能影响策略有效性
- 建议结合其他技术指标综合判断

## 📊 图表说明

生成的图表包含：
1. D-E涨跌幅分布对比（成功vs失败案例）
2. 各区间成功率统计
3. 样本数量分布
4. D-E涨跌幅与实际涨幅散点图

## 🔄 更新记录

- 2025-08-03: 初始版本，包含详细区间分析
- 支持15%和10%范围的细分统计
- 修复中文字体渲染问题
