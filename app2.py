import dash
from dash import dcc, html, Input, Output, State, callback_context, no_update, ALL
import dash_bootstrap_components as dbc
import pandas as pd
import json
import os
from dash import dash_table
from datetime import datetime, timedelta
import numpy as np

def compute_kdj(df, n=9, m1=3, m2=3):
    """
    计算KDJ指标
    :param df: 包含high, low, close列的DataFrame
    :param n: RSV计算周期，默认9
    :param m1: K值平滑周期，默认3
    :param m2: D值平滑周期，默认3
    :return: 包含K, D, J列的DataFrame
    """
    df = df.copy()

    # 计算RSV (Raw Stochastic Value)
    low_n = df['low'].rolling(window=n, min_periods=1).min()
    high_n = df['high'].rolling(window=n, min_periods=1).max()

    # 避免除零错误
    rsv = np.where(high_n != low_n,
                   (df['close'] - low_n) / (high_n - low_n) * 100,
                   50)  # 当高低相等时，RSV设为50

    df['RSV'] = rsv

    # 计算K值 (使用指数移动平均)
    df['K'] = df['RSV'].ewm(alpha=1/m1, adjust=False).mean()

    # 计算D值 (K值的指数移动平均)
    df['D'] = df['K'].ewm(alpha=1/m2, adjust=False).mean()

    # 计算J值
    df['J'] = 3 * df['K'] - 2 * df['D']

    return df[['K', 'D', 'J']]

# 初始化Dash应用
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP], suppress_callback_exceptions=True)
app.title = "选股分析助手"

# 选股分析页面
analysis_page = dbc.Container([
    html.H4("选股分析", className="mb-4 mt-2"),
    dbc.Row([
        dbc.Col([
            dbc.Form([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("选择日志文件", className="form-label"),
                        dcc.Dropdown(
                            id="log-file-dropdown",
                            placeholder="选择日志文件...",
                            style={"width": "100%"}
                        )
                    ], width=6),
                    dbc.Col([
                        dbc.Label("选择股票数据目录", className="form-label"),
                        dcc.Dropdown(
                            id="analysis-data-dir",
                            options=[
                                {"label": "stock_data", "value": "stock_data"},
                                {"label": "stock_data - 副本", "value": "stock_data - 副本"},
                                {"label": "stock_data - 副本 (3)", "value": "stock_data - 副本 (3)"}
                            ],
                            value="stock_data",
                            placeholder="选择数据目录..."
                        )
                    ], width=6)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Button("开始分析", id="start-analysis-btn", color="primary", className="me-2"),
                        dbc.Button("导出结果", id="export-analysis-btn", color="success", className="me-2"),
                        html.Div(id="analysis-progress", className="mt-2")
                    ])
                ])
            ])
        ])
    ], className="mb-4"),
    
    # 分析结果表格
    dbc.Row([
        dbc.Col([
            html.H5("选股分析结果", className="mb-3"),
            dash_table.DataTable(
                id="analysis-table",
                columns=[
                    {"name": "策略", "id": "策略"},
                    {"name": "日期", "id": "日期"},
                    {"name": "股票", "id": "股票"},
                    {"name": "买入日期", "id": "买入日期"},
                    {"name": "评分", "id": "评分", "type": "numeric"},
                    {"name": "3日成功选股", "id": "3日成功选股"},
                    {"name": "5日成功选股", "id": "5日成功选股"},
                    # 未来收益率
                    {"name": "3日最大涨幅", "id": "3日最大涨幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    {"name": "3日最大跌幅", "id": "3日最大跌幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    {"name": "5日最大涨幅", "id": "5日最大涨幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    {"name": "5日最大跌幅", "id": "5日最大跌幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    {"name": "10日最大涨幅", "id": "10日最大涨幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    {"name": "10日最大跌幅", "id": "10日最大跌幅", "type": "numeric", "format": {"specifier": ".1%"}},
                    # A点信息
                    {"name": "A点日期", "id": "A点日期"},
                    {"name": "A点开盘", "id": "A点开盘", "type": "numeric"},
                    {"name": "A点收盘", "id": "A点收盘", "type": "numeric"},
                    {"name": "A点最高", "id": "A点最高", "type": "numeric"},
                    {"name": "A点最低", "id": "A点最低", "type": "numeric"},
                    {"name": "A点成交量", "id": "A点成交量", "type": "numeric"},
                    {"name": "A点实体涨跌幅", "id": "A点实体涨跌幅"},
                    {"name": "A点价格振幅", "id": "A点价格振幅"},
                    # B点信息
                    {"name": "B点日期", "id": "B点日期"},
                    {"name": "B点开盘", "id": "B点开盘", "type": "numeric"},
                    {"name": "B点收盘", "id": "B点收盘", "type": "numeric"},
                    {"name": "B点最高", "id": "B点最高", "type": "numeric"},
                    {"name": "B点最低", "id": "B点最低", "type": "numeric"},
                    {"name": "B点成交量", "id": "B点成交量", "type": "numeric"},
                    {"name": "B点实体涨跌幅", "id": "B点实体涨跌幅"},
                    {"name": "B点价格振幅", "id": "B点价格振幅"},
                    # C点信息
                    {"name": "C点日期", "id": "C点日期"},
                    {"name": "C点开盘", "id": "C点开盘", "type": "numeric"},
                    {"name": "C点收盘", "id": "C点收盘", "type": "numeric"},
                    {"name": "C点最高", "id": "C点最高", "type": "numeric"},
                    {"name": "C点最低", "id": "C点最低", "type": "numeric"},
                    {"name": "C点成交量", "id": "C点成交量", "type": "numeric"},
                    {"name": "C点实体涨跌幅", "id": "C点实体涨跌幅"},
                    {"name": "C点价格振幅", "id": "C点价格振幅"},
                    # D点信息
                    {"name": "D点日期", "id": "D点日期"},
                    {"name": "D点开盘", "id": "D点开盘", "type": "numeric"},
                    {"name": "D点收盘", "id": "D点收盘", "type": "numeric"},
                    {"name": "D点最高", "id": "D点最高", "type": "numeric"},
                    {"name": "D点最低", "id": "D点最低", "type": "numeric"},
                    {"name": "D点成交量", "id": "D点成交量", "type": "numeric"},
                    {"name": "D点实体涨跌幅", "id": "D点实体涨跌幅"},
                    {"name": "D点价格振幅", "id": "D点价格振幅"},
                    # E点信息
                    {"name": "E点日期", "id": "E点日期"},
                    {"name": "E点开盘", "id": "E点开盘", "type": "numeric"},
                    {"name": "E点收盘", "id": "E点收盘", "type": "numeric"},
                    {"name": "E点最高", "id": "E点最高", "type": "numeric"},
                    {"name": "E点最低", "id": "E点最低", "type": "numeric"},
                    {"name": "E点成交量", "id": "E点成交量", "type": "numeric"},
                    {"name": "E点实体涨跌幅", "id": "E点实体涨跌幅"},
                    {"name": "E点价格振幅", "id": "E点价格振幅"},
                    # KDJ相关信息
                    {"name": "E点J值", "id": "E点J值", "type": "numeric", "format": {"specifier": ".2f"}},
                    {"name": "E点J值相对D点J值涨幅", "id": "E点J值相对D点J值涨幅", "type": "numeric", "format": {"specifier": ".2%"}},
                    {"name": "E点相对D点收盘价涨幅", "id": "E点相对D点收盘价涨幅", "type": "numeric", "format": {"specifier": ".2%"}},
                    # 区间信息
                    {"name": "A-B涨幅", "id": "A-B涨幅"},
                    {"name": "A-B天数", "id": "A-B天数", "type": "numeric"},
                    {"name": "B-C跌幅", "id": "B-C跌幅"},
                    {"name": "B-C天数", "id": "B-C天数", "type": "numeric"},
                    {"name": "C-D涨幅", "id": "C-D涨幅"},
                    {"name": "C-D天数", "id": "C-D天数", "type": "numeric"},
                    {"name": "D-E涨幅", "id": "D-E涨幅"},
                    {"name": "D-E天数", "id": "D-E天数", "type": "numeric"},
                    # 成交量比例信息
                    {"name": "D点成交量/C-D均量", "id": "D点成交量/C-D均量", "type": "numeric"},
                    {"name": "D点上影线涨幅", "id": "D点上影线涨幅", "type": "numeric"},
                    {"name": "D点上影线/实体", "id": "D点上影线/实体", "type": "numeric"},
                    {"name": "E点成交量/C-D均量", "id": "E点成交量/C-D均量", "type": "numeric"},
                    {"name": "E点成交量/D点成交量", "id": "E点成交量/D点成交量", "type": "numeric"}
                ],
                data=[],
                style_table={'overflowX': 'auto', 'overflowY': 'auto', 'maxHeight': '600px'},
                style_cell={
                    'textAlign': 'center',
                    'minWidth': '80px',
                    'maxWidth': '120px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'fontSize': '12px',
                    'padding': '8px'
                },
                style_cell_conditional=[
                    # 基本信息列 - 较窄
                    {'if': {'column_id': '策略'}, 'minWidth': '120px', 'maxWidth': '150px'},
                    {'if': {'column_id': '日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '股票'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': '买入日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '评分'}, 'minWidth': '60px', 'maxWidth': '80px'},
                    {'if': {'column_id': '成功选股'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    # 日期列 - 较窄
                    {'if': {'column_id': 'A点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 价格列 - 中等宽度
                    {'if': {'column_id': 'A点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    # 成交量列 - 较宽
                    {'if': {'column_id': 'A点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 百分比列 - 中等宽度
                    {'if': {'column_id': 'A点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'A点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 区间信息列 - 中等宽度
                    {'if': {'column_id': 'A-B涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A-B天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'B-C跌幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B-C天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'C-D涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C-D天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'D-E涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D-E天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    # 成交量比例列 - 较宽
                    {'if': {'column_id': 'D点成交量/C-D均量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    {'if': {'column_id': 'D点上影线涨幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点上影线/实体'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点成交量/C-D均量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    {'if': {'column_id': 'E点成交量/D点成交量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    # 未来收益率列 - 中等宽度
                    {'if': {'column_id': '3日最大涨幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '3日最大跌幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '5日最大涨幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '5日最大跌幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '10日最大涨幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '10日最大跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                ],
                page_size=20,
                sort_action='native',
                filter_action='native',
                sort_mode='multi',
                column_selectable=False,
                row_selectable=False,
                style_as_list_view=True,
                css=[{
                    'selector': '.dash-table-container',
                    'rule': 'font-family: "Microsoft YaHei", Arial, sans-serif;'
                }],
                style_header={
                    'backgroundColor': '#f8f9fa',
                    'fontWeight': 'bold',
                    'textAlign': 'center',
                    'fontSize': '11px',
                    'padding': '6px'
                },
                style_data_conditional=[
                    # 3日成功选股列 - 成功为绿色，失败为红色
                    {
                        'if': {'column_id': '3日成功选股', 'filter_query': '{3日成功选股} = 成功'},
                        'backgroundColor': '#d4edda',
                        'color': '#155724',
                        'fontWeight': 'bold'
                    },
                    {
                        'if': {'column_id': '3日成功选股', 'filter_query': '{3日成功选股} = 失败'},
                        'backgroundColor': '#f8d7da',
                        'color': '#721c24',
                        'fontWeight': 'bold'
                    },
                    # 5日成功选股列 - 成功为绿色，失败为红色
                    {
                        'if': {'column_id': '5日成功选股', 'filter_query': '{5日成功选股} = 成功'},
                        'backgroundColor': '#d4edda',
                        'color': '#155724',
                        'fontWeight': 'bold'
                    },
                    {
                        'if': {'column_id': '5日成功选股', 'filter_query': '{5日成功选股} = 失败'},
                        'backgroundColor': '#f8d7da',
                        'color': '#721c24',
                        'fontWeight': 'bold'
                    },
                    # 3日最大涨幅 - 绝对值超过1%标为红色
                    {
                        'if': {
                            'column_id': '3日最大涨幅',
                            'filter_query': '{3日最大涨幅} > 0.01 || {3日最大涨幅} < -0.01'
                        },
                        'color': '#dc3545',
                        'fontWeight': 'bold'
                    },
                    # 3日最大跌幅 - 绝对值超过1%标为绿色
                    {
                        'if': {
                            'column_id': '3日最大跌幅',
                            'filter_query': '{3日最大跌幅} > 0.01 || {3日最大跌幅} < -0.01'
                        },
                        'color': '#28a745',
                        'fontWeight': 'bold'
                    },
                    # 5日最大涨幅 - 绝对值超过1%标为红色
                    {
                        'if': {
                            'column_id': '5日最大涨幅',
                            'filter_query': '{5日最大涨幅} > 0.01 || {5日最大涨幅} < -0.01'
                        },
                        'color': '#dc3545',
                        'fontWeight': 'bold'
                    },
                    # 5日最大跌幅 - 绝对值超过1%标为绿色
                    {
                        'if': {
                            'column_id': '5日最大跌幅',
                            'filter_query': '{5日最大跌幅} > 0.01 || {5日最大跌幅} < -0.01'
                        },
                        'color': '#28a745',
                        'fontWeight': 'bold'
                    },
                    # 10日最大涨幅 - 绝对值超过1%标为红色
                    {
                        'if': {
                            'column_id': '10日最大涨幅',
                            'filter_query': '{10日最大涨幅} > 0.01 || {10日最大涨幅} < -0.01'
                        },
                        'color': '#dc3545',
                        'fontWeight': 'bold'
                    },
                    # 10日最大跌幅 - 绝对值超过1%标为绿色
                    {
                        'if': {
                            'column_id': '10日最大跌幅',
                            'filter_query': '{10日最大跌幅} > 0.01 || {10日最大跌幅} < -0.01'
                        },
                        'color': '#28a745',
                        'fontWeight': 'bold'
                    }
                ]
            )
        ])
    ]),
    
    # 统计信息
    dbc.Row([
        dbc.Col([
            html.H5("统计信息", className="mb-3"),
            html.Div(id="analysis-stats")
        ])
    ])
], fluid=True)

# 应用布局
app.layout = dbc.Container([
    dbc.NavbarSimple(
        brand="选股分析助手",
        brand_href="#",
        color="primary",
        dark=True,
    ),
    analysis_page,
], fluid=True)

# === 选股分析相关回调函数 ===

@app.callback(
    Output("log-file-dropdown", "options"),
    [Input("analysis-data-dir", "value")]
)
def update_log_file_options(data_dir):
    """更新日志文件选项"""
    import os
    log_files = []
    logs_dir = "logs"
    
    if os.path.exists(logs_dir):
        for file in os.listdir(logs_dir):
            if file.endswith('.log') and 'JZVolumeShrinkSelector' in file:
                log_files.append({
                    "label": file,
                    "value": os.path.join(logs_dir, file)
                })
    
    return log_files

def parse_log_file(log_file_path):
    """解析日志文件，提取选股结果"""
    import pandas as pd
    from datetime import datetime
    
    results = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用字符串分割找到所有选股记录
        start_marker = "=== 详细参数信息 ==="
        end_marker = "=== 参数信息结束 ==="
        
        # 找到所有开始和结束标记的位置
        start_positions = []
        end_positions = []
        
        pos = 0
        while True:
            start_pos = content.find(start_marker, pos)
            if start_pos == -1:
                break
            start_positions.append(start_pos)
            pos = start_pos + len(start_marker)
        
        pos = 0
        while True:
            end_pos = content.find(end_marker, pos)
            if end_pos == -1:
                break
            end_positions.append(end_pos)
            pos = end_pos + len(end_marker)
        
        print(start_positions)
        print(end_positions)

        # 提取每对标记之间的内容
        for i, start_pos in enumerate(start_positions):
            if i < len(end_positions):
                end_pos = end_positions[i]
                section = content[start_pos:end_pos]
                # print(f"section: {section}")
                try:
                    # 使用字符串匹配提取信息
                    result = extract_stock_info(section)
                    print(f"result: {result}")
                    if result:
                        # print(f"result: {result}")
                        results.append(result)
                except Exception as e:
                    print(f"解析选股记录时出错: {e}")
                    continue
    
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
    
    return results

def extract_stock_info(section):
    """从选股记录中提取股票信息"""
    import re
    
    try:
        print("开始解析选股记录...")
        print(f"原始section: {section[:200]}...")  # 只打印前200个字符
        
        # 提取股票代码
        code_match = re.search(r'股票代码:\s*(\d+)', section)
        if not code_match:
            print(f"股票代码匹配失败")
            return None
        code = code_match.group(1)
        print(f"股票代码: {code}")

        # 提取A点信息 - 修改正则表达式以匹配实际格式
        a_match = re.search(r'A点:\s*日期=(\d{4}-\d{2}-\d{2})\s*00:00:00,\s*开盘=([\d.]+),\s*收盘=([\d.]+),\s*最高=([\d.]+),\s*最低=([\d.]+),\s*成交量=([\d.]+),\s*实体涨跌幅=([\d.-]+),\s*价格振幅=([\d.]+)', section)
        if not a_match:
            print(f"A点匹配失败")
            return None
        print(f"A点匹配成功: {a_match.groups()}")

        # 提取B点信息
        b_match = re.search(r'B点:\s*日期=(\d{4}-\d{2}-\d{2})\s*00:00:00,\s*开盘=([\d.]+),\s*收盘=([\d.]+),\s*最高=([\d.]+),\s*最低=([\d.]+),\s*成交量=([\d.]+),\s*实体涨跌幅=([\d.-]+),\s*价格振幅=([\d.]+)', section)
        if not b_match:
            print(f"B点匹配失败")
            return None
        print(f"B点匹配成功: {b_match.groups()}")

        # 提取C点信息
        c_match = re.search(r'C点:\s*日期=(\d{4}-\d{2}-\d{2})\s*00:00:00,\s*开盘=([\d.]+),\s*收盘=([\d.]+),\s*最高=([\d.]+),\s*最低=([\d.]+),\s*成交量=([\d.]+),\s*实体涨跌幅=([\d.-]+),\s*价格振幅=([\d.]+)', section)
        if not c_match:
            print(f"C点匹配失败")
            return None
        print(f"C点匹配成功: {c_match.groups()}")

        # 提取D点信息
        d_match = re.search(r'D点:\s*日期=(\d{4}-\d{2}-\d{2})\s*00:00:00,\s*开盘=([\d.]+),\s*收盘=([\d.]+),\s*最高=([\d.]+),\s*最低=([\d.]+),\s*成交量=([\d.]+),\s*实体涨跌幅=([\d.-]+),\s*价格振幅=([\d.]+)', section)
        if not d_match:
            print(f"D点匹配失败")
            return None
        print(f"D点匹配成功: {d_match.groups()}")
        
        # 提取E点信息
        e_match = re.search(r'E点:\s*日期=(\d{4}-\d{2}-\d{2})\s*00:00:00,\s*开盘=([\d.]+),\s*收盘=([\d.]+),\s*最高=([\d.]+),\s*最低=([\d.]+),\s*成交量=([\d.]+),\s*实体涨跌幅=([\d.-]+),\s*价格振幅=([\d.]+)', section)
        if not e_match:
            print(f"E点匹配失败")
            return None
        print(f"E点匹配成功: {e_match.groups()}")
        
        # 提取区间信息
        ab_match = re.search(r'A-B区间:\s*涨幅=([\d.]+),\s*时间间隔=(\d+)天', section)
        bc_match = re.search(r'B-C区间:\s*跌幅=([\d.]+),\s*时间间隔=(\d+)天', section)
        cd_match = re.search(r'C-D区间:\s*涨幅=([\d.]+),\s*时间间隔=(\d+)天', section)
        de_match = re.search(r'D-E区间:\s*涨幅=([\d.-]+),\s*时间间隔=(\d+)天', section)

        # 提取成交量信息
        d_vol_match = re.search(r'D点成交量/C-D均量=([\d.]+)', section)
        e_vol_match = re.search(r'E点成交量/C-D均量=([\d.]+)', section)
        d_shadow_match = re.search(r'D点上影线和实体比例上影线/实体=([\d.]+)', section)

        # 提取KDJ相关信息
        e_j_value_match = re.search(r'E点的J值=([\d.-]+)', section)
        e_vs_d_j_pct_match = re.search(r'E点J值相对D点J值的涨幅=([\d.-]+)', section)
        e_vs_d_pct_match = re.search(r'E点相对D点收盘价的涨幅=([\d.-]+)', section)
        
        # 构建结果
        result = {
            'code': code,
            'date': e_match.group(1),  # 使用E点日期作为选股日期
            'a_date': a_match.group(1),
            'a_open': float(a_match.group(2)),
            'a_close': float(a_match.group(3)),
            'a_high': float(a_match.group(4)),
            'a_low': float(a_match.group(5)),
            'a_vol': float(a_match.group(6)),
            'a_entity_pct': float(a_match.group(7)),
            'a_price_amplitude': float(a_match.group(8)),
            'b_date': b_match.group(1),
            'b_open': float(b_match.group(2)),
            'b_close': float(b_match.group(3)),
            'b_high': float(b_match.group(4)),
            'b_low': float(b_match.group(5)),
            'b_vol': float(b_match.group(6)),
            'b_entity_pct': float(b_match.group(7)),
            'b_price_amplitude': float(b_match.group(8)),
            'c_date': c_match.group(1),
            'c_open': float(c_match.group(2)),
            'c_close': float(c_match.group(3)),
            'c_high': float(c_match.group(4)),
            'c_low': float(c_match.group(5)),
            'c_vol': float(c_match.group(6)),
            'c_entity_pct': float(c_match.group(7)),
            'c_price_amplitude': float(c_match.group(8)),
            'd_date': d_match.group(1),
            'd_open': float(d_match.group(2)),
            'd_close': float(d_match.group(3)),
            'd_high': float(d_match.group(4)),
            'd_low': float(d_match.group(5)),
            'd_vol': float(d_match.group(6)),
            'd_entity_pct': float(d_match.group(7)),
            'd_price_amplitude': float(d_match.group(8)),
            'e_date': e_match.group(1),
            'e_open': float(e_match.group(2)),
            'e_close': float(e_match.group(3)),
            'e_high': float(e_match.group(4)),
            'e_low': float(e_match.group(5)),
            'e_vol': float(e_match.group(6)),
            'e_entity_pct': float(e_match.group(7)),
            'e_price_amplitude': float(e_match.group(8))
        }
        
        print(f"基本字段构建完成")
        
        # 添加区间信息
        if ab_match:
            result['ab_rise'] = float(ab_match.group(1))
            result['ab_days'] = int(ab_match.group(2))
            print(f"A-B区间: 涨幅={result['ab_rise']}, 天数={result['ab_days']}")
        if bc_match:
            result['bc_fall'] = float(bc_match.group(1))
            result['bc_days'] = int(bc_match.group(2))
            print(f"B-C区间: 跌幅={result['bc_fall']}, 天数={result['bc_days']}")
        if cd_match:
            result['cd_rise'] = float(cd_match.group(1))
            result['cd_days'] = int(cd_match.group(2))
            print(f"C-D区间: 涨幅={result['cd_rise']}, 天数={result['cd_days']}")
        if de_match:
            result['de_rise'] = float(de_match.group(1))
            result['de_days'] = int(de_match.group(2))
            print(f"D-E区间: 涨幅={result['de_rise']}, 天数={result['de_days']}")
        
        # 添加成交量信息
        if d_vol_match:
            result['d_vol_ratio'] = float(d_vol_match.group(1))
            print(f"D点成交量比例: {result['d_vol_ratio']}")
        if e_vol_match:
            result['e_vol_ratio'] = float(e_vol_match.group(1))
            print(f"E点成交量比例: {result['e_vol_ratio']}")
        if d_shadow_match:
            result['d_shadow_ratio'] = float(d_shadow_match.group(1))
            print(f"D点上影线比例: {result['d_shadow_ratio']}")

        # 添加KDJ相关信息
        if e_j_value_match:
            result['e_j_value'] = float(e_j_value_match.group(1))
            print(f"E点J值: {result['e_j_value']}")
        if e_vs_d_j_pct_match:
            result['e_vs_d_j_pct'] = float(e_vs_d_j_pct_match.group(1))
            print(f"E点J值相对D点J值涨幅: {result['e_vs_d_j_pct']}")
        if e_vs_d_pct_match:
            result['e_vs_d_pct'] = float(e_vs_d_pct_match.group(1))
            print(f"E点相对D点收盘价涨幅: {result['e_vs_d_pct']}")

        print(f"解析完成，结果: {result}")
        return result
        
    except Exception as e:
        print(f"解析选股记录时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def parse_point_line_regex(line, point_prefix):
    """使用正则表达式解析某个点的信息行"""
    import re
    
    try:
        point_name = point_prefix.replace("点:", "").lower()
        result = {}
        
        print(f"解析{point_prefix}信息: {line}")
        
        # 使用正则表达式提取各个字段
        date_match = re.search(r'日期=(\d{4}-\d{2}-\d{2})', line)
        if date_match:
            result[f'{point_name}_date'] = date_match.group(1)
            print(f"  {point_name}_date: {result[f'{point_name}_date']}")
        
        open_match = re.search(r'开盘=([\d.]+)', line)
        if open_match:
            result[f'{point_name}_open'] = float(open_match.group(1))
            print(f"  {point_name}_open: {result[f'{point_name}_open']}")
        
        close_match = re.search(r'收盘=([\d.]+)', line)
        if close_match:
            result[f'{point_name}_close'] = float(close_match.group(1))
            print(f"  {point_name}_close: {result[f'{point_name}_close']}")
        
        high_match = re.search(r'最高=([\d.]+)', line)
        if high_match:
            result[f'{point_name}_high'] = float(high_match.group(1))
            print(f"  {point_name}_high: {result[f'{point_name}_high']}")
        
        low_match = re.search(r'最低=([\d.]+)', line)
        if low_match:
            result[f'{point_name}_low'] = float(low_match.group(1))
            print(f"  {point_name}_low: {result[f'{point_name}_low']}")
        
        vol_match = re.search(r'成交量=([\d.]+)', line)
        if vol_match:
            result[f'{point_name}_vol'] = float(vol_match.group(1))
            print(f"  {point_name}_vol: {result[f'{point_name}_vol']}")
        
        entity_pct_match = re.search(r'实体涨跌幅=([\d.]+)', line)
        if entity_pct_match:
            result[f'{point_name}_entity_pct'] = float(entity_pct_match.group(1))
            print(f"  {point_name}_entity_pct: {result[f'{point_name}_entity_pct']}")
        
        price_amplitude_match = re.search(r'价格振幅=([\d.]+)', line)
        if price_amplitude_match:
            result[f'{point_name}_price_amplitude'] = float(price_amplitude_match.group(1))
            print(f"  {point_name}_price_amplitude: {result[f'{point_name}_price_amplitude']}")
        
        print(f"  {point_name}点解析完成: {result}")
        return result
    except Exception as e:
        print(f"解析点信息出错: {e}")
        return None

def parse_interval_line_regex(line, interval_prefix):
    """使用正则表达式解析区间信息行"""
    import re
    
    try:
        interval_name = interval_prefix.replace("区间:", "").lower().replace("-", "_")
        result = {}
        
        print(f"解析{interval_prefix}信息: {line}")
        
        # 解析涨幅
        rise_match = re.search(r'涨幅=([\d.-]+)', line)
        if rise_match:
            result[f'{interval_name}_rise'] = float(rise_match.group(1))
            print(f"  {interval_name}_rise: {result[f'{interval_name}_rise']}")
        
        # 解析跌幅
        fall_match = re.search(r'跌幅=([\d.-]+)', line)
        if fall_match:
            result[f'{interval_name}_fall'] = float(fall_match.group(1))
            print(f"  {interval_name}_fall: {result[f'{interval_name}_fall']}")
        
        # 解析时间间隔
        days_match = re.search(r'时间间隔=(\d+)天', line)
        if days_match:
            result[f'{interval_name}_days'] = int(days_match.group(1))
            print(f"  {interval_name}_days: {result[f'{interval_name}_days']}")
        
        print(f"  {interval_name}区间解析完成: {result}")
        return result
    except Exception as e:
        print(f"解析区间信息出错: {e}")
        return None

def extract_value(text, start_marker, end_marker):
    """从文本中提取指定标记之间的值"""
    start_pos = text.find(start_marker)
    if start_pos == -1:
        return None
    
    start_pos += len(start_marker)
    end_pos = text.find(end_marker, start_pos)
    if end_pos == -1:
        return None
    
    return text[start_pos:end_pos].strip()

def extract_point_info(section, point_marker):
    """提取某个点的所有信息"""
    point_start = section.find(point_marker)
    if point_start == -1:
        return None
    
    # 找到下一个点的开始位置或段落结束
    next_point = section.find("\n\n", point_start)
    if next_point == -1:
        next_point = len(section)
    
    point_text = section[point_start:next_point]
    
    # 提取各个字段
    date = extract_value(point_text, "日期=", " ")
    open_price = extract_value(point_text, "开盘=", " ")
    close_price = extract_value(point_text, "收盘=", " ")
    high_price = extract_value(point_text, "最高=", " ")
    low_price = extract_value(point_text, "最低=", " ")
    volume = extract_value(point_text, "成交量=", " ")
    entity_pct = extract_value(point_text, "实体涨跌幅=", " ")
    price_amplitude = extract_value(point_text, "价格振幅=", " ")
    
    if not all([date, open_price, close_price, high_price, low_price, volume, entity_pct, price_amplitude]):
        return None
    
    return {
        'date': date,
        'open': float(open_price),
        'close': float(close_price),
        'high': float(high_price),
        'low': float(low_price),
        'vol': float(volume),
        'entity_pct': float(entity_pct),
        'price_amplitude': float(price_amplitude)
    }

def determine_3d_success(returns):
    """判断3日选股是否成功 - 涨幅超过5%且跌幅小于3%"""
    max_3d = returns.get('max_3d', 0)
    min_3d = returns.get('min_3d', 0)

    if max_3d is None or min_3d is None:
        return "未知"  # 数据不足
    
    # 判断3日：涨幅超过5%且跌幅小于3%
    if max_3d >= 5.0 and min_3d > -3.0:
        return "成功"
    else:
        return "失败"

def determine_5d_success(returns):
    """判断5日选股是否成功 - 涨幅超过10%且跌幅小于5%"""
    max_5d = returns.get('max_5d', 0)
    min_5d = returns.get('min_5d', 0)

    if max_5d is None or min_5d is None:
        return "未知"  # 数据不足

    # 判断5日：涨幅超过10%且跌幅小于5%
    if max_5d >= 10.0 and min_5d > -5.0:
        return "成功"
    else:
        return "失败"

# 返回值百分制涨跌幅，有符号
def calculate_future_returns(result, data_dir):
    """计算未来收益率"""
    import os
    import pandas as pd
    from datetime import datetime, timedelta

    code = result['code']
    date = result['date']

    print(f"🔍 计算 {code} 在 {date} 的未来收益率")

    # 读取股票数据
    file_path = os.path.join(data_dir, f"{code}.csv")
    if not os.path.exists(file_path):
        print(f"❌ 股票数据文件不存在: {file_path}")
        return {
            'max_3d': 0, 'min_3d': 0,
            'max_5d': 0, 'min_5d': 0,
            'max_10d': 0, 'min_10d': 0
        }

    try:
        df = pd.read_csv(file_path)
        df['date'] = pd.to_datetime(df['date'])
        print(f"   📊 数据文件加载成功，共 {len(df)} 条记录")

        # 找到买入日期（E点后一天）
        buy_date = pd.to_datetime(date) + timedelta(days=1)
        buy_row = df[df['date'] >= buy_date].iloc[0] if not df[df['date'] >= buy_date].empty else None

        if buy_row is None:
            print(f"   ❌ 找不到买入日期 {buy_date} 的数据")
            return {
                'max_3d': 0, 'min_3d': 0,
                'max_5d': 0, 'min_5d': 0,
                'max_10d': 0, 'min_10d': 0
            }

        buy_price = buy_row['close']
        print(f"   💰 买入价格: {buy_price}")

        # 获取未来数据
        future_data = df[df['date'] > buy_date].head(10)

        if future_data.empty:
            print(f"   ❌ 没有未来数据")
            return {
                'max_3d': 0, 'min_3d': 0,
                'max_5d': 0, 'min_5d': 0,
                'max_10d': 0, 'min_10d': 0
            }

        print(f"   📈 未来数据: {len(future_data)} 条记录")

        # 计算3日、5日、10日最大涨跌幅
        def get_max_returns(data, days):
            if len(data) < days:
                print(f"     ⚠️  {days}日数据不足，只有 {len(data)} 条")
                if len(data) == 0:
                    return 0, 0
                # 使用现有数据计算
                period_data = data
            else:
                period_data = data.head(days)

            max_high = period_data['high'].max()
            min_low = period_data['low'].min()
            max_return = (max_high - buy_price) / buy_price * 100
            min_return = (min_low - buy_price) / buy_price * 100
            print(f"     📊 {days}日: 最高{max_high:.2f}(+{max_return:.2f}%), 最低{min_low:.2f}({min_return:.2f}%)")
            return max_return, min_return

        max_3d, min_3d = get_max_returns(future_data, 3)
        max_5d, min_5d = get_max_returns(future_data, 5)
        max_10d, min_10d = get_max_returns(future_data, 10)

        result_dict = {
            'max_3d': max_3d,
            'min_3d': min_3d,
            'max_5d': max_5d,
            'min_5d': min_5d,
            'max_10d': max_10d,
            'min_10d': min_10d
        }

        print(f"   ✅ 收益率计算完成: {result_dict}")
        return result_dict

    except Exception as e:
        print(f"❌ 计算收益率时出错: {e}")
        import traceback
        traceback.print_exc()
        return {
            'max_3d': 0, 'min_3d': 0,
            'max_5d': 0, 'min_5d': 0,
            'max_10d': 0, 'min_10d': 0
        }

@app.callback(
    [Output("analysis-table", "data"),
     Output("analysis-progress", "children", allow_duplicate=True),
     Output("analysis-stats", "children")],
    [Input("start-analysis-btn", "n_clicks")],
    [State("log-file-dropdown", "value"),
     State("analysis-data-dir", "value")],
    prevent_initial_call=True
)
def start_analysis_callback(n_clicks, log_file, data_dir):
    if not n_clicks or not log_file or not data_dir:
        return [], "", ""
    
    try:
        # 解析日志文件
        results = parse_log_file(log_file)
        
        if not results:
            return [], html.Div("⚠️ 未找到选股结果，请检查日志文件", 
                              style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"}), ""
        
        # 计算和收益率
        table_data = []
        for result in results:
            # 计算收益率
            returns = calculate_future_returns(result, data_dir)

            # 判断选股是否成功
            success_3d = determine_3d_success(returns)
            success_5d = determine_5d_success(returns)

            # 构建表格行
            row = {
                "策略": "JZVolumeShrinkSelector",
                "日期": result['date'],
                "股票": result['code'],
                "买入日期": (pd.to_datetime(result['date']) + timedelta(days=1)).strftime('%Y-%m-%d'),
                "3日成功选股": success_3d,
                "5日成功选股": success_5d,
                # 未来收益率 - 保持百分比数值形式
                "3日最大涨幅": returns.get('max_3d', 0) / 100 if returns.get('max_3d') is not None else 0,
                "3日最大跌幅": returns.get('min_3d', 0) / 100 if returns.get('min_3d') is not None else 0,
                "5日最大涨幅": returns.get('max_5d', 0) / 100 if returns.get('max_5d') is not None else 0,
                "5日最大跌幅": returns.get('min_5d', 0) / 100 if returns.get('min_5d') is not None else 0,
                "10日最大涨幅": returns.get('max_10d', 0) / 100 if returns.get('max_10d') is not None else 0,
                "10日最大跌幅": returns.get('min_10d', 0) / 100 if returns.get('min_10d') is not None else 0,
                # A点信息
                "A点日期": result['a_date'],
                "A点开盘": result['a_open'],
                "A点收盘": result['a_close'],
                "A点最高": result['a_high'],
                "A点最低": result['a_low'],
                "A点成交量": result['a_vol'],
                "A点实体涨跌幅": f"{result['a_entity_pct'] * 100:.2f}%",
                "A点价格振幅": f"{result['a_price_amplitude'] * 100:.2f}%",
                # B点信息
                "B点日期": result['b_date'],
                "B点开盘": result['b_open'],
                "B点收盘": result['b_close'],
                "B点最高": result['b_high'],
                "B点最低": result['b_low'],
                "B点成交量": result['b_vol'],
                "B点实体涨跌幅": f"{result['b_entity_pct'] * 100:.2f}%",
                "B点价格振幅": f"{result['b_price_amplitude'] * 100:.2f}%",
                # C点信息
                "C点日期": result['c_date'],
                "C点开盘": result['c_open'],
                "C点收盘": result['c_close'],
                "C点最高": result['c_high'],
                "C点最低": result['c_low'],
                "C点成交量": result['c_vol'],
                "C点实体涨跌幅": f"{result['c_entity_pct'] * 100:.2f}%",
                "C点价格振幅": f"{result['c_price_amplitude'] * 100:.2f}%",
                # D点信息
                "D点日期": result['d_date'],
                "D点开盘": result['d_open'],
                "D点收盘": result['d_close'],
                "D点最高": result['d_high'],
                "D点最低": result['d_low'],
                "D点成交量": result['d_vol'],
                "D点实体涨跌幅": f"{result['d_entity_pct'] * 100:.2f}%",
                "D点价格振幅": f"{result['d_price_amplitude'] * 100:.2f}%",
                # E点信息
                "E点日期": result['e_date'],
                "E点开盘": result['e_open'],
                "E点收盘": result['e_close'],
                "E点最高": result['e_high'],
                "E点最低": result['e_low'],
                "E点成交量": result['e_vol'],
                "E点实体涨跌幅": f"{result['e_entity_pct'] * 100:.2f}%",
                "E点价格振幅": f"{result['e_price_amplitude'] * 100:.2f}%",
                # 区间信息
                "A-B涨幅": f"{result.get('ab_rise', 0) * 100:.2f}%",
                "A-B天数": result.get('ab_days', 0),
                "B-C跌幅": f"{result.get('bc_fall', 0) * 100:.2f}%",
                "B-C天数": result.get('bc_days', 0),
                "C-D涨幅": f"{result.get('cd_rise', 0) * 100:.2f}%",
                "C-D天数": result.get('cd_days', 0),
                "D-E涨幅": f"{result.get('de_rise', 0) * 100:.2f}%",
                "D-E天数": result.get('de_days', 0),
                # 成交量比例信息
                "D点成交量/C-D均量": result.get('d_vol_ratio', 0),
                "D点上影线涨幅": result.get('d_shadow_ratio', 0),
                "D点上影线/实体": result.get('d_shadow_ratio', 0),
                "E点成交量/C-D均量": result.get('e_vol_ratio', 0),
                "E点成交量/D点成交量": round(result.get('e_vol_ratio', 0) / result.get('d_vol_ratio', 1), 2) if result.get('d_vol_ratio', 1) != 0 else 0,
                # KDJ相关信息
                "E点J值": result.get('e_j_value', 0),
                "E点J值相对D点J值涨幅": result.get('e_vs_d_j_pct', 0),
                "E点相对D点收盘价涨幅": result.get('e_vs_d_pct', 0),

            }
            table_data.append(row)
        
        # 计算统计信息
        if table_data:
            # 3日成功选股统计
            success_3d_statuses = [row['3日成功选股'] for row in table_data]
            success_3d_count = success_3d_statuses.count('成功')
            failure_3d_count = success_3d_statuses.count('失败')
            unknown_3d_count = success_3d_statuses.count('未知')

            # 5日成功选股统计
            success_5d_statuses = [row['5日成功选股'] for row in table_data]
            success_5d_count = success_5d_statuses.count('成功')
            failure_5d_count = success_5d_statuses.count('失败')
            unknown_5d_count = success_5d_statuses.count('未知')
            # 计算3日成功率
            total_3d_known = success_3d_count + failure_3d_count
            success_3d_rate = (success_3d_count / total_3d_known * 100) if total_3d_known > 0 else 0

            # 计算5日成功率
            total_5d_known = success_5d_count + failure_5d_count
            success_5d_rate = (success_5d_count / total_5d_known * 100) if total_5d_known > 0 else 0

            # 计算双重成功（3日和5日都成功）
            both_success_count = sum(1 for row in table_data
                                   if row['3日成功选股'] == '成功' and row['5日成功选股'] == '成功')
            both_success_rate = (both_success_count / len(table_data) * 100) if len(table_data) > 0 else 0

            stats = [
                html.P(f"总选股数量: {len(table_data)}"),
                html.Hr(),

                # 双重成功统计（最重要的指标）
                html.H5("🎯 双重成功选股统计", style={"color": "#dc3545", "marginTop": "15px", "fontWeight": "bold"}),
                html.P(f"3日和5日都成功: {both_success_count}个",
                       style={"color": "#dc3545", "fontWeight": "bold", "fontSize": "18px"}),
                html.P(f"双重成功率: {both_success_rate:.1f}%",
                       style={"color": "#dc3545", "fontWeight": "bold", "fontSize": "18px"}),
                html.P("(当天买入，既满足3日条件又满足5日条件的股票)",
                       style={"color": "#6c757d", "fontSize": "12px", "fontStyle": "italic"}),

                html.Hr(),
                html.H5("📊 3日成功选股统计", style={"color": "#007bff", "marginTop": "15px"}),
                html.P(f"成功选股: {success_3d_count}个", style={"color": "#28a745", "fontWeight": "bold"}),
                html.P(f"失败选股: {failure_3d_count}个", style={"color": "#dc3545", "fontWeight": "bold"}),
                html.P(f"数据不足: {unknown_3d_count}个", style={"color": "#6c757d"}),
                html.P(f"3日成功率: {success_3d_rate:.1f}%", style={"color": "#007bff", "fontWeight": "bold", "fontSize": "16px"}),

                html.Hr(),
                html.H5("📊 5日成功选股统计", style={"color": "#007bff", "marginTop": "15px"}),
                html.P(f"成功选股: {success_5d_count}个", style={"color": "#28a745", "fontWeight": "bold"}),
                html.P(f"失败选股: {failure_5d_count}个", style={"color": "#dc3545", "fontWeight": "bold"}),
                html.P(f"数据不足: {unknown_5d_count}个", style={"color": "#6c757d"}),
                html.P(f"5日成功率: {success_5d_rate:.1f}%", style={"color": "#007bff", "fontWeight": "bold", "fontSize": "16px"})
            ]
        else:
            stats = [html.P("无统计数据")]
        
        return table_data, html.Div(f"✅ 分析完成，共找到 {len(table_data)} 个选股结果", 
                                  style={"color": "#28a745", "fontWeight": "bold", "marginTop": "10px"}), stats
    
    except Exception as e:
        return [], html.Div(f"❌ 分析出错: {str(e)}", 
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"}), ""

@app.callback(
    [Output("export-analysis-btn", "n_clicks"),
     Output("analysis-progress", "children", allow_duplicate=True)],
    [Input("export-analysis-btn", "n_clicks")],
    [State("analysis-table", "data")],
    prevent_initial_call=True
)
def export_analysis_results(n_clicks, table_data):
    if not n_clicks:
        return 0, ""

    # 检查是否有数据可以导出
    if not table_data or len(table_data) == 0:
        return 0, html.Div("⚠️ 没有数据可以导出，请先进行选股分析",
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"})

    try:
        import pandas as pd
        from datetime import datetime

        print(f"🔄 开始导出数据，共 {len(table_data)} 条记录")
        print("📊 表格数据示例:")
        if table_data:
            print(f"   第一条记录的键: {list(table_data[0].keys())}")

        # 转换为DataFrame
        df = pd.DataFrame(table_data)

        # 检查涨跌幅列和成功选股列是否存在且有数据
        return_columns = ['3日最大涨幅', '3日最大跌幅', '5日最大涨幅', '5日最大跌幅', '10日最大涨幅', '10日最大跌幅']
        success_columns = ['3日成功选股', '5日成功选股']

        print("📊 检查涨跌幅数据:")
        for col in return_columns:
            if col in df.columns:
                non_null_count = df[col].notna().sum()
                non_zero_count = (df[col] != 0).sum()
                print(f"   {col}: {non_null_count}/{len(df)} 非空, {non_zero_count}/{len(df)} 非零")

                # 如果数据是字符串格式，尝试转换
                if df[col].dtype == 'object':
                    try:
                        # 移除百分号并转换为数值
                        df[col] = pd.to_numeric(df[col].astype(str).str.replace('%', ''), errors='coerce')
                        print(f"     已转换 {col} 从字符串到数值")
                    except:
                        print(f"     ⚠️  {col} 转换失败")
            else:
                print(f"   ❌ 缺失列: {col}")

        print("📊 检查成功选股数据:")
        for col in success_columns:
            if col in df.columns:
                value_counts = df[col].value_counts()
                print(f"   {col}: {value_counts.to_dict()}")
            else:
                print(f"   ❌ 缺失列: {col}")

        # 创建输出目录
        if not os.path.exists("选股分析结果"):
            os.makedirs("选股分析结果")

        # 生成文件名
        filename = f"选股分析结果/选股分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 导出到Excel，使用openpyxl引擎以支持格式设置
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='选股分析结果', index=False)

                # 获取工作表
                worksheet = writer.sheets['选股分析结果']

                # 设置涨跌幅列的格式为百分比
                from openpyxl.styles import NamedStyle
                try:
                    percent_style = NamedStyle(name="percent", number_format="0.00%")

                    # 找到涨跌幅列的位置并设置格式
                    for col_idx, col_name in enumerate(df.columns, 1):
                        if col_name in return_columns:
                            # 将列索引转换为Excel列字母
                            col_letter = chr(64 + col_idx) if col_idx <= 26 else chr(64 + col_idx // 26) + chr(64 + col_idx % 26)
                            for row in range(2, len(df) + 2):  # 从第2行开始（第1行是标题）
                                cell = worksheet[f"{col_letter}{row}"]
                                if cell.value is not None and isinstance(cell.value, (int, float)):
                                    cell.style = percent_style
                except Exception as format_error:
                    print(f"⚠️  格式设置失败: {format_error}")
        except ImportError:
            # 如果openpyxl不可用，使用默认引擎
            df.to_excel(filename, index=False)
            print("⚠️  使用默认Excel引擎，无法设置百分比格式")

        print(f"✅ 导出完成: {filename}")
        print(f"📋 导出的列: {list(df.columns)}")

        return 0, html.Div(f"✅ 导出成功！文件已保存为: {filename}",
                          style={"color": "#28a745", "fontWeight": "bold", "marginTop": "10px"})
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, html.Div(f"❌ 导出失败: {str(e)}",
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"})

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=8051)