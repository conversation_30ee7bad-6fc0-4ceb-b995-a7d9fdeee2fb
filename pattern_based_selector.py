#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于模式识别的选股系统
从历史成功案例中提取关键模式，然后在新数据中寻找匹配的模式
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

class PatternBasedSelector:
    """基于模式识别的选股器"""
    
    def __init__(self):
        self.feature_columns = [
            "A点实体涨跌幅", "A点价格振幅",
            "B点成交量", "B点实体涨跌幅", "B点价格振幅", 
            "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量",
            "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"
        ]
        self.scaler = StandardScaler()
        self.success_patterns = []
        self.pattern_weights = []
        
    def analyze_success_patterns(self, train_file):
        """分析成功案例的模式"""
        print("📊 分析历史成功案例模式...")
        
        # 加载数据
        df = pd.read_excel(train_file)
        print(f"   历史数据: {len(df)} 条记录")
        
        # 提取成功案例
        target_col = "5日成功选股"
        if target_col not in df.columns:
            print(f"❌ 未找到目标列: {target_col}")
            return False
            
        successful_mask = df[target_col] == "成功"
        successful_cases = df[successful_mask].copy()
        
        print(f"✅ 找到 {len(successful_cases)} 个成功案例")
        
        if len(successful_cases) < 5:
            print("❌ 成功案例太少，无法进行模式分析")
            return False
        
        # 按涨幅分层分析
        gain_col = "5日最大涨幅"
        if gain_col in successful_cases.columns:
            gains = pd.to_numeric(successful_cases[gain_col], errors='coerce')
            successful_cases = successful_cases.copy()
            successful_cases['gain_numeric'] = gains
            successful_cases = successful_cases.dropna(subset=['gain_numeric'])
            
            # 分为高涨幅和中等涨幅两组
            high_gain_threshold = successful_cases['gain_numeric'].quantile(0.7)  # 前30%
            
            high_gain_cases = successful_cases[successful_cases['gain_numeric'] >= high_gain_threshold]
            medium_gain_cases = successful_cases[successful_cases['gain_numeric'] < high_gain_threshold]
            
            print(f"🎯 高涨幅案例: {len(high_gain_cases)} 个 (涨幅≥{high_gain_threshold:.1%})")
            print(f"📊 中等涨幅案例: {len(medium_gain_cases)} 个")
            
            # 分别提取模式
            self._extract_patterns_from_group(high_gain_cases, "高涨幅", weight=2.0)
            self._extract_patterns_from_group(medium_gain_cases, "中等涨幅", weight=1.0)
        else:
            self._extract_patterns_from_group(successful_cases, "全部", weight=1.0)
        
        print(f"✅ 提取了 {len(self.success_patterns)} 个成功模式")
        return True
    
    def _extract_patterns_from_group(self, cases, group_name, weight=1.0):
        """从一组成功案例中提取模式"""
        if len(cases) == 0:
            return
            
        print(f"🔍 分析{group_name}模式...")
        
        # 预处理特征
        features = self._preprocess_features(cases)
        if features is None:
            return
        
        # 使用聚类找到代表性模式
        n_clusters = min(3, len(cases))  # 最多3个聚类
        if n_clusters < 1:
            return
            
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features)
        
        # 每个聚类的中心作为一个模式
        for i in range(n_clusters):
            cluster_mask = cluster_labels == i
            cluster_cases = cases[cluster_mask]
            cluster_features = features[cluster_mask]
            
            if len(cluster_cases) > 0:
                # 计算聚类中心
                pattern_center = np.mean(cluster_features, axis=0)
                
                # 计算平均涨幅作为模式权重的参考
                if 'gain_numeric' in cluster_cases.columns:
                    avg_gain = cluster_cases['gain_numeric'].mean()
                    pattern_weight = weight * (1 + avg_gain)  # 涨幅越高权重越大
                else:
                    pattern_weight = weight
                
                self.success_patterns.append(pattern_center)
                self.pattern_weights.append(pattern_weight)
                
                print(f"   模式 {len(self.success_patterns)}: {len(cluster_cases)} 个案例, 权重: {pattern_weight:.2f}")
    
    def _preprocess_features(self, df):
        """预处理特征数据"""
        try:
            # 检查特征列
            missing_features = [col for col in self.feature_columns if col not in df.columns]
            if missing_features:
                print(f"❌ 缺失特征列: {missing_features[:5]}...")
                return None
            
            # 提取特征
            features = df[self.feature_columns].copy()
            
            # 转换百分比格式
            percentage_columns = [col for col in self.feature_columns if any(keyword in col for keyword in ['涨跌幅', '振幅', '涨幅', '跌幅'])]
            for col in percentage_columns:
                if col in features.columns:
                    features[col] = pd.to_numeric(features[col].astype(str).str.replace('%', ''), errors='coerce') / 100
            
            # 处理缺失值
            features = features.fillna(features.median())
            
            # 标准化
            if not hasattr(self, '_scaler_fitted'):
                features_scaled = self.scaler.fit_transform(features)
                self._scaler_fitted = True
            else:
                features_scaled = self.scaler.transform(features)
            
            return features_scaled
            
        except Exception as e:
            print(f"❌ 特征预处理失败: {e}")
            return None
    
    def find_pattern_matches(self, test_file, top_k=3):
        """在测试数据中找到与成功模式匹配的股票"""
        print(f"\n🔍 在测试数据中寻找模式匹配...")
        
        # 加载测试数据
        test_df = pd.read_excel(test_file)
        print(f"   测试数据: {len(test_df)} 条记录")
        
        # 预处理测试数据
        test_features = self._preprocess_features(test_df)
        if test_features is None:
            return None
        
        # 计算每个测试样本与所有成功模式的匹配度
        pattern_scores = []
        
        for i, test_sample in enumerate(test_features):
            sample_scores = []
            
            for j, (pattern, weight) in enumerate(zip(self.success_patterns, self.pattern_weights)):
                # 计算余弦相似度
                similarity = cosine_similarity([test_sample], [pattern])[0][0]
                # 加权得分
                weighted_score = similarity * weight
                sample_scores.append(weighted_score)
            
            # 取最高的模式匹配得分
            max_score = max(sample_scores) if sample_scores else 0
            best_pattern = np.argmax(sample_scores) if sample_scores else -1
            
            pattern_scores.append({
                'index': i,
                'score': max_score,
                'best_pattern': best_pattern,
                'stock_info': test_df.iloc[i].get('股票', f'股票_{i+1}'),
                'buy_date': test_df.iloc[i].get('买入日期', 'N/A'),
                'actual_gain': test_df.iloc[i].get('5日最大涨幅', 'N/A')
            })
        
        # 按得分排序
        pattern_scores.sort(key=lambda x: x['score'], reverse=True)
        
        # 选择前top_k个
        top_matches = pattern_scores[:top_k]
        
        print(f"\n🎯 模式匹配结果 (前{top_k}个):")
        for i, match in enumerate(top_matches):
            print(f"   {i+1}. {match['stock_info']} (得分: {match['score']:.3f})")
            print(f"      匹配模式: {match['best_pattern']+1}, 实际涨幅: {match['actual_gain']}")
        
        return top_matches

def main():
    """主函数"""
    print("🎯 基于模式识别的选股系统")
    print("=" * 80)
    
    # 数据文件路径
    train_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    test_file = "选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    # 创建选股器
    selector = PatternBasedSelector()
    
    # 分析成功模式
    if not selector.analyze_success_patterns(train_file):
        print("❌ 模式分析失败")
        return
    
    # 寻找模式匹配
    matches = selector.find_pattern_matches(test_file, top_k=3)
    
    if matches:
        print(f"\n🎉 找到 {len(matches)} 个模式匹配的候选股票！")
        
        # 计算精确率
        if all('actual_gain' in m and m['actual_gain'] != 'N/A' for m in matches):
            successful_predictions = 0
            for match in matches:
                try:
                    gain = float(str(match['actual_gain']).replace('%', ''))
                    if gain >= 10:  # 涨幅≥10%算成功
                        successful_predictions += 1
                except:
                    pass
            
            precision = successful_predictions / len(matches) if matches else 0
            print(f"\n📊 模式匹配选股精确率: {precision:.1%} ({successful_predictions}/{len(matches)})")
            
            if precision > 0:
                print("🎉 找到了有效的成功模式！")
            else:
                print("⚠️ 当前模式在测试数据上效果不佳")
    else:
        print("\n⚠️ 没有找到模式匹配的候选股票")

if __name__ == "__main__":
    main()
