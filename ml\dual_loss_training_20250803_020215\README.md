# 双损失函数深度学习股票预测模型训练结果

## 训练信息
- **训练时间**: 2025年08月03日 02:05:26
- **基础成功率**: 13.11%
- **目标预测比例**: 8.0%
- **输出文件夹**: ml/dual_loss_training_20250803_020215

## 模型架构
1. **PrecisionMLP**: 多层感知机，专注精确率优化
2. **ResidualNet**: 残差网络，使用跳跃连接防止梯度消失
3. **AttentionNet**: 注意力网络，多头注意力机制

## 双损失函数
1. **精确率损失**: 鼓励预测为成功且实际为成功的比例
2. **比例损失**: 控制预测为成功的股票占总数的比例接近8.0%

## 文件结构
```
ml/dual_loss_training_20250803_020215/
├── models/                 # 模型文件
│   ├── best_*_model.pth   # 训练好的模型
│   ├── scaler.pkl         # 标准化器
│   └── label_encoder.pkl  # 标签编码器
├── reports/               # 报告文件
│   └── evaluation_results.json     # 评估结果JSON
├── config/                # 配置文件
│   └── ml_config.json     # 训练配置
└── README.md              # 说明文件
```

## 模型性能

### 最佳模型: PrecisionMLP
- **精确率**: 26.09%
- **预测数量**: 23个
- **预测比例**: 9.06% (目标: 8.0%)
- **比例偏差**: 0.011
- **综合评分**: 0.250

### 所有模型对比
| 模型 | 精确率 | 预测数量 | 预测比例 | 比例偏差 | 综合评分 |
|------|--------|----------|----------|----------|----------|
| PrecisionMLP | 26.09% | 23 | 9.06% | 0.011 | 0.250 |
| ResidualNet | 22.58% | 31 | 12.20% | 0.042 | 0.184 |
| AttentionNet | 19.05% | 21 | 8.27% | 0.003 | 0.188 |
| EnsembleModel | 20.69% | 29 | 11.42% | 0.034 | 0.173 |


## 使用方法

### 进行预测
```bash
python predict_dual_loss.py --input "新数据.xlsx" --output "预测结果.xlsx" --model_folder "ml/dual_loss_training_20250803_020215"
```

### 注意事项
- 模型同时优化精确率和预测比例
- 预测结果仅供参考，投资需谨慎
- 建议结合其他分析方法综合判断
