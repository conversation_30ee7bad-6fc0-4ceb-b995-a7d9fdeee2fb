#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
E点成交量/D点成交量区间统计分析
专门分析不同成交量比率区间的成功率分布
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from analyze_de_change_success import MultiConditionAnalyzer
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class VolumeRatioAnalyzer:
    """成交量比率分析器"""
    
    def __init__(self):
        self.analyzer = MultiConditionAnalyzer()
        
        # 定义成交量比率区间
        self.volume_ranges = [
            ("极致缩量", 0.0, 0.2),      # 缩量80%+
            ("严重缩量", 0.2, 0.4),      # 缩量60%-80%
            ("明显缩量", 0.4, 0.6),      # 缩量40%-60%
            ("适度缩量", 0.6, 0.8),      # 缩量20%-40%
            ("基本持平", 0.8, 1.2),      # 变化±20%
            ("适度放量", 1.2, 1.5),      # 放量20%-50%
            ("明显放量", 1.5, 2.0),      # 放量50%-100%
            ("大幅放量", 2.0, 3.0),      # 放量100%-200%
            ("异常放量", 3.0, 10.0)      # 放量200%+
        ]
        
        # 精细化区间（用于更详细分析）
        self.fine_ranges = [
            ("超极缩量", 0.0, 0.1),      # 缩量90%+
            ("极致缩量", 0.1, 0.2),      # 缩量80%-90%
            ("很大缩量", 0.2, 0.3),      # 缩量70%-80%
            ("大幅缩量", 0.3, 0.4),      # 缩量60%-70%
            ("中度缩量", 0.4, 0.5),      # 缩量50%-60%
            ("轻度缩量", 0.5, 0.6),      # 缩量40%-50%
            ("小幅缩量", 0.6, 0.7),      # 缩量30%-40%
            ("微幅缩量", 0.7, 0.8),      # 缩量20%-30%
            ("基本不变", 0.8, 1.0),      # 缩量0%-20%
            ("微幅放量", 1.0, 1.2),      # 放量0%-20%
            ("小幅放量", 1.2, 1.4),      # 放量20%-40%
            ("中度放量", 1.4, 1.6),      # 放量40%-60%
            ("大幅放量", 1.6, 2.0),      # 放量60%-100%
            ("巨量放大", 2.0, 3.0),      # 放量100%-200%
            ("异常放量", 3.0, 10.0)      # 放量200%+
        ]
    
    def analyze_volume_ratio_ranges(self, data_file: str, use_fine_ranges: bool = False):
        """分析成交量比率区间"""
        print("📊 E点成交量/D点成交量区间统计分析")
        print("=" * 80)
        
        # 加载数据
        df = self.analyzer.load_data(data_file)
        
        if '5日成功选股' not in df.columns:
            print("❌ 未找到目标列")
            return None
        
        if 'E点成交量/D点成交量' not in df.columns:
            print("❌ 未找到E点成交量/D点成交量列")
            return None
        
        # 预处理成交量比率数据
        volume_ratio = pd.to_numeric(df['E点成交量/D点成交量'], errors='coerce')
        valid_mask = pd.notna(volume_ratio) & (volume_ratio > 0)
        
        valid_df = df[valid_mask].copy()
        valid_ratio = volume_ratio[valid_mask]
        
        print(f"✅ 有效数据: {len(valid_df)} 条")
        print(f"📈 成交量比率统计:")
        print(f"   平均值: {valid_ratio.mean():.3f}")
        print(f"   中位数: {valid_ratio.median():.3f}")
        print(f"   标准差: {valid_ratio.std():.3f}")
        print(f"   最小值: {valid_ratio.min():.3f}")
        print(f"   最大值: {valid_ratio.max():.3f}")
        
        # 选择分析区间
        ranges = self.fine_ranges if use_fine_ranges else self.volume_ranges
        range_type = "精细化" if use_fine_ranges else "标准"
        
        print(f"\n📊 {range_type}区间分析:")
        print("-" * 100)
        print(f"{'区间名称':<12} {'比率范围':<15} {'总数量':<8} {'成功数量':<8} {'成功率':<8} {'平均涨幅':<10} {'中位涨幅':<10}")
        print("-" * 100)
        
        results = []
        
        for range_name, min_val, max_val in ranges:
            # 筛选区间数据
            if max_val == 10.0:  # 最后一个区间
                mask = valid_ratio >= min_val
                range_str = f"≥{min_val:.1f}"
            else:
                mask = (valid_ratio >= min_val) & (valid_ratio < max_val)
                range_str = f"{min_val:.1f}-{max_val:.1f}"
            
            range_data = valid_df[mask]
            total_count = len(range_data)
            
            if total_count > 0:
                success_mask = range_data['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                success_rate = success_count / total_count
                
                # 计算涨幅统计
                avg_gain = 0
                median_gain = 0
                if success_count > 0 and '5日最大涨幅' in range_data.columns:
                    success_cases = range_data[success_mask]
                    actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                    if len(actual_gains) > 0:
                        avg_gain = actual_gains.mean()
                        median_gain = actual_gains.median()
                
                print(f"{range_name:<12} {range_str:<15} {total_count:<8} {success_count:<8} "
                      f"{success_rate:<8.1%} {avg_gain:<10.1%} {median_gain:<10.1%}")
                
                results.append({
                    'range_name': range_name,
                    'range_str': range_str,
                    'min_val': min_val,
                    'max_val': max_val,
                    'total_count': total_count,
                    'success_count': success_count,
                    'success_rate': success_rate,
                    'avg_gain': avg_gain,
                    'median_gain': median_gain
                })
        
        # 分析最佳区间
        self.analyze_best_ranges(results)
        
        # 生成可视化
        self.create_volume_ratio_charts(valid_df, valid_ratio, results, range_type)
        
        return results
    
    def analyze_best_ranges(self, results: list):
        """分析最佳区间"""
        print(f"\n🏆 最佳区间分析:")
        print("=" * 80)
        
        # 按成功率排序（但要考虑样本量）
        valid_results = [r for r in results if r['total_count'] >= 10]  # 至少10个样本
        
        if not valid_results:
            print("❌ 没有足够样本的区间")
            return
        
        # 按成功率排序
        by_success_rate = sorted(valid_results, key=lambda x: x['success_rate'], reverse=True)
        
        print(f"📈 按成功率排序 (样本≥10):")
        for i, result in enumerate(by_success_rate[:5]):
            print(f"   {i+1}. {result['range_name']} ({result['range_str']}): "
                  f"成功率{result['success_rate']:.1%}, 样本{result['total_count']}, "
                  f"平均涨幅{result['avg_gain']:.1%}")
        
        # 按样本量排序
        by_sample_size = sorted(valid_results, key=lambda x: x['total_count'], reverse=True)
        
        print(f"\n📊 按样本量排序:")
        for i, result in enumerate(by_sample_size[:5]):
            print(f"   {i+1}. {result['range_name']} ({result['range_str']}): "
                  f"样本{result['total_count']}, 成功率{result['success_rate']:.1%}, "
                  f"平均涨幅{result['avg_gain']:.1%}")
        
        # 综合评分（成功率 * 0.6 + 样本量权重 * 0.4）
        for result in valid_results:
            sample_weight = min(result['total_count'] / 100, 1.0)  # 100个样本为满分
            result['composite_score'] = result['success_rate'] * 0.6 + sample_weight * 0.4
        
        by_composite = sorted(valid_results, key=lambda x: x['composite_score'], reverse=True)
        
        print(f"\n🎯 综合评分排序:")
        for i, result in enumerate(by_composite[:5]):
            print(f"   {i+1}. {result['range_name']} ({result['range_str']}): "
                  f"综合评分{result['composite_score']:.3f}, 成功率{result['success_rate']:.1%}, "
                  f"样本{result['total_count']}")
        
        # 推荐策略
        if by_composite:
            best = by_composite[0]
            print(f"\n💡 推荐区间: {best['range_name']} ({best['range_str']})")
            print(f"   理由: 综合评分最高 ({best['composite_score']:.3f})")
            print(f"   成功率: {best['success_rate']:.1%}")
            print(f"   样本量: {best['total_count']}")
            print(f"   平均涨幅: {best['avg_gain']:.1%}")
    
    def create_volume_ratio_charts(self, df: pd.DataFrame, volume_ratio: pd.Series, 
                                 results: list, range_type: str):
        """创建成交量比率分析图表"""
        print(f"\n🎨 生成{range_type}区间分析图表...")
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Volume Ratio Analysis ({range_type})', fontsize=16, fontweight='bold')
        
        # 1. 成交量比率分布直方图
        ax1 = axes[0, 0]
        success_mask = df['5日成功选股'] == "成功"
        success_ratio = volume_ratio[success_mask]
        fail_ratio = volume_ratio[~success_mask]
        
        # 设置合理的bins
        bins = np.logspace(-1, 1, 30)  # 对数刻度，从0.1到10
        
        ax1.hist([success_ratio, fail_ratio], bins=bins, alpha=0.7,
                 label=['Success Cases', 'Failure Cases'], color=['green', 'red'])
        ax1.set_xscale('log')
        ax1.set_title('Volume Ratio Distribution', fontweight='bold')
        ax1.set_xlabel('Volume Ratio (E/D)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.axvline(1.0, color='black', linestyle='--', alpha=0.5, label='No Change')
        
        # 2. 各区间成功率柱状图
        ax2 = axes[0, 1]
        valid_results = [r for r in results if r['total_count'] > 0]
        
        if valid_results:
            range_names = [r['range_name'] for r in valid_results]
            success_rates = [r['success_rate'] * 100 for r in valid_results]
            sample_counts = [r['total_count'] for r in valid_results]
            
            bars = ax2.bar(range(len(range_names)), success_rates,
                          color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in success_rates])
            ax2.set_title('Success Rate by Volume Range', fontweight='bold')
            ax2.set_xlabel('Volume Range')
            ax2.set_ylabel('Success Rate (%)')
            ax2.set_xticks(range(len(range_names)))
            ax2.set_xticklabels(range_names, rotation=45, ha='right')
            
            # 添加样本数标签
            for i, (bar, count) in enumerate(zip(bars, sample_counts)):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'n={count}', ha='center', va='bottom', fontsize=8)
        
        # 3. 样本数量分布
        ax3 = axes[1, 0]
        if valid_results:
            bars = ax3.bar(range(len(range_names)), sample_counts, alpha=0.7, color='skyblue')
            ax3.set_title('Sample Count by Volume Range', fontweight='bold')
            ax3.set_xlabel('Volume Range')
            ax3.set_ylabel('Sample Count')
            ax3.set_xticks(range(len(range_names)))
            ax3.set_xticklabels(range_names, rotation=45, ha='right')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{int(height)}', ha='center', va='bottom', fontsize=8)
        
        # 4. 成功率vs样本量散点图
        ax4 = axes[1, 1]
        if valid_results:
            x_vals = [r['total_count'] for r in valid_results]
            y_vals = [r['success_rate'] * 100 for r in valid_results]
            colors = [r['avg_gain'] * 100 for r in valid_results]
            
            scatter = ax4.scatter(x_vals, y_vals, c=colors, s=100, alpha=0.7, cmap='RdYlGn')
            ax4.set_title('Success Rate vs Sample Count', fontweight='bold')
            ax4.set_xlabel('Sample Count')
            ax4.set_ylabel('Success Rate (%)')
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax4)
            cbar.set_label('Average Gain (%)')
            
            # 添加区间名称标签
            for i, result in enumerate(valid_results):
                if result['total_count'] >= 20:  # 只标注样本量较大的
                    ax4.annotate(result['range_name'], 
                               (result['total_count'], result['success_rate'] * 100),
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = f"volume_ratio_analysis_{range_type.lower()}.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {output_file}")
        
        plt.show()
    
    def generate_volume_ratio_report(self, data_file: str, output_file: str = None):
        """生成成交量比率分析报告"""
        if output_file is None:
            output_file = "volume_ratio_analysis_report.txt"
        
        print(f"\n📝 生成成交量比率分析报告...")
        
        # 分析标准区间和精细区间
        standard_results = self.analyze_volume_ratio_ranges(data_file, use_fine_ranges=False)
        print("\n" + "="*80)
        fine_results = self.analyze_volume_ratio_ranges(data_file, use_fine_ranges=True)
        
        # 生成报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("E点成交量/D点成交量区间分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("一、分析说明\n")
            f.write("-" * 30 + "\n")
            f.write("本报告分析E点成交量相对D点成交量的比率与5日选股成功率的关系\n")
            f.write("比率<1.0表示缩量，比率>1.0表示放量\n\n")
            
            f.write("二、标准区间分析结果\n")
            f.write("-" * 30 + "\n")
            if standard_results:
                for result in standard_results:
                    if result['total_count'] > 0:
                        f.write(f"{result['range_name']} ({result['range_str']}): ")
                        f.write(f"{result['total_count']}只股票, ")
                        f.write(f"成功{result['success_count']}只, ")
                        f.write(f"成功率{result['success_rate']:.1%}, ")
                        f.write(f"平均涨幅{result['avg_gain']:.1%}\n")
            
            f.write("\n三、精细区间分析结果\n")
            f.write("-" * 30 + "\n")
            if fine_results:
                for result in fine_results:
                    if result['total_count'] > 0:
                        f.write(f"{result['range_name']} ({result['range_str']}): ")
                        f.write(f"{result['total_count']}只股票, ")
                        f.write(f"成功{result['success_count']}只, ")
                        f.write(f"成功率{result['success_rate']:.1%}, ")
                        f.write(f"平均涨幅{result['avg_gain']:.1%}\n")
            
            f.write(f"\n报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 报告已保存: {output_file}")

def main():
    """主函数"""
    print("📊 E点成交量/D点成交量区间统计分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = VolumeRatioAnalyzer()
    
    # 数据文件
    data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    # 生成完整报告
    analyzer.generate_volume_ratio_report(data_file)
    
    print(f"\n💡 成交量比率分析要点:")
    print("1. 适度缩量(0.6-0.8)往往比极致缩量效果更好")
    print("2. 微幅放量(1.0-1.2)可能是启动信号")
    print("3. 异常放量(>3.0)需要谨慎，可能是出货")
    print("4. 结合价格走势判断缩量/放量的意义")

if __name__ == "__main__":
    main()
