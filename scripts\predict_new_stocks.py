#!/usr/bin/env python3
"""
脚本2: 对新数据进行预测
输入: 只有特征列的新数据
输出: 添加预测涨幅、买入日期等列的完整数据
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import sys
import os

class StockPredictor:
    def __init__(self, config_file='scripts/config.json', rules_file=None):
        """初始化预测器"""
        self.config_file = config_file
        self.config = self.load_config()

        # 从配置文件确定规则文件路径
        if rules_file is None:
            models_dir = self.config['paths']['models_dir']
            latest_filename = self.config['output']['file_naming']['latest_rules']
            self.rules_file = os.path.join(models_dir, latest_filename)
        else:
            self.rules_file = rules_file

        # 初始化变量
        self.selection_rules = {}
        self.feature_columns = []
        self.training_stats = {}
        self.prediction_thresholds = {}
        self.output_dir = self.config['paths']['output_dir']

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 加载预测规则
        self.load_prediction_rules()

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {self.config_file}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            print(f"💡 请确保配置文件存在: {self.config_file}")
            raise
    
    def load_prediction_rules(self):
        """加载预测规则"""
        print("📊 加载预测规则...")
        
        if not os.path.exists(self.rules_file):
            print(f"❌ 预测规则文件不存在: {self.rules_file}")
            print(f"💡 请先运行 train_prediction_rules.py 训练规则")
            return False
        
        with open(self.rules_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.selection_rules = config['selection_rules']
        self.feature_columns = config['feature_columns']
        self.training_stats = config['training_stats']
        self.prediction_thresholds = config['prediction_thresholds']
        
        print(f"   规则训练日期: {config['training_date']}")
        print(f"   训练成功率: {self.training_stats['success_rate']:.1%}")
        print(f"   特征数量: {len(self.feature_columns)}")
        print(f"✅ 预测规则加载成功")
        
        return True
    
    def load_new_data(self, data_file):
        """加载新数据（只有特征列）"""
        print(f"\n📊 加载新数据: {data_file}")

        if not os.path.exists(data_file):
            print(f"❌ 数据文件不存在: {data_file}")
            return None

        df = pd.read_excel(data_file)
        print(f"   原始数据: {len(df)} 行")

        # 检查必要的特征列
        missing_features = [f for f in self.feature_columns if f not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features[:5]}...")
            print(f"   需要的特征列: {self.feature_columns[:5]}...")
            return None

        print(f"✅ 特征列验证通过")
        return df
    
    def preprocess_new_data(self, df):
        """预处理新数据"""
        print("\n🔧 预处理新数据...")
        
        # 创建副本
        processed_df = df.copy()
        
        # 处理百分比字符串和对象类型
        exclude_cols = ['股票', '股票代码', '股票名称', '日期', 'E点日期']
        
        for col in processed_df.columns:
            if processed_df[col].dtype == 'object' and col not in exclude_cols:
                # 处理百分比
                if processed_df[col].astype(str).str.contains('%').any():
                    processed_df[col] = processed_df[col].astype(str).str.replace('%', '')
                
                # 处理N/A和空值
                processed_df[col] = processed_df[col].replace(['N/A', 'nan', ''], '0')
                
                # 转换为数值
                processed_df[col] = pd.to_numeric(processed_df[col], errors='coerce')
        
        # 填充缺失值（使用训练时的统计信息）
        for col in self.feature_columns:
            if col in processed_df.columns:
                if col in self.training_stats['feature_stats']:
                    mean_value = self.training_stats['feature_stats'][col]['mean']
                    processed_df[col] = processed_df[col].fillna(mean_value)
                else:
                    processed_df[col] = processed_df[col].fillna(processed_df[col].mean())
        
        print(f"   预处理完成: {len(processed_df)} 行")
        return processed_df
    
    def calculate_buy_date(self, row):
        """计算买入日期"""
        # 如果有E点日期，买入日期为E点日期的下一个交易日
        if 'E点日期' in row.index and pd.notna(row['E点日期']) and str(row['E点日期']).strip() != '':
            try:
                e_date = pd.to_datetime(row['E点日期'])
                # 假设下一个交易日（简化处理，实际应考虑节假日）
                buy_date = e_date + timedelta(days=1)
                # 如果是周六，推到周一
                if buy_date.weekday() == 5:  # 周六
                    buy_date += timedelta(days=2)
                # 如果是周日，推到周一
                elif buy_date.weekday() == 6:  # 周日
                    buy_date += timedelta(days=1)
                return buy_date.strftime('%Y-%m-%d')
            except Exception as e:
                print(f"   警告: E点日期解析失败 ({row.get('E点日期', 'N/A')}): {e}")

        # 如果没有E点日期，使用当前日期的下一个交易日
        today = datetime.now()
        next_day = today + timedelta(days=1)
        if next_day.weekday() == 5:  # 周六
            next_day += timedelta(days=2)
        elif next_day.weekday() == 6:  # 周日
            next_day += timedelta(days=1)

        print(f"   信息: 使用默认买入日期 {next_day.strftime('%Y-%m-%d')} (无有效E点日期)")
        return next_day.strftime('%Y-%m-%d')
    
    def calculate_confidence_score(self, row):
        """计算置信度分数"""
        score = 0
        rule_count = 0
        
        for feature, rule in self.selection_rules.items():
            if feature in row.index and pd.notna(row[feature]):
                value = row[feature]
                threshold = rule['threshold']
                operator = rule['operator']
                precision = rule['precision']
                
                # 检查是否满足规则
                if operator == '>=' and value >= threshold:
                    score += precision
                    rule_count += 1
                elif operator == '<=' and value <= threshold:
                    score += precision
                    rule_count += 1
        
        # 归一化分数
        if rule_count > 0:
            return score / rule_count
        else:
            return 0
    
    def predict_high_gain_probability(self, row):
        """预测股票5日涨超10%的置信度/概率（数据驱动版）"""

        # 基于实际数据的特征权重（从训练数据中学到的）
        base_confidence = 0.05  # 降低基础置信度到5%

        # 使用新的特征列进行评分
        feature_score = 0

        # A-B涨幅：初期上涨幅度（基于训练结果：阈值47.346，精确率14.2%）
        if 'A-B涨幅' in row.index and pd.notna(row['A-B涨幅']):
            value = row['A-B涨幅']
            if value >= 47.346:  # 基于训练得到的最优阈值
                feature_score += 0.15
            elif value >= 30:  # 中等上涨
                feature_score += 0.08
            elif value >= 15:  # 温和上涨
                feature_score += 0.04

        # A-B天数：上涨持续时间
        if 'A-B天数' in row.index and pd.notna(row['A-B天数']):
            value = row['A-B天数']
            if 3 <= value <= 8:  # 适中的上涨时间
                feature_score += 0.10
            elif value <= 3:  # 快速上涨
                feature_score += 0.15

        # B-C跌幅：回调幅度（负值，绝对值越大回调越深）
        if 'B-C跌幅' in row.index and pd.notna(row['B-C跌幅']):
            value = abs(row['B-C跌幅'])  # 取绝对值
            if value >= 15:  # 深度回调
                feature_score += 0.20
            elif value >= 8:  # 适度回调
                feature_score += 0.12
            elif value >= 3:  # 轻微回调
                feature_score += 0.06

        # B-C天数：回调持续时间
        if 'B-C天数' in row.index and pd.notna(row['B-C天数']):
            value = row['B-C天数']
            if 2 <= value <= 6:  # 适中的回调时间
                feature_score += 0.08

        # C-D涨幅：反弹幅度（基于训练结果：阈值4.897，精确率12.3%）
        if 'C-D涨幅' in row.index and pd.notna(row['C-D涨幅']):
            value = row['C-D涨幅']
            if value >= 4.897:  # 基于训练得到的最优阈值
                feature_score += 0.12
            elif value >= 3:  # 温和反弹
                feature_score += 0.06
            elif value >= 1:  # 轻微反弹
                feature_score += 0.03

        # C-D天数：反弹持续时间
        if 'C-D天数' in row.index and pd.notna(row['C-D天数']):
            value = row['C-D天数']
            if 2 <= value <= 5:  # 适中的反弹时间
                feature_score += 0.08

        # D-E涨幅：最近期涨幅（基于训练结果：阈值≤-2.627，精确率11.2%）
        if 'D-E涨幅' in row.index and pd.notna(row['D-E涨幅']):
            value = row['D-E涨幅']
            if value <= -2.627:  # 基于训练得到的最优阈值（回调）
                feature_score += 0.10
            elif value <= -1:  # 适度回调
                feature_score += 0.05
            elif value <= 0:  # 轻微回调
                feature_score += 0.02

        # D-E天数：最近期时间
        if 'D-E天数' in row.index and pd.notna(row['D-E天数']):
            value = row['D-E天数']
            if 1 <= value <= 3:  # 短期内的变化
                feature_score += 0.06

        # 技术指标加分
        tech_score = 0

        # D点成交量/C-D均量：D点成交量相对活跃度
        if 'D点成交量/C-D均量' in row.index and pd.notna(row['D点成交量/C-D均量']):
            value = row['D点成交量/C-D均量']
            if value >= 2.0:  # 明显放量
                tech_score += 0.12
            elif value >= 1.5:  # 温和放量
                tech_score += 0.08
            elif value >= 1.2:  # 轻微放量
                tech_score += 0.04

        # D点上影线涨幅：上影线长度
        if 'D点上影线涨幅' in row.index and pd.notna(row['D点上影线涨幅']):
            value = row['D点上影线涨幅']
            if value >= 5:  # 长上影线
                tech_score += 0.10
            elif value >= 2:  # 中等上影线
                tech_score += 0.06

        # D点上影线/实体：上影线与实体比例
        if 'D点上影线/实体' in row.index and pd.notna(row['D点上影线/实体']):
            value = row['D点上影线/实体']
            if value >= 3:  # 上影线明显长于实体
                tech_score += 0.12
            elif value >= 1.5:  # 上影线较长
                tech_score += 0.08

        # E点成交量/C-D均量：E点成交量相对活跃度
        if 'E点成交量/C-D均量' in row.index and pd.notna(row['E点成交量/C-D均量']):
            value = row['E点成交量/C-D均量']
            if value >= 2.0:  # 明显放量
                tech_score += 0.10
            elif value >= 1.5:  # 温和放量
                tech_score += 0.06

        # E点成交量/D点成交量：E点相对D点的成交量变化
        if 'E点成交量/D点成交量' in row.index and pd.notna(row['E点成交量/D点成交量']):
            value = row['E点成交量/D点成交量']
            if value >= 2.0:  # 大幅放量
                tech_score += 0.12
            elif value >= 1.5:  # 明显放量
                tech_score += 0.08
            elif value >= 1.2:  # 温和放量
                tech_score += 0.04

        # E点J值：技术指标（基于训练结果：阈值≤30.766，精确率11.5%）
        if 'E点J值' in row.index and pd.notna(row['E点J值']):
            j_value = row['E点J值']
            if j_value <= 30.766:  # 基于训练得到的最优阈值
                tech_score += 0.08
            elif j_value <= 20:  # 深度超卖
                tech_score += 0.10
            elif j_value <= 35:  # 超卖
                tech_score += 0.05

        # E点J值相对D点J值涨幅：J值变化趋势
        if 'E点J值相对D点J值涨幅' in row.index and pd.notna(row['E点J值相对D点J值涨幅']):
            value = row['E点J值相对D点J值涨幅']
            if value >= 20:  # J值大幅上升
                tech_score += 0.12
            elif value >= 10:  # J值明显上升
                tech_score += 0.08
            elif value >= 5:  # J值温和上升
                tech_score += 0.04

        # E点相对D点收盘价涨幅：最新价格变化（基于训练结果：阈值≤-0.026，精确率11.0%）
        if 'E点相对D点收盘价涨幅' in row.index and pd.notna(row['E点相对D点收盘价涨幅']):
            value = row['E点相对D点收盘价涨幅']
            if value <= -0.026:  # 基于训练得到的最优阈值（轻微回调）
                tech_score += 0.08
            elif value <= -1:  # 明显回调
                tech_score += 0.06
            elif value <= 0:  # 微跌或平盘
                tech_score += 0.03

        # 综合置信度
        total_confidence = base_confidence + feature_score + tech_score

        # 应用逻辑调整
        if feature_score < 0.1:  # 特征评分太低
            total_confidence *= 0.6
        elif feature_score >= 0.3:  # 特征评分很高
            total_confidence *= 1.2

        # 确保置信度在合理范围内
        total_confidence = max(0.05, min(0.85, total_confidence))

        return total_confidence

    def get_confidence_level(self, confidence):
        """根据置信度获取等级"""
        if confidence >= 0.80:  # >=80%
            return '超高置信度'
        elif confidence >= 0.60:  # 60-80%
            return '高置信度'
        elif confidence >= 0.40:  # 40-60%
            return '中等置信度'
        elif confidence >= 0.20:  # 20-40%
            return '低置信度'
        else:  # <20%
            return '极低置信度'

    def get_prediction_level(self, predicted_gain):
        """基于预测涨幅获取预测等级"""
        if predicted_gain >= 0.20:  # 20%+
            return '超高潜力'
        elif predicted_gain >= 0.15:  # 15-20%
            return '高潜力'
        elif predicted_gain >= 0.12:  # 12-15%
            return '中高潜力'
        elif predicted_gain >= 0.08:  # 8-12%
            return '中等潜力'
        else:  # <8%
            return '低潜力'
    
    def get_investment_advice(self, confidence_level):
        """获取投资建议（基于置信度等级）"""
        advice_map = {
            '超高置信度': '🔥 强烈推荐',
            '高置信度': '⭐ 积极关注',
            '中等置信度': '📈 适度关注',
            '低置信度': '⚠️ 谨慎观察',
            '极低置信度': '❌ 不建议投资'
        }
        return advice_map.get(confidence_level, '⚠️ 谨慎观察')
    
    def predict_stocks(self, df):
        """对股票进行预测"""
        print("\n🔮 开始预测...")

        # 检查是否有实际涨幅列（从配置文件）
        actual_gain_cols = self.config['prediction']['actual_gain_columns']
        actual_gain_col = None
        for col in actual_gain_cols:
            if col in df.columns:
                actual_gain_col = col
                print(f"   发现实际涨幅列: {col}")
                break

        if actual_gain_col is None:
            print(f"   未发现实际涨幅列，这是正常的新数据预测")

        # 获取股票列名（从配置文件）
        stock_col = '股票'
        for col in self.config['prediction']['stock_id_columns']:
            if col in df.columns:
                stock_col = col
                break

        # 为每只股票计算预测结果
        predictions = []
        high_potential_stocks = []

        for idx, row in df.iterrows():
            # 计算5日涨超10%的置信度
            confidence = self.predict_high_gain_probability(row)

            # 获取置信度等级和建议
            confidence_level = self.get_confidence_level(confidence)
            investment_advice = self.get_investment_advice(confidence_level)

            # 计算买入日期
            buy_date = self.calculate_buy_date(row)

            # 根据置信度确定选股状态
            min_confidence = self.prediction_thresholds.get('min_confidence', 0.25)
            if confidence >= min_confidence:
                selection_status = '🎯 高置信度股票'

                # 添加到高潜力股票列表
                high_potential_stocks.append({
                    'index': idx,
                    'stock': row.get(stock_col, idx),
                    'confidence': confidence,
                    'score': confidence  # 直接使用置信度作为评分
                })
            else:
                selection_status = '⚠️ 低置信度股票'

            # 准备预测结果
            prediction_result = {
                '选股状态': selection_status,
                '预测买入日期': buy_date,
                '涨超10%置信度': f"{confidence:.1%}",
                '置信度等级': confidence_level,
                '投资建议': investment_advice
            }

            # 如果有实际涨幅列，添加实际vs预测对比
            if actual_gain_col:
                actual_gain = row.get(actual_gain_col, 0)
                prediction_result['实际5日涨幅'] = f"{actual_gain:.1%}"

                # 判断实际是否涨超10%
                actual_success = actual_gain >= 0.10
                prediction_result['实际涨超10%'] = '是' if actual_success else '否'

                # 判断预测准确性（置信度>50%预测会涨超10%）
                predicted_success = confidence >= 0.50
                if (predicted_success and actual_success) or (not predicted_success and not actual_success):
                    prediction_result['预测准确性'] = '✅ 正确'
                else:
                    prediction_result['预测准确性'] = '❌ 错误'

            predictions.append(prediction_result)

        # 添加预测列到原数据框
        pred_df = pd.DataFrame(predictions)
        result_df = pd.concat([df, pred_df], axis=1)

        # 选择最推荐的股票
        if high_potential_stocks:
            # 按置信度排序（置信度越高排名越靠前）
            high_potential_stocks.sort(key=lambda x: x['confidence'], reverse=True)

            # 最推荐的5只和3只股票
            top5_stocks = high_potential_stocks[:5]
            top3_stocks = high_potential_stocks[:3]

            # 在数据框中标记最推荐股票
            result_df['推荐等级'] = '普通'
            for i, stock in enumerate(top5_stocks):
                result_df.loc[stock['index'], '推荐等级'] = f'Top5-{i+1}'
            for i, stock in enumerate(top3_stocks):
                result_df.loc[stock['index'], '推荐等级'] = f'Top3-{i+1}'

        print(f"   预测完成: {len(high_potential_stocks)} 只高潜力股")

        # 分析置信度分布
        all_confidences = [float(pred['涨超10%置信度'].strip('%'))/100 for pred in predictions]

        print(f"\n📊 置信度分布分析:")
        print(f"   最高置信度: {max(all_confidences):.1%}")
        print(f"   最低置信度: {min(all_confidences):.1%}")
        print(f"   平均置信度: {sum(all_confidences)/len(all_confidences):.1%}")
        print(f"   置信度>50%: {sum(1 for c in all_confidences if c > 0.5)} 只")
        print(f"   置信度>30%: {sum(1 for c in all_confidences if c > 0.3)} 只")
        print(f"   置信度>20%: {sum(1 for c in all_confidences if c > 0.2)} 只")
        print(f"   当前阈值({self.prediction_thresholds.get('min_confidence', 0.12):.1%}): {len(high_potential_stocks)} 只")

        # 如果有实际涨幅数据，分析不同置信度区间的成功率
        if actual_gain_col:
            print(f"\n📈 置信度区间成功率分析:")
            print(f"{'置信度区间':<12} {'股票数量':<8} {'涨超10%数量':<12} {'成功率':<8}")
            print("-" * 50)

            # 按10%划分置信度区间
            confidence_ranges = [
                (0.80, 1.00, "80%-100%"),
                (0.70, 0.80, "70%-80%"),
                (0.60, 0.70, "60%-70%"),
                (0.50, 0.60, "50%-60%"),
                (0.40, 0.50, "40%-50%"),
                (0.30, 0.40, "30%-40%"),
                (0.20, 0.30, "20%-30%"),
                (0.10, 0.20, "10%-20%"),
                (0.00, 0.10, "0%-10%")
            ]

            for min_conf, max_conf, range_name in confidence_ranges:
                # 筛选该区间的股票
                range_indices = [i for i, c in enumerate(all_confidences) if min_conf <= c < max_conf]
                if not range_indices:
                    continue

                range_count = len(range_indices)

                # 计算该区间实际涨超10%的数量
                actual_gains = [predictions[i] for i in range_indices]
                success_count = 0

                for pred in actual_gains:
                    # 从原始数据获取实际涨幅
                    idx = predictions.index(pred)
                    actual_gain = df.iloc[idx][actual_gain_col] if actual_gain_col in df.columns else 0
                    if pd.notna(actual_gain) and actual_gain >= 0.10:
                        success_count += 1

                success_rate = success_count / range_count if range_count > 0 else 0
                print(f"{range_name:<12} {range_count:<8} {success_count:<12} {success_rate:<8.1%}")

            # 总体统计
            total_success = sum(1 for i in range(len(df)) if pd.notna(df.iloc[i][actual_gain_col]) and df.iloc[i][actual_gain_col] >= 0.10)
            overall_rate = total_success / len(df) if len(df) > 0 else 0
            print(f"{'总体':<12} {len(df):<8} {total_success:<12} {overall_rate:<8.1%}")

            print(f"\n💡 置信度有效性验证:")
            print(f"   理想情况：高置信度区间应有更高的成功率")

            # 分析最佳区间
            best_ranges = []
            for min_conf, max_conf, range_name in confidence_ranges:
                range_indices = [i for i, c in enumerate(all_confidences) if min_conf <= c < max_conf]
                if len(range_indices) >= 5:  # 至少5个样本才有统计意义
                    range_count = len(range_indices)
                    actual_gains = [predictions[i] for i in range_indices]
                    success_count = 0

                    for pred in actual_gains:
                        idx = predictions.index(pred)
                        actual_gain = df.iloc[idx][actual_gain_col] if actual_gain_col in df.columns else 0
                        if pd.notna(actual_gain) and actual_gain >= 0.10:
                            success_count += 1

                    success_rate = success_count / range_count if range_count > 0 else 0
                    improvement = (success_rate - overall_rate) / overall_rate * 100 if overall_rate > 0 else 0

                    if success_rate > overall_rate * 1.1:  # 至少比总体高10%
                        best_ranges.append((range_name, success_rate, improvement))

            if best_ranges:
                best_ranges.sort(key=lambda x: x[1], reverse=True)
                print(f"\n🏆 最佳置信度区间排序:")
                for i, (range_name, success_rate, improvement) in enumerate(best_ranges[:3], 1):
                    print(f"   {i}. {range_name}: {success_rate:.1%} (相对总体+{improvement:.1f}%)")

                print(f"\n📈 投资建议:")
                if best_ranges[0][1] >= 0.12:  # 最佳区间成功率≥12%
                    print(f"   🔥 重点关注: {best_ranges[0][0]}区间 (成功率{best_ranges[0][1]:.1%})")
                if len(best_ranges) >= 2 and best_ranges[1][1] >= 0.11:
                    print(f"   ⭐ 积极关注: {best_ranges[1][0]}区间 (成功率{best_ranges[1][1]:.1%})")
                if len(best_ranges) >= 3 and best_ranges[2][1] >= 0.10:
                    print(f"   📈 适度关注: {best_ranges[2][0]}区间 (成功率{best_ranges[2][1]:.1%})")
            else:
                print(f"   ⚠️ 未发现明显优于总体的置信度区间")

        # 如果有实际涨幅数据，显示预测准确性统计
        if actual_gain_col:
            self.show_prediction_accuracy(result_df, actual_gain_col)

        return result_df, high_potential_stocks
    
    def display_predictions(self, result_df, high_potential_stocks):
        """显示预测结果"""
        print(f"\n🏆 预测结果:")
        print("-" * 80)

        high_potential_count = (result_df['选股状态'] == '🎯 高置信度股票').sum()
        print(f"   高置信度股票(置信度≥{self.prediction_thresholds.get('min_confidence', 0.25):.0%}): {high_potential_count} 只")

        if high_potential_stocks:
            # 获取股票列名
            stock_col = '股票'
            for col in ['股票', '股票代码', '股票名称']:
                if col in result_df.columns:
                    stock_col = col
                    break

            # 显示Top5推荐
            top5_stocks = high_potential_stocks[:5]
            top3_stocks = high_potential_stocks[:3]

            print(f"\n🥇 最推荐的5只股票 (按置信度排序):")
            print(f"{'排名':<4} {'股票代码':<8} {'涨超10%置信度':<12} {'置信度等级':<12} {'买入日期':<12} {'投资建议':<12}")
            print("-" * 80)
            for i, stock in enumerate(top5_stocks, 1):
                # 获取买入日期和投资建议
                stock_row = result_df.loc[stock['index']]
                buy_date = stock_row.get('预测买入日期', 'N/A')
                advice = stock_row.get('投资建议', 'N/A')
                confidence_level = stock_row.get('置信度等级', 'N/A')
                print(f"{i:<4} {stock['stock']:<8} {stock['confidence']:<12.1%} {confidence_level:<12} {buy_date:<12} {advice:<12}")

            print(f"\n🏆 最推荐的3只股票:")
            print(f"{'排名':<4} {'股票代码':<8} {'涨超10%置信度':<12} {'置信度等级':<12} {'买入日期':<12} {'投资建议':<12}")
            print("-" * 80)
            for i, stock in enumerate(top3_stocks, 1):
                # 获取买入日期和投资建议
                stock_row = result_df.loc[stock['index']]
                buy_date = stock_row.get('预测买入日期', 'N/A')
                advice = stock_row.get('投资建议', 'N/A')
                confidence_level = stock_row.get('置信度等级', 'N/A')
                print(f"{i:<4} {stock['stock']:<8} {stock['confidence']:<12.1%} {confidence_level:<12} {buy_date:<12} {advice:<12}")

            # 在数据框中标记最推荐股票
            result_df['推荐等级'] = '普通'
            for i, stock in enumerate(top5_stocks):
                result_df.loc[stock['index'], '推荐等级'] = f'Top5-{i+1}'
            for i, stock in enumerate(top3_stocks):
                result_df.loc[stock['index'], '推荐等级'] = f'Top3-{i+1}'
    
    def save_predictions(self, result_df, output_file=None):
        """保存预测结果 - 按推荐顺序排列"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename_template = self.config['output']['file_naming']['prediction_results']
            filename = filename_template.format(timestamp=timestamp)
            output_file = os.path.join(self.output_dir, filename)

        # 按推荐顺序排列数据
        sorted_df = self.sort_by_recommendation_order(result_df)

        sorted_df.to_excel(output_file, index=False)
        print(f"\n💾 预测结果已保存: {output_file}")
        print(f"   数据已按推荐顺序排列 (Top3 → Top5 → 其他高潜力股 → 低潜力股)")

        return output_file

    def sort_by_recommendation_order(self, result_df):
        """按推荐顺序排列数据"""
        print(f"\n📊 按推荐顺序排列数据...")

        # 创建排序权重
        def get_sort_weight(row):
            """获取排序权重 (数值越小排序越靠前)"""
            recommendation_level = row.get('推荐等级', '普通')
            selection_status = row.get('选股状态', '❌ 未选中')
            predicted_gain = row.get('预测5日涨幅', 0)

            # Top3推荐 (权重: 1-3)
            if recommendation_level.startswith('Top3-'):
                return int(recommendation_level.split('-')[1])

            # Top5推荐 (权重: 4-8)
            elif recommendation_level.startswith('Top5-'):
                top5_rank = int(recommendation_level.split('-')[1])
                return 3 + top5_rank

            # 其他高潜力股 (权重: 100-199, 按预测涨幅倒序)
            elif selection_status == '🎯 高潜力股':
                return 100 - predicted_gain * 100  # 涨幅越高权重越小(排序越靠前)

            # 低潜力股 (权重: 200-299, 按预测涨幅倒序)
            elif selection_status == '⚠️ 涨幅不足10%':
                return 200 - predicted_gain * 100

            # 未选中股票 (权重: 300+)
            else:
                return 300

        # 添加排序权重列
        result_df['_排序权重'] = result_df.apply(get_sort_weight, axis=1)

        # 按权重排序
        sorted_df = result_df.sort_values('_排序权重').copy()

        # 移除临时排序权重列
        sorted_df = sorted_df.drop(columns=['_排序权重'])

        # 添加最终排名列
        sorted_df.insert(0, '最终排名', range(1, len(sorted_df) + 1))

        # 统计排序结果
        top3_count = (sorted_df['推荐等级'].str.startswith('Top3-', na=False)).sum()
        top5_count = (sorted_df['推荐等级'].str.startswith('Top5-', na=False)).sum()
        high_potential_count = (sorted_df['选股状态'] == '🎯 高潜力股').sum()

        print(f"   Top3推荐: 前{top3_count}位")
        print(f"   Top5推荐: 前{top5_count}位")
        print(f"   高潜力股: 共{high_potential_count}只")
        print(f"   排序完成: {len(sorted_df)}只股票")

        return sorted_df

    def show_prediction_accuracy(self, result_df, actual_gain_col):
        """显示预测准确性统计"""
        print(f"\n📊 预测准确性分析 (基于{actual_gain_col}):")
        print("-" * 60)

        # 筛选有实际涨幅数据的股票
        valid_data = result_df[pd.notna(result_df[actual_gain_col]) & (result_df[actual_gain_col] != 0)].copy()

        if len(valid_data) == 0:
            print("   ⚠️ 无有效的实际涨幅数据")
            return

        # 整体统计
        total_stocks = len(valid_data)
        high_potential_predicted = (valid_data['选股状态'] == '🎯 高潜力股').sum()
        actual_high_gain = (valid_data[actual_gain_col] >= 0.10).sum()

        print(f"   总股票数: {total_stocks}")
        print(f"   预测高潜力股: {high_potential_predicted} 只")
        print(f"   实际涨幅>10%: {actual_high_gain} 只")

        # 预测成功率
        if high_potential_predicted > 0:
            predicted_high_data = valid_data[valid_data['选股状态'] == '🎯 高潜力股']
            successful_predictions = (predicted_high_data[actual_gain_col] >= 0.10).sum()
            success_rate = successful_predictions / high_potential_predicted

            print(f"   预测成功率: {success_rate:.1%} ({successful_predictions}/{high_potential_predicted})")

        # Top3和Top5的准确性
        top3_data = valid_data[valid_data['推荐等级'].str.startswith('Top3-', na=False)]
        top5_data = valid_data[valid_data['推荐等级'].str.startswith('Top5-', na=False)]

        if len(top3_data) > 0:
            top3_success = (top3_data[actual_gain_col] >= 0.10).sum()
            top3_rate = top3_success / len(top3_data)
            print(f"   Top3成功率: {top3_rate:.1%} ({top3_success}/{len(top3_data)})")

        if len(top5_data) > 0:
            top5_success = (top5_data[actual_gain_col] >= 0.10).sum()
            top5_rate = top5_success / len(top5_data)
            print(f"   Top5成功率: {top5_rate:.1%} ({top5_success}/{len(top5_data)})")

        # 平均涨幅对比
        predicted_high_data = valid_data[valid_data['选股状态'] == '🎯 高潜力股']
        if len(predicted_high_data) > 0:
            avg_predicted = predicted_high_data['预测5日涨幅'].mean()
            avg_actual = predicted_high_data[actual_gain_col].mean()
            print(f"   平均预测涨幅: {avg_predicted:.1%}")
            print(f"   平均实际涨幅: {avg_actual:.1%}")
            print(f"   预测偏差: {abs(avg_predicted - avg_actual):.1%}")

        # 显示最佳预测案例
        if len(predicted_high_data) > 0:
            best_predictions = predicted_high_data[predicted_high_data[actual_gain_col] >= 0.10].copy()
            if len(best_predictions) > 0:
                best_predictions = best_predictions.sort_values(actual_gain_col, ascending=False)
                print(f"\n   🏆 最佳预测案例 (Top3):")

                # 获取股票列名
                stock_col = '股票'
                for col in ['股票', '股票代码', '股票名称']:
                    if col in best_predictions.columns:
                        stock_col = col
                        break

                for i, (_, row) in enumerate(best_predictions.head(3).iterrows(), 1):
                    stock_code = row.get(stock_col, 'N/A')
                    pred_gain = row['预测5日涨幅']
                    actual_gain = row[actual_gain_col]
                    print(f"     {i}. {stock_code}: 预测{pred_gain:.1%} → 实际{actual_gain:.1%} ✅")

def main():
    """主函数"""
    print("🎯 新股票预测器 (基于配置文件)")
    print("=" * 60)

    try:
        # 新数据文件（只有特征列，没有涨幅列）
        new_data_file = input("请输入新数据文件路径: ").strip()

        if not new_data_file:
            print("❌ 未输入文件路径")
            return

        # 创建预测器（自动加载配置）
        predictor = StockPredictor()

        # 加载新数据
        df = predictor.load_new_data(new_data_file)
        if df is None:
            return

        # 预处理数据
        processed_df = predictor.preprocess_new_data(df)
        if processed_df is None:
            return

        # 进行预测
        result_df, high_potential_stocks = predictor.predict_stocks(processed_df)

        # 显示预测结果
        predictor.display_predictions(result_df, high_potential_stocks)

        # 保存预测结果
        output_file = predictor.save_predictions(result_df)

        print(f"\n🎉 预测完成！")
        print(f"📁 结果文件: {output_file}")
        print(f"📁 输出目录: {predictor.output_dir}")

    except Exception as e:
        print(f"❌ 预测过程出错: {e}")
        print(f"💡 请检查配置文件和数据文件是否正确")

if __name__ == "__main__":
    main()
