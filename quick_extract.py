#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速股票数据提取工具
简化版本，用于快速提取指定日期范围的股票数据
"""

import pandas as pd
import os
from datetime import datetime

def quick_extract():
    """快速提取模式"""
    
    # 默认文件路径
    input_file = r'C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250803_140012.xlsx'
    
    print("🎯 快速股票数据提取")
    print("=" * 40)
    print(f"输入文件: {os.path.basename(input_file)}")
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    try:
        # 读取数据
        df = pd.read_excel(input_file)
        print(f"📊 总记录数: {len(df)}")
        
        # 查找日期列
        date_columns = [col for col in df.columns if '买入日期' in col or '日期' in col]
        if not date_columns:
            print("❌ 未找到日期列")
            print(f"可用列: {list(df.columns)}")
            return
        
        date_column = date_columns[0]
        print(f"📅 日期列: {date_column}")
        
        # 转换日期
        df[date_column] = pd.to_datetime(df[date_column])
        
        # 显示可用日期范围
        min_date = df[date_column].min().strftime('%Y-%m-%d')
        max_date = df[date_column].max().strftime('%Y-%m-%d')
        print(f"📈 日期范围: {min_date} 到 {max_date}")
        
        # 显示每日数据量
        daily_counts = df[date_column].dt.date.value_counts().sort_index()
        print(f"\n📋 每日数据量:")
        for date, count in daily_counts.items():
            print(f"   {date}: {count} 条")
        
        # 获取用户输入
        print(f"\n请输入要提取的日期范围:")
        start_date = input("开始日期 (YYYY-MM-DD): ").strip()
        end_date = input("结束日期 (YYYY-MM-DD): ").strip()
        
        if not start_date or not end_date:
            print("❌ 请输入有效的日期范围")
            return
        
        # 筛选数据
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        mask = (df[date_column] >= start_dt) & (df[date_column] <= end_dt)
        filtered_df = df[mask].copy()
        
        print(f"\n🔍 筛选结果: {len(filtered_df)} 条记录")
        
        if len(filtered_df) == 0:
            print("⚠️  指定日期范围内没有数据")
            return
        
        # 生成输出文件名
        output_file = f"筛选结果_{start_date}_到_{end_date}.xlsx"
        output_path = os.path.join(os.path.dirname(input_file), output_file)
        
        # 保存数据
        filtered_df.to_excel(output_path, index=False)
        print(f"💾 已保存到: {output_file}")
        
        # 显示预览
        print(f"\n📋 数据预览:")
        preview_cols = []
        for col in [date_column, '股票代码', '股票名称', '预测概率']:
            if col in filtered_df.columns:
                preview_cols.append(col)
        
        if preview_cols:
            print(filtered_df[preview_cols].head(10).to_string(index=False))
        
        # 统计信息
        if '预测概率' in filtered_df.columns:
            prob_stats = filtered_df['预测概率'].describe()
            print(f"\n📊 预测概率统计:")
            print(f"   平均值: {prob_stats['mean']:.1f}%")
            print(f"   最大值: {prob_stats['max']:.1f}%")
            print(f"   最小值: {prob_stats['min']:.1f}%")
        
    except Exception as e:
        print(f"❌ 处理出错: {e}")

if __name__ == "__main__":
    quick_extract()
