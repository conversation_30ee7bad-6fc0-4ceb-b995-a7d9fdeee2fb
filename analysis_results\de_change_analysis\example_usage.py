#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多条件筛选分析使用示例
演示如何使用自定义条件进行股票筛选分析
"""

from analyze_de_change_success import analyze_multi_conditions, MultiConditionAnalyzer

def example_1_single_condition():
    """示例1: 单一条件筛选"""
    print("=" * 80)
    print("示例1: 单一条件筛选 - D-E大幅下跌")
    print("=" * 80)
    
    # 定义条件: D-E点涨跌幅在-15%到-5%之间
    conditions = [
        {"feature": "D-E涨跌幅", "operator": "between", "value": [-15, -5]}
    ]
    
    analyze_multi_conditions(
        data_file="../../选股分析结果/2025-05-01-2025-06-01.xlsx",
        custom_conditions=conditions,
        condition_name="D-E大幅下跌筛选"
    )

def example_2_multiple_conditions():
    """示例2: 多条件组合筛选"""
    print("\n" + "=" * 80)
    print("示例2: 多条件组合筛选 - 技术调整+J值稳定")
    print("=" * 80)
    
    # 定义多个条件
    conditions = [
        {"feature": "D-E涨跌幅", "operator": "between", "value": [-10, -3]},  # D-E中度下跌
        {"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": [-5, 0]},  # J值相对稳定
        {"feature": "E点成交量/D点成交量", "operator": "between", "value": [0.8, 2.0]}  # 成交量适中
    ]
    
    analyze_multi_conditions(
        data_file="../../选股分析结果/2025-05-01-2025-06-01.xlsx",
        custom_conditions=conditions,
        condition_name="技术调整+J值稳定组合"
    )

def example_3_volume_analysis():
    """示例3: 成交量分析"""
    print("\n" + "=" * 80)
    print("示例3: 成交量放大筛选")
    print("=" * 80)
    
    # 定义条件: 成交量显著放大
    conditions = [
        {"feature": "E点成交量/D点成交量", "operator": ">", "value": 1.5},  # 成交量放大50%以上
        {"feature": "D-E涨跌幅", "operator": ">", "value": -5}  # 跌幅不超过5%
    ]
    
    analyze_multi_conditions(
        data_file="../../选股分析结果/2025-05-01-2025-06-01.xlsx",
        custom_conditions=conditions,
        condition_name="成交量放大筛选"
    )

def example_4_conservative_strategy():
    """示例4: 保守策略"""
    print("\n" + "=" * 80)
    print("示例4: 保守策略 - 小幅调整+稳定指标")
    print("=" * 80)
    
    # 定义保守条件
    conditions = [
        {"feature": "D-E涨跌幅", "operator": "between", "value": [-3, 1]},  # 小幅调整
        {"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": [-2, 2]},  # J值稳定
        {"feature": "E点实体涨跌幅", "operator": ">", "value": -2}  # E点实体跌幅不大
    ]
    
    analyze_multi_conditions(
        data_file="../../选股分析结果/2025-05-01-2025-06-01.xlsx",
        custom_conditions=conditions,
        condition_name="保守策略筛选"
    )

def example_5_aggressive_strategy():
    """示例5: 激进策略"""
    print("\n" + "=" * 80)
    print("示例5: 激进策略 - 大幅调整抄底")
    print("=" * 80)
    
    # 定义激进条件
    conditions = [
        {"feature": "D-E涨跌幅", "operator": "<", "value": -10},  # 大幅下跌
        {"feature": "E点J值", "operator": "<", "value": 20},  # J值较低，可能超跌
        {"feature": "E点成交量/D点成交量", "operator": ">", "value": 1.2}  # 成交量放大
    ]
    
    analyze_multi_conditions(
        data_file="../../选股分析结果/2025-05-01-2025-06-01.xlsx",
        custom_conditions=conditions,
        condition_name="激进抄底策略"
    )

def example_6_custom_analyzer():
    """示例6: 直接使用分析器类"""
    print("\n" + "=" * 80)
    print("示例6: 直接使用MultiConditionAnalyzer类")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = MultiConditionAnalyzer()
    
    # 加载数据
    df = analyzer.load_data("../../选股分析结果/2025-05-01-2025-06-01.xlsx")
    
    # 定义多个不同的条件组合进行对比
    condition_sets = [
        {
            "name": "超跌反弹",
            "conditions": [{"feature": "D-E涨跌幅", "operator": "<", "value": -8}]
        },
        {
            "name": "温和调整", 
            "conditions": [{"feature": "D-E涨跌幅", "operator": "between", "value": [-5, -1]}]
        },
        {
            "name": "小幅上涨",
            "conditions": [{"feature": "D-E涨跌幅", "operator": "between", "value": [0, 3]}]
        }
    ]
    
    print("📊 条件对比分析:")
    print("-" * 60)
    print(f"{'策略名称':<15} {'符合数量':<10} {'成功数量':<10} {'成功率':<10}")
    print("-" * 60)
    
    for condition_set in condition_sets:
        mask = analyzer.apply_conditions(df, condition_set["conditions"])
        filtered_df = df[mask]
        
        if len(filtered_df) > 0:
            success_mask = filtered_df['5日成功选股'] == "成功"
            success_count = success_mask.sum()
            total_count = len(filtered_df)
            success_rate = success_count / total_count
            
            print(f"{condition_set['name']:<15} {total_count:<10} {success_count:<10} {success_rate:<10.1%}")

def run_all_examples():
    """运行所有示例"""
    print("🎯 多条件筛选分析使用示例")
    print("本脚本演示了如何使用自定义条件进行股票筛选分析")
    
    # 运行所有示例
    example_1_single_condition()
    example_2_multiple_conditions()
    example_3_volume_analysis()
    example_4_conservative_strategy()
    example_5_aggressive_strategy()
    example_6_custom_analyzer()
    
    print("\n" + "=" * 80)
    print("✅ 所有示例运行完成！")
    print("💡 您可以根据这些示例创建自己的筛选条件")
    print("📊 建议根据实际需求调整条件参数以获得最佳效果")

if __name__ == "__main__":
    run_all_examples()
