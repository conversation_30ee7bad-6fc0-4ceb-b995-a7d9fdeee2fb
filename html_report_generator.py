#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML报告生成器
读取格式化的JSON文件并生成可视化HTML报告
"""

import json
import os
from datetime import datetime

class HTMLReportGenerator:
    """HTML报告生成器"""
    
    def __init__(self, json_file: str):
        self.json_file = json_file
        self.data = None
        
    def load_data(self):
        """加载JSON数据"""
        print(f"📊 加载数据文件: {self.json_file}")
        
        if not os.path.exists(self.json_file):
            raise FileNotFoundError(f"文件不存在: {self.json_file}")
        
        with open(self.json_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        print(f"✅ 成功加载数据")
        return self.data
    
    def generate_html_report(self, output_file: str = None):
        """生成HTML报告"""
        # 如果没有指定输出文件名，使用时间戳生成
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"stock_analysis_report_{timestamp}.html"

        print(f"🎨 生成HTML报告: {output_file}")

        if not self.data:
            raise ValueError("数据未加载，请先调用load_data()")
        
        # 获取数据
        analysis_info = self.data.get('analysis_info', {})
        summary = self.data.get('summary', {})
        composite_records = self.data.get('composite_records', [])
        monthly_stats = self.data.get('monthly_statistics', {})
        
        # 生成HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选股分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }}
        
        .header h1 {{
            color: #007bff;
            margin: 0;
            font-size: 2.5em;
        }}
        
        .header .subtitle {{
            color: #666;
            margin-top: 10px;
            font-size: 1.1em;
        }}
        
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .summary-card .value {{
            font-size: 2em;
            font-weight: bold;
            margin: 0;
        }}
        
        .success-rate {{
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
        }}
        
        .table-container {{
            margin-top: 30px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        
        th {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }}
        
        tr:hover {{
            background-color: #f8f9fa;
        }}
        
        .success {{
            color: #28a745;
            font-weight: bold;
        }}
        
        .failure {{
            color: #dc3545;
            font-weight: bold;
        }}
        
        .stock-code {{
            font-weight: bold;
            color: #007bff;
        }}
        
        .gain-positive {{
            color: #28a745;
            font-weight: bold;
        }}
        
        .gain-negative {{
            color: #dc3545;
            font-weight: bold;
        }}
        
        .details-btn {{
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }}
        
        .details-btn:hover {{
            background: #0056b3;
        }}
        
        .details-row {{
            display: none;
            background: #f8f9fa;
        }}
        
        .details-content {{
            padding: 15px;
            border-left: 4px solid #007bff;
        }}
        
        .individual-record {{
            background: white;
            margin: 5px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }}
        
        .individual-record.failure {{
            border-left-color: #dc3545;
        }}
        
        .footer {{
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }}
        
        .analysis-info {{
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }}
        
        .analysis-info h3 {{
            margin-top: 0;
            color: #495057;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 选股分析报告</h1>
            <div class="subtitle">复合记录成功率分析</div>
        </div>
        
        <div class="analysis-info">
            <h3>📋 分析信息</h3>
            <p><strong>源文件:</strong> {analysis_info.get('source_file', 'N/A')}</p>
            <p><strong>分析时间:</strong> {analysis_info.get('analysis_date', 'N/A')}</p>
            <p><strong>原始记录数:</strong> {analysis_info.get('total_original_records', 0):,} 条</p>
            <p><strong>复合记录数:</strong> {analysis_info.get('total_composite_records', 0):,} 条</p>
            <p><strong>成功阈值:</strong> {analysis_info.get('success_threshold', 10)}%</p>
            <p><strong>时间合并窗口:</strong> {analysis_info.get('max_days_gap', 15)} 天</p>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总股票数</h3>
                <div class="value">{summary.get('total_stocks', 0)}</div>
            </div>
            <div class="summary-card">
                <h3>复合记录数</h3>
                <div class="value">{summary.get('total_composite_records', 0)}</div>
            </div>
            <div class="summary-card">
                <h3>成功记录数</h3>
                <div class="value">{summary.get('successful_records', 0)}</div>
            </div>
            <div class="summary-card success-rate">
                <h3>成功率</h3>
                <div class="value">{summary.get('success_rate', 0):.1f}%</div>
            </div>
        </div>

        <div class="table-container">
            <h2>📅 月度统计分析</h2>
            <table>
                <thead>
                    <tr>
                        <th>月份</th>
                        <th>总股票数</th>
                        <th>复合记录数</th>
                        <th>成功记录数</th>
                        <th>成功率</th>
                        <th>原始记录数</th>
                    </tr>
                </thead>
                <tbody>
"""

        # 添加月度统计数据
        if monthly_stats:
            sorted_months = sorted(monthly_stats.keys())
            for month in sorted_months:
                stats = monthly_stats[month]
                success_class = "success" if stats['success_rate'] >= 25 else "failure" if stats['success_rate'] < 15 else ""

                html_content += f"""
                    <tr>
                        <td><strong>{month}</strong></td>
                        <td>{stats['total_stocks']}</td>
                        <td>{stats['composite_records']}</td>
                        <td>{stats['success_records']}</td>
                        <td class="{success_class}"><strong>{stats['success_rate']:.1f}%</strong></td>
                        <td>{stats['total_individual_records']}</td>
                    </tr>
"""

        html_content += """
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <h2>📈 详细复合记录</h2>
            <table id="recordsTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>股票代码</th>
                        <th>开始日期</th>
                        <th>结束日期</th>
                        <th>天数跨度</th>
                        <th>记录数量</th>
                        <th>最大涨幅</th>
                        <th>是否成功</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
"""
        
        # 添加表格行
        for record in composite_records:
            success_class = "success" if record['is_success'] else "failure"
            success_text = "✅ 成功" if record['is_success'] else "❌ 失败"
            gain_class = "gain-positive" if record['max_gain'] >= 0 else "gain-negative"
            
            html_content += f"""
                    <tr>
                        <td>{record['id']}</td>
                        <td class="stock-code">{record['stock_code']}</td>
                        <td>{record['start_date']}</td>
                        <td>{record['end_date']}</td>
                        <td>{record['days_span']} 天</td>
                        <td>{record['record_count']}</td>
                        <td class="{gain_class}">{record['max_gain']:.2f}%</td>
                        <td class="{success_class}">{success_text}</td>
                        <td><button class="details-btn" onclick="toggleDetails({record['id']})">收起详情</button></td>
                    </tr>
                    <tr class="details-row" id="details-{record['id']}" style="display: table-row;">
                        <td colspan="9">
                            <div class="details-content">
                                <h4>📋 个别记录详情</h4>
"""
            
            # 添加个别记录
            for i, individual in enumerate(record['individual_records']):
                gain_class = "gain-positive" if individual['gain_5d'] >= 10 else "gain-negative"
                record_class = "success" if individual['gain_5d'] >= 10 else "failure"
                
                html_content += f"""
                                <div class="individual-record {record_class}">
                                    <strong>记录 {i+1}:</strong> 
                                    买入日期: {individual['buy_date']}, 
                                    5日涨幅: <span class="{gain_class}">{individual['gain_5d']:.2f}%</span>, 
                                    成功标记: {individual['success_flag']}
                                </div>
"""
            
            html_content += """
                            </div>
                        </td>
                    </tr>
"""
        
        # 完成HTML
        html_content += f"""
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>© 2025 选股分析系统</p>
        </div>
    </div>
    
    <script>
        function toggleDetails(recordId) {{
            const detailsRow = document.getElementById('details-' + recordId);
            const button = event.target;

            if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {{
                detailsRow.style.display = 'table-row';
                button.textContent = '收起详情';
            }} else {{
                detailsRow.style.display = 'none';
                button.textContent = '查看详情';
            }}
        }}
        
        // 添加表格排序功能
        function sortTable(columnIndex) {{
            const table = document.getElementById('recordsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr:not(.details-row)'));
            
            rows.sort((a, b) => {{
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();
                
                // 尝试数值比较
                const aNum = parseFloat(aValue.replace(/[^\\d.-]/g, ''));
                const bNum = parseFloat(bValue.replace(/[^\\d.-]/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {{
                    return bNum - aNum; // 降序
                }}
                
                return aValue.localeCompare(bValue);
            }});
            
            // 重新插入排序后的行
            rows.forEach(row => tbody.appendChild(row));
        }}
        
        // 为表头添加点击事件
        document.querySelectorAll('th').forEach((th, index) => {{
            if (index < 8) {{ // 排除详情列
                th.style.cursor = 'pointer';
                th.addEventListener('click', () => sortTable(index));
                th.title = '点击排序';
            }}
        }});
    </script>
</body>
</html>
"""
        
        # 保存HTML文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML报告已生成: {output_file}")
        return output_file

def main():
    """主函数"""
    json_file = "composite_stock_analysis.json"
    
    if not os.path.exists(json_file):
        print(f"❌ JSON文件不存在: {json_file}")
        print("请先运行 stock_result_filter.py 生成数据文件")
        return
    
    # 创建HTML报告生成器
    generator = HTMLReportGenerator(json_file)
    
    try:
        # 加载数据
        generator.load_data()
        
        # 生成HTML报告
        html_file = generator.generate_html_report()
        
        print(f"\n🎉 HTML报告生成完成!")
        print(f"📄 文件位置: {html_file}")
        print(f"🌐 请在浏览器中打开查看报告")
        
        # 尝试自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(f"file://{os.path.abspath(html_file)}")
            print(f"🚀 已自动打开浏览器")
        except:
            print(f"💡 请手动在浏览器中打开: {os.path.abspath(html_file)}")
        
    except Exception as e:
        print(f"❌ 生成HTML报告时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
