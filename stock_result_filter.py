#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选股结果过滤和分析脚本
按股票分类，合并15天内的重复选股，统计成功率
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import json

class StockResultFilter:
    """选股结果过滤器"""
    
    def __init__(self, excel_file: str):
        self.excel_file = excel_file
        self.df = None
        self.composite_data = []
        
    def load_data(self):
        """加载Excel数据"""
        print(f"📊 加载数据文件: {self.excel_file}")
        
        if not os.path.exists(self.excel_file):
            raise FileNotFoundError(f"文件不存在: {self.excel_file}")
        
        # 读取Excel文件
        self.df = pd.read_excel(self.excel_file)
        print(f"✅ 成功加载 {len(self.df)} 条记录")
        
        # 显示列名
        print(f"📋 数据列: {list(self.df.columns)}")
        
        # 检查必要的列
        required_cols = ['股票', '买入日期', '5日成功选股', '5日最大涨幅']
        missing_cols = [col for col in required_cols if col not in self.df.columns]
        
        if missing_cols:
            print(f"⚠️ 缺失必要列: {missing_cols}")
            # 尝试找到相似的列名
            for missing_col in missing_cols:
                similar_cols = [col for col in self.df.columns if missing_col.replace('5日', '') in col]
                if similar_cols:
                    print(f"   可能的替代列: {similar_cols}")
        
        return self.df
    
    def preprocess_data(self):
        """预处理数据"""
        print("\n🔧 预处理数据...")
        
        # 转换买入日期为datetime
        if '买入日期' in self.df.columns:
            self.df['买入日期'] = pd.to_datetime(self.df['买入日期'], errors='coerce')
        
        # 处理5日最大涨幅，转换为百分比数值
        if '5日最大涨幅' in self.df.columns:
            # 处理可能的百分号和字符串格式
            def parse_percentage(val):
                if pd.isna(val):
                    return np.nan
                if isinstance(val, str):
                    # 移除%符号和空格
                    val = val.replace('%', '').replace(' ', '')
                    try:
                        return float(val)
                    except:
                        return np.nan
                # 如果是小数格式（如0.1表示10%），转换为百分比
                num_val = float(val) if not pd.isna(val) else np.nan
                # 如果数值在0-1之间，可能是小数格式，转换为百分比
                if not pd.isna(num_val) and -1 <= num_val <= 1:
                    return num_val * 100
                return num_val

            self.df['5日最大涨幅_数值'] = self.df['5日最大涨幅'].apply(parse_percentage)
        
        # 移除无效数据
        valid_mask = (
            pd.notna(self.df['股票']) & 
            pd.notna(self.df['买入日期']) &
            pd.notna(self.df['5日最大涨幅_数值'])
        )
        
        invalid_count = len(self.df) - valid_mask.sum()
        if invalid_count > 0:
            print(f"⚠️ 移除 {invalid_count} 条无效记录")
        
        self.df = self.df[valid_mask].copy()
        
        # 按股票和买入日期排序
        self.df = self.df.sort_values(['股票', '买入日期']).reset_index(drop=True)
        
        print(f"✅ 预处理完成，有效记录: {len(self.df)} 条")
        
        return self.df
    
    def group_by_stock_and_time(self, max_days_gap: int = 15):
        """按股票分组，合并时间间隔不超过max_days_gap天的记录"""
        print(f"\n🔗 按股票分组并合并 {max_days_gap} 天内的重复选股...")
        
        self.composite_data = []
        
        # 按股票分组
        for stock_code, stock_group in self.df.groupby('股票'):
            stock_group = stock_group.sort_values('买入日期').reset_index(drop=True)
            
            # 对每只股票的记录进行时间聚合
            i = 0
            while i < len(stock_group):
                # 开始一个新的复合记录
                composite_record = {
                    'stock_code': stock_code,
                    'records': [stock_group.iloc[i]],
                    'start_date': stock_group.iloc[i]['买入日期'],
                    'end_date': stock_group.iloc[i]['买入日期']
                }
                
                # 查找时间间隔内的其他记录
                j = i + 1
                while j < len(stock_group):
                    current_date = stock_group.iloc[j]['买入日期']
                    days_diff = (current_date - composite_record['end_date']).days
                    
                    if days_diff <= max_days_gap:
                        # 添加到当前复合记录
                        composite_record['records'].append(stock_group.iloc[j])
                        composite_record['end_date'] = current_date
                        j += 1
                    else:
                        # 时间间隔超过阈值，停止合并
                        break
                
                self.composite_data.append(composite_record)
                i = j  # 从下一个未处理的记录开始
        
        print(f"✅ 生成 {len(self.composite_data)} 个复合记录")
        
        # 统计信息
        total_original = len(self.df)
        total_composite = len(self.composite_data)
        compression_ratio = (total_original - total_composite) / total_original * 100
        
        print(f"📊 压缩统计:")
        print(f"   原始记录: {total_original} 条")
        print(f"   复合记录: {total_composite} 条")
        print(f"   压缩率: {compression_ratio:.1f}%")
        
        return self.composite_data
    
    def analyze_composite_success(self, success_threshold: float = 10.0):
        """分析复合记录的成功率"""
        print(f"\n📈 分析复合记录成功率 (阈值: {success_threshold}%)...")
        
        success_count = 0
        total_count = len(self.composite_data)
        
        for composite in self.composite_data:
            # 检查复合记录中是否有任何一条记录的5日最大涨幅超过阈值
            max_gains = [record['5日最大涨幅_数值'] for record in composite['records']]
            max_gain = max(max_gains) if max_gains else 0
            
            # 判断成功
            is_success = bool(max_gain >= success_threshold)
            composite['is_success'] = is_success
            composite['max_gain'] = float(max_gain) if not pd.isna(max_gain) else 0.0
            composite['record_count'] = len(composite['records'])
            
            if is_success:
                success_count += 1
        
        success_rate = success_count / total_count * 100 if total_count > 0 else 0
        
        print(f"✅ 成功率分析完成:")
        print(f"   总复合记录: {total_count}")
        print(f"   成功记录: {success_count}")
        print(f"   成功率: {success_rate:.2f}%")
        
        return success_rate

    def analyze_monthly_statistics(self):
        """按月统计分析数据"""
        print(f"\n📅 按月统计分析...")

        monthly_stats = {}

        for composite in self.composite_data:
            # 使用开始日期的年月作为分组键
            start_date = composite['start_date']
            month_key = start_date.strftime('%Y-%m')

            if month_key not in monthly_stats:
                monthly_stats[month_key] = {
                    'stocks': set(),
                    'composite_records': 0,
                    'success_records': 0,
                    'total_individual_records': 0
                }

            # 统计数据
            monthly_stats[month_key]['stocks'].add(composite['stock_code'])
            monthly_stats[month_key]['composite_records'] += 1
            monthly_stats[month_key]['total_individual_records'] += composite['record_count']

            if composite['is_success']:
                monthly_stats[month_key]['success_records'] += 1

        # 转换为最终格式并计算成功率
        final_monthly_stats = {}
        for month, stats in monthly_stats.items():
            final_monthly_stats[month] = {
                'total_stocks': len(stats['stocks']),
                'composite_records': stats['composite_records'],
                'success_records': stats['success_records'],
                'success_rate': stats['success_records'] / stats['composite_records'] * 100 if stats['composite_records'] > 0 else 0,
                'total_individual_records': stats['total_individual_records']
            }

        # 按月份排序
        sorted_months = sorted(final_monthly_stats.keys())

        print(f"📊 月度统计结果:")
        print("-" * 80)
        print(f"{'月份':<10} {'总股票数':<8} {'复合记录数':<10} {'成功记录数':<10} {'成功率':<8} {'原始记录数':<10}")
        print("-" * 80)

        for month in sorted_months:
            stats = final_monthly_stats[month]
            print(f"{month:<10} {stats['total_stocks']:<8} {stats['composite_records']:<10} "
                  f"{stats['success_records']:<10} {stats['success_rate']:<8.1f}% {stats['total_individual_records']:<10}")

        return final_monthly_stats

    def generate_formatted_output(self, output_file: str = None):
        """生成格式化的输出文件"""
        # 如果没有指定输出文件名，使用时间戳生成
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"composite_stock_analysis_{timestamp}.json"

        print(f"\n📝 生成格式化输出文件: {output_file}")
        
        # 准备输出数据
        output_data = {
            'analysis_info': {
                'source_file': self.excel_file,
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_original_records': len(self.df),
                'total_composite_records': len(self.composite_data),
                'success_threshold': 10.0,
                'max_days_gap': 15
            },
            'summary': {
                'total_stocks': len(set([comp['stock_code'] for comp in self.composite_data])),
                'total_composite_records': len(self.composite_data),
                'successful_records': sum(1 for comp in self.composite_data if comp['is_success']),
                'success_rate': sum(1 for comp in self.composite_data if comp['is_success']) / len(self.composite_data) * 100 if self.composite_data else 0
            },
            'composite_records': []
        }
        
        # 添加详细的复合记录
        for i, composite in enumerate(self.composite_data):
            record_data = {
                'id': i + 1,
                'stock_code': composite['stock_code'],
                'start_date': composite['start_date'].strftime('%Y-%m-%d'),
                'end_date': composite['end_date'].strftime('%Y-%m-%d'),
                'record_count': composite['record_count'],
                'max_gain': float(round(composite['max_gain'], 2)),
                'is_success': bool(composite['is_success']),
                'days_span': int((composite['end_date'] - composite['start_date']).days),
                'individual_records': []
            }
            
            # 添加个别记录详情
            for record in composite['records']:
                individual_record = {
                    'buy_date': record['买入日期'].strftime('%Y-%m-%d'),
                    'gain_5d': float(round(record['5日最大涨幅_数值'], 2)),
                    'success_flag': str(record.get('5日成功选股', 'N/A'))
                }
                
                # 添加其他可用的列
                for col in self.df.columns:
                    if col not in ['买入日期', '5日最大涨幅_数值', '5日最大涨幅', '5日成功选股']:
                        value = record.get(col, 'N/A')
                        # 确保值是JSON可序列化的
                        if pd.isna(value):
                            individual_record[col] = None
                        elif isinstance(value, (pd.Timestamp, datetime)):
                            individual_record[col] = value.strftime('%Y-%m-%d') if hasattr(value, 'strftime') else str(value)
                        elif isinstance(value, (np.integer, np.floating)):
                            individual_record[col] = float(value)
                        elif isinstance(value, np.bool_):
                            individual_record[col] = bool(value)
                        else:
                            individual_record[col] = str(value)
                
                record_data['individual_records'].append(individual_record)
            
            output_data['composite_records'].append(record_data)
        
        # 保存为JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 格式化文件已保存: {output_file}")
        
        return output_data
    
    def run_analysis(self, max_days_gap: int = 15, success_threshold: float = 10.0):
        """运行完整分析流程"""
        print("🚀 开始选股结果分析...")
        print("=" * 60)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 预处理
        self.preprocess_data()
        
        # 3. 按股票和时间分组
        self.group_by_stock_and_time(max_days_gap)
        
        # 4. 分析成功率
        success_rate = self.analyze_composite_success(success_threshold)

        # 5. 按月统计分析
        monthly_stats = self.analyze_monthly_statistics()

        # 6. 生成输出文件
        output_data = self.generate_formatted_output()

        # 7. 将月度统计添加到输出数据
        output_data['monthly_statistics'] = monthly_stats
        
        print("\n" + "=" * 60)
        print("🎉 分析完成!")
        print(f"📊 最终成功率: {success_rate:.2f}%")
        
        return output_data

def main():
    """主函数"""
    # 数据文件路径
    excel_file = "选股分析结果/选股分析结果_20250804_222043.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        print("请确认文件路径是否正确")
        return
    
    # 创建过滤器并运行分析
    filter_tool = StockResultFilter(excel_file)
    
    try:
        output_data = filter_tool.run_analysis(
            max_days_gap=15,      # 15天内的记录合并
            success_threshold=10.0 # 10%涨幅阈值
        )
        
        print(f"\n💡 分析要点:")
        print(f"1. 原始记录被按股票分组，15天内的重复选股合并为复合记录")
        print(f"2. 复合记录中任意一条记录5日涨幅≥10%即视为成功")
        print(f"3. 结果已保存到 composite_stock_analysis.json")
        print(f"4. 接下来将生成HTML可视化报告...")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
