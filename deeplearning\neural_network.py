#!/usr/bin/env python3
"""
深度学习神经网络模型
用于股票涨跌预测的二分类任务
"""

import torch
import torch.nn as torch_nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import warnings
warnings.filterwarnings('ignore')

class PrecisionFocusedLoss(torch_nn.Module):
    """专注精确率的损失函数 - 优化预测为正样本且实际为正样本的概率"""

    def __init__(self, alpha=3.0, beta=0.3, gamma=0.1):
        super().__init__()
        self.alpha = alpha  # 正样本权重 - 重点关注
        self.beta = beta    # 负样本权重 - 适度惩罚
        self.gamma = gamma  # 精确率正则化权重

    def forward(self, outputs, targets):
        # 确保维度一致
        outputs = outputs.squeeze()
        targets = targets.squeeze().float()

        # 计算概率
        probs = torch.sigmoid(outputs)

        # 基础BCE损失，但调整权重
        pos_mask = targets == 1
        neg_mask = targets == 0

        # 正样本损失：强烈鼓励正确识别正样本
        if pos_mask.sum() > 0:
            pos_loss = -self.alpha * torch.log(probs[pos_mask] + 1e-8).mean()
        else:
            pos_loss = torch.tensor(0.0, device=outputs.device)

        # 负样本损失：适度惩罚误判负样本
        if neg_mask.sum() > 0:
            neg_loss = -self.beta * torch.log(1 - probs[neg_mask] + 1e-8).mean()
        else:
            neg_loss = torch.tensor(0.0, device=outputs.device)

        # 精确率正则化：鼓励高置信度的正预测
        # 对于高概率预测，如果是正样本给奖励，如果是负样本给惩罚
        high_conf_mask = probs > 0.7
        if high_conf_mask.sum() > 0:
            high_conf_targets = targets[high_conf_mask]
            precision_reg = -self.gamma * (high_conf_targets.mean() - 0.5)  # 鼓励高置信度预测的精确率
        else:
            precision_reg = torch.tensor(0.0, device=outputs.device)

        return pos_loss + neg_loss + precision_reg

class TopKPrecisionLoss(torch_nn.Module):
    """TopK精确率损失函数 - 直接优化TopK的精确率"""

    def __init__(self, k=10, lambda_reg=0.2):
        super().__init__()
        self.k = k
        self.lambda_reg = lambda_reg

    def forward(self, outputs, targets):
        # 确保维度一致
        outputs = outputs.squeeze()
        targets = targets.squeeze().float()
        batch_size = outputs.size(0)

        probs = torch.sigmoid(outputs)

        # 获取TopK预测
        k_actual = min(self.k, batch_size)
        _, topk_indices = torch.topk(probs, k_actual)

        # TopK中的正样本比例
        topk_targets = targets[topk_indices]
        topk_precision = topk_targets.mean()

        # 主要损失：1 - TopK精确率 (确保需要梯度)
        precision_loss = torch.tensor(1.0, device=outputs.device, requires_grad=True) - topk_precision

        # 正则化：鼓励正样本有更高概率
        pos_mask = targets == 1
        if pos_mask.sum() > 0:
            pos_probs = probs[pos_mask]
            reg_loss = -torch.log(pos_probs + 1e-8).mean()
        else:
            reg_loss = torch.tensor(0.0, device=outputs.device)

        return precision_loss + self.lambda_reg * reg_loss

class PrecisionMLP(torch_nn.Module):
    """精确度优化的多层感知机 - 防过拟合优化版"""

    def __init__(self, input_dim, hidden_dims=[64, 32, 16], dropout_rate=0.5):
        super(PrecisionMLP, self).__init__()

        layers = []
        prev_dim = input_dim

        for i, hidden_dim in enumerate(hidden_dims):
            layers.append(torch_nn.Linear(prev_dim, hidden_dim))
            layers.append(torch_nn.BatchNorm1d(hidden_dim))  # 批归一化
            layers.append(torch_nn.ReLU())
            # 逐层递增dropout率
            dropout_rate_layer = dropout_rate * (1 + i * 0.1)
            layers.append(torch_nn.Dropout(min(dropout_rate_layer, 0.7)))
            prev_dim = hidden_dim

        # 输出层
        layers.append(torch_nn.Linear(prev_dim, 1))
        layers.append(torch_nn.Sigmoid())

        self.network = torch_nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

class ResidualNet(torch_nn.Module):
    """残差网络 - 防过拟合优化版"""

    def __init__(self, input_dim, hidden_dims=[64, 32, 16], dropout_rate=0.4):
        super(ResidualNet, self).__init__()

        self.input_layer = torch_nn.Linear(input_dim, hidden_dims[0])
        self.input_bn = torch_nn.BatchNorm1d(hidden_dims[0])

        self.hidden_layers = torch_nn.ModuleList()
        self.batch_norms = torch_nn.ModuleList()
        self.dropouts = torch_nn.ModuleList()

        for i in range(len(hidden_dims) - 1):
            self.hidden_layers.append(torch_nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            self.batch_norms.append(torch_nn.BatchNorm1d(hidden_dims[i+1]))
            # 逐层递增dropout率
            dropout_rate_layer = dropout_rate * (1 + i * 0.15)
            self.dropouts.append(torch_nn.Dropout(min(dropout_rate_layer, 0.7)))

        self.output_layer = torch_nn.Linear(hidden_dims[-1], 1)

    def forward(self, x):
        # 输入层
        out = F.relu(self.input_bn(self.input_layer(x)))

        # 残差连接的隐藏层
        for i, (layer, bn, dropout) in enumerate(zip(self.hidden_layers, self.batch_norms, self.dropouts)):
            residual = out
            out = F.relu(bn(layer(out)))
            out = dropout(out)

            # 残差连接（如果维度匹配）
            if residual.shape == out.shape:
                out = out + residual

        # 输出层 (不使用sigmoid，因为使用BCEWithLogitsLoss)
        out = self.output_layer(out)
        return out

class AttentionNet(torch_nn.Module):
    """注意力网络"""

    def __init__(self, input_dim, hidden_dims=[128, 64, 32], dropout_rate=0.3, num_heads=4):
        super(AttentionNet, self).__init__()

        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.num_heads = num_heads

        # 输入投影
        self.input_projection = torch_nn.Linear(input_dim, hidden_dims[0])

        # 多头注意力
        self.attention = torch_nn.MultiheadAttention(
            embed_dim=hidden_dims[0],
            num_heads=num_heads,
            dropout=dropout_rate,
            batch_first=True
        )

        # 前馈网络
        self.feed_forward = torch_nn.ModuleList()
        prev_dim = hidden_dims[0]

        for hidden_dim in hidden_dims[1:]:
            self.feed_forward.append(torch_nn.Linear(prev_dim, hidden_dim))
            self.feed_forward.append(torch_nn.ReLU())
            self.feed_forward.append(torch_nn.Dropout(dropout_rate))
            prev_dim = hidden_dim

        # 输出层
        self.output_layer = torch_nn.Linear(prev_dim, 1)
        self.dropout = torch_nn.Dropout(dropout_rate)

    def forward(self, x):
        # 输入投影
        x = F.relu(self.input_projection(x))

        # 为注意力机制添加序列维度
        x = x.unsqueeze(1)  # (batch_size, 1, hidden_dim)

        # 多头注意力
        attn_output, _ = self.attention(x, x, x)
        attn_output = attn_output.squeeze(1)  # 移除序列维度

        # 残差连接
        x = x.squeeze(1) + attn_output
        x = self.dropout(x)

        # 前馈网络
        for layer in self.feed_forward:
            x = layer(x)

        # 输出层 (不使用sigmoid，因为使用BCEWithLogitsLoss)
        x = self.output_layer(x)
        return x

class StockNeuralNetwork:
    """股票预测神经网络管理器"""

    def __init__(self, config_path="deeplearning/config.json"):
        """初始化神经网络"""
        self.config_path = config_path
        self.config = self.load_config()
        self.models = {}
        self.history = {}
        self.feature_names = None
        self.device = self.get_device()

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None

    def get_device(self):
        """获取计算设备"""
        if self.config and self.config['training']['device'] == 'auto':
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            device = torch.device('cpu')

        print(f"🖥️ 使用设备: {device}")
        return device
    
    def build_models(self, input_dim):
        """构建所有神经网络模型"""
        print("🏗️ 构建神经网络模型...")

        model_configs = self.config['models']

        for model_name, config in model_configs.items():
            print(f"   构建 {model_name}...")

            if model_name == 'PrecisionMLP':
                model = PrecisionMLP(
                    input_dim=input_dim,
                    hidden_dims=config['hidden_dims'],
                    dropout_rate=config['dropout_rate']
                )
            elif model_name == 'ResidualNet':
                model = ResidualNet(
                    input_dim=input_dim,
                    hidden_dims=config['hidden_dims'],
                    dropout_rate=config['dropout_rate']
                )
            elif model_name == 'AttentionNet':
                model = AttentionNet(
                    input_dim=input_dim,
                    hidden_dims=config['hidden_dims'],
                    dropout_rate=config['dropout_rate'],
                    num_heads=config['num_heads']
                )
            else:
                print(f"   ❌ 未知模型类型: {model_name}")
                continue

            model = model.to(self.device)
            self.models[model_name] = model

            print(f"   ✅ {model_name} 构建完成")

        return self.models
    
    def train_model(self, model_name, X_train, y_train, X_val=None, y_val=None):
        """训练单个模型"""
        if model_name not in self.models:
            print(f"❌ 模型 {model_name} 不存在")
            return None

        model = self.models[model_name]
        training_config = self.config['training']

        # 准备数据
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(np.array(y_train).reshape(-1, 1)).to(self.device)

        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(
            train_dataset,
            batch_size=training_config['batch_size'],
            shuffle=True
        )

        # 验证数据
        val_loader = None
        if X_val is not None and y_val is not None:
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(np.array(y_val).reshape(-1, 1)).to(self.device)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=training_config['batch_size'])

        # 优化器和损失函数
        weight_decay = training_config.get('weight_decay', 0.01)
        optimizer = optim.Adam(model.parameters(),
                              lr=training_config['learning_rate'],
                              weight_decay=weight_decay)

        # 选择损失函数 - 专注精确率优化
        loss_type = training_config.get('loss_type', 'precision_focused')

        if loss_type == 'topk_precision':
            criterion = TopKPrecisionLoss(k=10, lambda_reg=0.2)
            print("   使用TopK精确率损失函数")
        elif loss_type == 'precision_focused':
            criterion = PrecisionFocusedLoss(alpha=2.0, beta=1.0, gamma=0.05)
            print("   使用精确率专注损失函数(平衡版)")
        else:
            # 传统BCE损失
            pos_weight = None
            class_weight = training_config.get('class_weight')
            if class_weight == 'balanced':
                pos_count = np.sum(y_train)
                neg_count = len(y_train) - pos_count
                if pos_count > 0:
                    pos_weight = torch.FloatTensor([neg_count / pos_count]).to(self.device)
                    print(f"   使用平衡权重: {pos_weight.item():.2f}")
            elif isinstance(class_weight, (int, float)):
                pos_weight = torch.FloatTensor([class_weight]).to(self.device)
                print(f"   使用固定权重: {pos_weight.item():.2f}")

            criterion = torch_nn.BCEWithLogitsLoss(pos_weight=pos_weight)
            print("   使用传统BCE损失函数")

        # 学习率调度器
        lr_config = training_config.get('lr_scheduler', {})
        if lr_config.get('type') == 'reduce_on_plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                factor=lr_config.get('factor', 0.5),
                patience=lr_config.get('patience', 8),
                min_lr=lr_config.get('min_lr', 1e-6)
            )
        else:
            scheduler = None

        # 训练历史
        history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }

        best_val_loss = float('inf')
        patience_counter = 0

        print(f"🚀 开始训练 {model_name}...")

        for epoch in range(training_config['epochs']):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                # 使用动态阈值，基于正样本比例
                threshold = 0.1  # 对于8.7%的正样本率，使用较低阈值
                # 将logits转换为概率
                probs = torch.sigmoid(outputs)
                predicted = (probs > threshold).float()
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()

            train_loss /= len(train_loader)
            train_acc = train_correct / train_total

            history['train_loss'].append(train_loss)
            history['train_acc'].append(train_acc)

            # 验证阶段
            val_loss = 0.0
            val_acc = 0.0

            if val_loader is not None:
                model.eval()
                val_correct = 0
                val_total = 0

                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = model(batch_X)
                        loss = criterion(outputs, batch_y)

                        val_loss += loss.item()
                        # 使用动态阈值，基于正样本比例
                        threshold = 0.1  # 对于8.7%的正样本率，使用较低阈值
                        # 将logits转换为概率
                        probs = torch.sigmoid(outputs)
                        predicted = (probs > threshold).float()
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()

                val_loss /= len(val_loader)
                val_acc = val_correct / val_total

                history['val_loss'].append(val_loss)
                history['val_acc'].append(val_acc)

                # 学习率调度器更新
                if scheduler is not None:
                    scheduler.step(val_loss)

                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.save_model(model_name, model, is_best=True)
                else:
                    patience_counter += 1

                if patience_counter >= training_config['early_stopping_patience']:
                    print(f"   早停触发，在第 {epoch+1} 轮停止训练")
                    break

            # 打印进度
            if (epoch + 1) % 10 == 0:
                if val_loader is not None:
                    print(f"   Epoch {epoch+1:3d}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                          f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
                else:
                    print(f"   Epoch {epoch+1:3d}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")

        self.history[model_name] = history
        print(f"✅ {model_name} 训练完成")

        return history
    
    def train_all_models(self, X_train, y_train, X_val=None, y_val=None):
        """训练所有模型"""
        print("🚀 开始训练所有神经网络模型...")
        print("=" * 60)

        if not self.models:
            self.build_models(X_train.shape[1])

        training_results = {}

        for model_name in self.models.keys():
            print(f"\n📋 训练 {model_name}:")
            print("-" * 40)

            start_time = datetime.now()
            history = self.train_model(model_name, X_train, y_train, X_val, y_val)
            end_time = datetime.now()

            training_time = end_time - start_time
            print(f"   训练用时: {training_time}")

            training_results[model_name] = {
                'history': history,
                'training_time': str(training_time)
            }

        print(f"\n✅ 所有模型训练完成！")
        return training_results
    
    def evaluate_model(self, model_name, X_test, y_test):
        """评估单个模型"""
        if model_name not in self.models:
            print(f"❌ 模型 {model_name} 不存在")
            return None

        model = self.models[model_name]
        model.eval()

        print(f"📊 评估 {model_name} 性能...")

        # 准备测试数据
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        y_test_tensor = torch.FloatTensor(np.array(y_test).reshape(-1, 1)).to(self.device)

        with torch.no_grad():
            # 预测 (输出logits)
            y_pred_logits = model(X_test_tensor)
            # 转换为概率
            y_pred_proba = torch.sigmoid(y_pred_logits).cpu().numpy()
            # 使用动态阈值，基于正样本比例
            threshold = 0.1  # 对于8.7%的正样本率，使用较低阈值
            y_pred = (y_pred_proba > threshold).astype(int)

            # 计算损失
            criterion = torch_nn.BCEWithLogitsLoss()
            test_loss = criterion(y_pred_logits, torch.FloatTensor(np.array(y_test).reshape(-1, 1)).to(self.device)).item()

        # 计算指标
        test_accuracy = (y_pred.flatten() == y_test).mean()

        # 精确率、召回率、F1分数
        from sklearn.metrics import precision_score, recall_score, f1_score
        test_precision = precision_score(y_test, y_pred.flatten(), zero_division=0)
        test_recall = recall_score(y_test, y_pred.flatten(), zero_division=0)
        test_f1 = f1_score(y_test, y_pred.flatten(), zero_division=0)

        # AUC分数
        auc_score = roc_auc_score(y_test, y_pred_proba.flatten())

        print(f"   准确率: {test_accuracy:.3f} | 精确率: {test_precision:.3f} | 召回率: {test_recall:.3f} | F1: {test_f1:.3f} | AUC: {auc_score:.3f}")

        # 混淆矩阵（简化显示）
        cm = confusion_matrix(y_test, y_pred.flatten())
        print(f"   混淆矩阵: [[{cm[0,0]}, {cm[0,1]}], [{cm[1,0]}, {cm[1,1]}]]")

        return {
            'model_name': model_name,
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'test_precision': test_precision,
            'test_recall': test_recall,
            'f1_score': test_f1,
            'auc_score': auc_score,
            'confusion_matrix': cm,
            'y_pred': y_pred.flatten(),
            'y_pred_proba': y_pred_proba.flatten()
        }

    def evaluate_all_models(self, X_test, y_test):
        """评估所有模型"""
        print("📊 评估所有模型性能...")
        print("=" * 60)

        evaluation_results = {}

        for model_name in self.models.keys():
            print(f"\n📋 评估 {model_name}:")
            print("-" * 40)

            result = self.evaluate_model(model_name, X_test, y_test)
            if result:
                evaluation_results[model_name] = result

        # 比较模型性能
        if len(evaluation_results) > 1:
            print(f"\n🏆 模型性能比较:")
            print("-" * 60)
            print(f"{'模型':<15} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'AUC':<8}")
            print("-" * 60)

            for model_name, result in evaluation_results.items():
                print(f"{model_name:<15} {result['test_accuracy']:<8.3f} {result['test_precision']:<8.3f} "
                      f"{result['test_recall']:<8.3f} {result['f1_score']:<8.3f} {result['auc_score']:<8.3f}")

        return evaluation_results
    
    def predict(self, model_name, X):
        """使用指定模型预测新数据"""
        if model_name not in self.models:
            print(f"❌ 模型 {model_name} 不存在")
            return None, None

        model = self.models[model_name]
        model.eval()

        # 准备数据
        X_tensor = torch.FloatTensor(X).to(self.device)

        with torch.no_grad():
            # 预测logits
            logits = model(X_tensor)
            # 转换为概率
            probabilities = torch.sigmoid(logits).cpu().numpy()

            # 预测类别 (使用较低阈值)
            threshold = 0.1  # 适应不平衡数据
            predictions = (probabilities > threshold).astype(int)

        return predictions.flatten(), probabilities.flatten()

    def predict_single_model(self, X, model_name):
        """使用单个模型进行预测（返回概率，不应用阈值）"""
        if model_name not in self.models:
            print(f"❌ 模型 {model_name} 不存在")
            return None, None

        model = self.models[model_name]
        model.eval()

        # 准备数据
        X_tensor = torch.FloatTensor(X).to(self.device)

        with torch.no_grad():
            # 预测logits
            logits = model(X_tensor)
            # 转换为概率
            probabilities = torch.sigmoid(logits).cpu().numpy()

        # 返回概率，让调用者决定阈值
        return None, probabilities.flatten()

    def predict_ensemble(self, X, method='average'):
        """集成预测"""
        if not self.models:
            print("❌ 没有可用模型")
            return None, None

        all_predictions = []
        all_probabilities = []

        for model_name in self.models.keys():
            pred, prob = self.predict(model_name, X)
            if pred is not None:
                all_predictions.append(pred)
                all_probabilities.append(prob)

        if not all_predictions:
            return None, None

        # 集成方法
        if method == 'average':
            # 平均概率
            ensemble_prob = np.mean(all_probabilities, axis=0)
            ensemble_pred = (ensemble_prob > 0.5).astype(int)
        elif method == 'voting':
            # 投票
            ensemble_pred = np.round(np.mean(all_predictions, axis=0)).astype(int)
            ensemble_prob = np.mean(all_probabilities, axis=0)
        else:
            print(f"❌ 未知集成方法: {method}")
            return None, None

        return ensemble_pred, ensemble_prob
    
    def save_model(self, model_name, model=None, is_best=False):
        """保存模型"""
        if model is None and model_name in self.models:
            model = self.models[model_name]

        if model is None:
            print(f"❌ 模型 {model_name} 不存在")
            return None

        model_dir = self.config['data']['model_path']
        os.makedirs(model_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if is_best:
            model_path = os.path.join(model_dir, f"best_{model_name}.pth")
        else:
            model_path = os.path.join(model_dir, f"{model_name}_{timestamp}.pth")

        torch.save({
            'model_state_dict': model.state_dict(),
            'model_name': model_name,
            'timestamp': timestamp,
            'config': self.config
        }, model_path)

        # print(f"💾 模型已保存: {model_path}")

        # 保存最新模型
        latest_path = os.path.join(model_dir, f"latest_{model_name}.pth")
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_name': model_name,
            'timestamp': timestamp,
            'config': self.config
        }, latest_path)

        return model_path

    def save_all_models(self):
        """保存所有模型"""
        saved_paths = {}
        for model_name in self.models.keys():
            path = self.save_model(model_name)
            if path:
                saved_paths[model_name] = path
        return saved_paths

    def load_model(self, model_name, model_path):
        """加载模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)

            # 从检查点获取模型配置信息
            saved_config = checkpoint.get('config', {})

            # 确定输入维度：优先使用保存的配置，其次使用当前特征数量
            if 'data' in saved_config and 'feature_columns' in saved_config['data']:
                input_dim = len(saved_config['data']['feature_columns'])
                print(f"   使用保存的特征维度: {input_dim}")
            elif self.feature_names:
                input_dim = len(self.feature_names)
                print(f"   使用当前特征维度: {input_dim}")
            else:
                # 尝试从模型权重推断输入维度
                if model_name == 'PrecisionMLP':
                    input_dim = checkpoint['model_state_dict']['network.0.weight'].shape[1]
                elif model_name == 'ResidualNet':
                    input_dim = checkpoint['model_state_dict']['input_layer.weight'].shape[1]
                elif model_name == 'AttentionNet':
                    input_dim = checkpoint['model_state_dict']['input_projection.weight'].shape[1]
                else:
                    input_dim = 16  # 默认值
                print(f"   从模型权重推断维度: {input_dim}")

            # 重建模型
            if model_name == 'PrecisionMLP':
                model = PrecisionMLP(
                    input_dim=input_dim,
                    hidden_dims=self.config['models'][model_name]['hidden_dims'],
                    dropout_rate=self.config['models'][model_name]['dropout_rate']
                )
            elif model_name == 'ResidualNet':
                model = ResidualNet(
                    input_dim=input_dim,
                    hidden_dims=self.config['models'][model_name]['hidden_dims'],
                    dropout_rate=self.config['models'][model_name]['dropout_rate']
                )
            elif model_name == 'AttentionNet':
                model = AttentionNet(
                    input_dim=input_dim,
                    hidden_dims=self.config['models'][model_name]['hidden_dims'],
                    dropout_rate=self.config['models'][model_name]['dropout_rate'],
                    num_heads=self.config['models'][model_name]['num_heads']
                )
            else:
                print(f"❌ 未知模型类型: {model_name}")
                return False

            model.load_state_dict(checkpoint['model_state_dict'])
            model = model.to(self.device)
            model.eval()

            self.models[model_name] = model
            print(f"✅ 模型 {model_name} 已加载: {model_path}")
            return True

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def plot_training_history(self, model_name=None, save_path=None):
        """绘制训练历史"""
        if not self.history:
            print("❌ 没有训练历史")
            return

        if model_name and model_name not in self.history:
            print(f"❌ 模型 {model_name} 没有训练历史")
            return

        # 如果指定了模型，只绘制该模型的历史
        if model_name:
            histories = {model_name: self.history[model_name]}
        else:
            histories = self.history

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        for name, history in histories.items():
            # 损失
            axes[0, 0].plot(history['train_loss'], label=f'{name} 训练损失')
            if history['val_loss']:
                axes[0, 0].plot(history['val_loss'], label=f'{name} 验证损失')

            # 准确率
            axes[0, 1].plot(history['train_acc'], label=f'{name} 训练准确率')
            if history['val_acc']:
                axes[0, 1].plot(history['val_acc'], label=f'{name} 验证准确率')

        axes[0, 0].set_title('模型损失')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()

        axes[0, 1].set_title('模型准确率')
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('准确率')
        axes[0, 1].legend()

        # 如果只有一个模型，显示更多细节
        if len(histories) == 1:
            history = list(histories.values())[0]

            # 学习曲线
            axes[1, 0].plot(range(len(history['train_loss'])), history['train_loss'], 'b-', label='训练损失')
            if history['val_loss']:
                axes[1, 0].plot(range(len(history['val_loss'])), history['val_loss'], 'r-', label='验证损失')
            axes[1, 0].set_title('学习曲线')
            axes[1, 0].set_xlabel('轮次')
            axes[1, 0].set_ylabel('损失')
            axes[1, 0].legend()

            # 准确率曲线
            axes[1, 1].plot(range(len(history['train_acc'])), history['train_acc'], 'b-', label='训练准确率')
            if history['val_acc']:
                axes[1, 1].plot(range(len(history['val_acc'])), history['val_acc'], 'r-', label='验证准确率')
            axes[1, 1].set_title('准确率曲线')
            axes[1, 1].set_xlabel('轮次')
            axes[1, 1].set_ylabel('准确率')
            axes[1, 1].legend()
        else:
            # 多模型比较
            axes[1, 0].axis('off')
            axes[1, 1].axis('off')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 训练历史图已保存: {save_path}")

        plt.show()

    def get_feature_importance(self, model_name, X_test, y_test, method='permutation'):
        """计算特征重要性"""
        if model_name not in self.models or self.feature_names is None:
            print("❌ 模型或特征名称未设置")
            return None

        model = self.models[model_name]
        print(f"🔍 计算 {model_name} 特征重要性 (方法: {method})...")

        if method == 'permutation':
            # 排列重要性
            baseline_result = self.evaluate_model(model_name, X_test, y_test)
            baseline_score = baseline_result['test_accuracy']

            importance_scores = []

            for i, feature_name in enumerate(self.feature_names):
                # 复制测试数据
                X_test_permuted = X_test.copy()

                # 随机打乱第i个特征
                np.random.shuffle(X_test_permuted[:, i])

                # 计算打乱后的性能
                permuted_result = self.evaluate_model(model_name, X_test_permuted, y_test)
                permuted_score = permuted_result['test_accuracy']

                # 重要性 = 基线性能 - 打乱后性能
                importance = baseline_score - permuted_score
                importance_scores.append(importance)

                print(f"   {feature_name}: {importance:.4f}")

            # 创建重要性DataFrame
            feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': importance_scores
            }).sort_values('importance', ascending=False)

            return feature_importance

        else:
            print(f"❌ 不支持的特征重要性方法: {method}")
            return None

    def get_all_feature_importance(self, X_test, y_test, method='permutation'):
        """计算所有模型的特征重要性"""
        all_importance = {}

        for model_name in self.models.keys():
            importance = self.get_feature_importance(model_name, X_test, y_test, method)
            if importance is not None:
                all_importance[model_name] = importance

        return all_importance

if __name__ == "__main__":
    # 测试神经网络
    from data_preprocessor import StockDataPreprocessor

    # 预处理数据
    preprocessor = StockDataPreprocessor()
    data_path = "选股分析结果/选股分析结果_20250730_225530.xlsx"
    processed_data = preprocessor.preprocess_training_data(data_path)

    if processed_data:
        # 创建和训练模型
        nn = StockNeuralNetwork()
        nn.feature_names = processed_data['feature_names']

        # 构建模型
        nn.build_models(processed_data['X_train'].shape[1])

        # 训练所有模型
        training_results = nn.train_all_models(
            processed_data['X_train'],
            processed_data['y_train'],
            processed_data['X_test'],
            processed_data['y_test']
        )

        # 评估所有模型
        evaluation_results = nn.evaluate_all_models(
            processed_data['X_test'],
            processed_data['y_test']
        )

        # 特征重要性
        importance_results = nn.get_all_feature_importance(
            processed_data['X_test'],
            processed_data['y_test']
        )

        # 保存所有模型
        saved_paths = nn.save_all_models()

        print(f"\n🎉 测试完成！")
        print(f"📁 模型保存路径: {saved_paths}")
