import dash
from dash import dcc, html, Input, Output, State, callback_context, no_update, ALL
import dash_bootstrap_components as dbc
import pandas as pd
import json
from select_stock import main as select_stock_main, multi_strategy_select, format_multi_strategy_result
import threading
import logging
import os
from dash import dash_table
from datetime import datetime, timedelta

# 初始化Dash应用
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP], suppress_callback_exceptions=True)
app.title = "选股助手"

# 全局变量存储选择器实例，避免重复创建
_selector_instances = {}

# === 全局变量和缓冲区 ===
strategy_result_buffer = []
strategy_result_lock = threading.Lock()
strategy_calc_thread = None
strategy_calc_running = False
# 可配置的缓冲步长
BUFFER_STEP_SIZE = 1000  # 每次刷新推进的天数，可自行修改
# 分批推进信号
strategy_calc_should_continue = threading.Event()

# 日志捕获相关
_log_buffer = []
_log_buffer_lock = threading.Lock()
_log_content = ""

class DashLogHandler(logging.Handler):
    def emit(self, record):
        try:
            msg = self.format(record)
            global _log_content
            with _log_buffer_lock:
                _log_buffer.append(msg)
                if len(_log_buffer) > 100:
                    _log_buffer.pop(0)
                _log_content = "\n".join(_log_buffer)
        except Exception:
            pass

dash_logger = logging.getLogger("dash_strategy")
dash_logger.setLevel(logging.INFO)
dash_handler = DashLogHandler()
dash_handler.setFormatter(logging.Formatter('%(message)s'))
dash_logger.addHandler(dash_handler)
dash_logger.propagate = False

# 动态读取策略配置
def load_default_config():
    with open("configs.json", "r", encoding="utf-8") as f:
            return json.load(f)

def generate_strategy_forms(config):
    forms = []
    selectors = config.get("selectors", [])
    for idx, sel in enumerate(selectors):
        params = sel.get("params", {})
        param_inputs = []
        for k, v in params.items():
            input_type = "number" if isinstance(v, (int, float)) else "text"
            param_inputs.append(
                dbc.Row([
                    dbc.Col(dbc.Label(k, style={"fontSize": "12px"}), width=6),
                    dbc.Col(dbc.Input(id={"type": "param-input", "idx": idx, "param": k}, value=v, type=input_type, size="sm"), width=6)
                ], className="mb-1")
            )
        forms.append(
            dbc.Col(
                dbc.Card([
                    dbc.CardHeader([
                        dbc.Checkbox(id={"type": "activate-checkbox", "idx": idx}, value=sel.get("activate", False), style={"marginRight": "8px"}),
                        html.Span(f"{sel.get('alias', sel['class'])} ({sel['class']})", style={"fontWeight": "bold", "fontSize": "14px"})
                    ]),
                    dbc.CardBody(param_inputs, style={"padding": "10px"})
                ], className="mb-2", style={"minWidth": "280px", "maxWidth": "320px", "margin": "0 auto"}),
                width=4,
                style={"display": "flex", "justifyContent": "center"}
            )
        )
    return [dbc.Row(forms, className="mb-2 justify-content-center")]

def get_stock_options(data_dir):
        return []

# 策略选股页面
with open("configs.json", "r", encoding="utf-8") as f:
    default_config = json.load(f)
strategy_page = dbc.Container([
    dcc.Store(id="strategy-progress", data={"current_day": 0, "total_days": 0, "results": {}, "is_running": False}),
    dcc.Store(id="trading-progress", data={"is_running": False}),
    html.H4("策略选股", className="mb-4 mt-2"),
    dbc.Row([
        dbc.Col([
            dbc.Form([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("行情数据目录"),
                        dbc.Input(id="strategy-data-dir", value="stock_data", placeholder="如 stock_data", type="text")
                    ], width=3),
                    dbc.Col([
                        dbc.Label("策略配置文件"),
                        dbc.Input(id="strategy-config", value="configs.json", placeholder="如 configs.json", type="text")
                    ], width=3),
                    dbc.Col([
                        dbc.Label("股票代码（多选）"),
                        dcc.Dropdown(id="strategy-tickers-dropdown", options=[], multi=True, placeholder="请选择股票", optionHeight=32)
                    ], width=3),
                    dbc.Col([
                        dbc.Label("日期范围"),
                        dcc.DatePickerRange(id="strategy-date-range", display_format="YYYY-MM-DD")
                    ], width=2),
                    dbc.Col([
                        dbc.Label("买入价类型"),
                        dcc.Dropdown(id="buy-price-type", options=[{"label": "次日开盘价", "value": "open"}, {"label": "次日收盘价", "value": "close"}], value="open", clearable=False, style={"minWidth": "110px"})
                    ], width=1),
                    dbc.Col([
                        dbc.Label("评价天数"),
                        dbc.Input(id="judge-days", value="10", type="number", min=1, max=30, step=1, style={"minWidth": "60px"})
                    ], width=1),
                    dbc.Col([
                        dbc.Label("止损位%"),
                        dbc.Input(id="stop-loss-threshold", value="-3", type="number", step=0.1, style={"minWidth": "80px"})
                    ], width=2),
                    dbc.Col([
                        dbc.Label("止盈位%"),
                        dbc.Input(id="take-profit-threshold", value="5", type="number", step=0.1, style={"minWidth": "80px"})
                    ], width=2),
                ], className="mb-2"),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("套利止损%"),
                        dbc.Input(id="arbitrage-stop-loss", value="-3", type="number", step=0.1, style={"minWidth": "80px"})
                    ], width=2),
                    dbc.Col([
                        dbc.Label("套利止盈%"),
                        dbc.Input(id="arbitrage-take-profit", value="2", type="number", step=0.1, style={"minWidth": "80px"})
                    ], width=2),
                    dbc.Col([
                        dbc.Label("套利成功阈值%"),
                        dbc.Input(id="arbitrage-success-threshold", value="1", type="number", step=0.1, style={"minWidth": "80px"})
                    ], width=2),
                ], className="mb-2"),
                html.Hr(),
                html.H5("策略参数设置", className="mb-2"),
                dbc.Row(id="strategy-forms-container", children=generate_strategy_forms(default_config)),
                dbc.Row([
                    dbc.Col([
                        html.Div(id="selected-strategies-display", className="mt-2 p-2", style={
                            "backgroundColor": "#f8f9fa",
                            "border": "1px solid #dee2e6",
                            "borderRadius": "4px",
                            "minHeight": "40px"
                        })
                    ], width=12)
                ]),
                dbc.Row([
                    dbc.Col([
                        dbc.Button("开始选股", id="run-strategy-btn", color="primary", className="mt-2"),
                        dbc.Button("停止选股", id="stop-strategy-btn", color="danger", className="mt-2 ms-2", disabled=True),
                    ], width=6),
                    dbc.Col([
                        html.Div(id="progress-info", className="mt-2")
                    ], width=6)
                ]),
                dcc.Interval(id="strategy-interval", interval=200, n_intervals=0, disabled=True),
            ])
        ], width=12)
    ]),
    dbc.Row([
        dbc.Col([
            html.H5("选股结果", className="mt-4"),
            html.Div(id="strategy-result-output"),
            html.H5("策略统计", className="mt-4"),
            html.Div(id="strategy-stats-output"),
            html.H5("模拟交易", className="mt-4"),
            dbc.Row([
                dbc.Col([
                    dbc.Button("获取选股结果", id="get-strategy-results-btn", color="primary", className="mt-2"),
                ], width=12)
            ]),
            html.Div(id="strategy-results-display"),
            html.H5("日志输出", className="mt-4"),
            html.Pre(id="strategy-log-output", style={
                "background": "#222", 
                "color": "#0f0", 
                "minHeight": "100px", 
                "maxHeight": "300px", 
                "overflow": "auto", 
                "padding": "12px", 
                "whiteSpace": "pre-wrap", 
                "wordWrap": "break-word",
                "fontFamily": "monospace",
                "fontSize": "13px",
                "border": "1px solid #444",
                "borderRadius": "4px",
                "margin": "0",
                "lineHeight": "1.4"
            }),
        ], width=12)
    ])
], fluid=True)

# 选股分析页面
analysis_page = dbc.Container([
    html.H4("选股分析", className="mb-4 mt-2"),
    dbc.Row([
        dbc.Col([
            dbc.Form([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("选择日志文件", className="form-label"),
                        dcc.Dropdown(
                            id="log-file-dropdown",
                            placeholder="选择日志文件...",
                            style={"width": "100%"}
                        )
                    ], width=6),
                    dbc.Col([
                        dbc.Label("选择股票数据目录", className="form-label"),
                        dcc.Dropdown(
                            id="analysis-data-dir",
                            options=[
                                {"label": "stock_data", "value": "stock_data"},
                                {"label": "stock_data - 副本", "value": "stock_data - 副本"},
                                {"label": "stock_data - 副本 (3)", "value": "stock_data - 副本 (3)"}
                            ],
                            value="stock_data",
                            placeholder="选择数据目录..."
                        )
                    ], width=6)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Button("开始分析", id="start-analysis-btn", color="primary", className="me-2"),
                        dbc.Button("导出结果", id="export-analysis-btn", color="success", className="me-2"),
                        html.Div(id="analysis-progress", className="mt-2")
                    ])
                ])
            ])
        ])
    ], className="mb-4"),
    
    # 分析结果表格
    dbc.Row([
        dbc.Col([
            html.H5("选股分析结果", className="mb-3"),
            dash_table.DataTable(
                id="analysis-table",
                columns=[
                    {"name": "策略", "id": "策略"},
                    {"name": "日期", "id": "日期"},
                    {"name": "股票", "id": "股票"},
                    {"name": "买入日期", "id": "买入日期"},
                    {"name": "评分", "id": "评分", "type": "numeric"},
                    {"name": "评级", "id": "评级"},
                    # A点信息
                    {"name": "A点日期", "id": "A点日期"},
                    {"name": "A点开盘", "id": "A点开盘", "type": "numeric"},
                    {"name": "A点收盘", "id": "A点收盘", "type": "numeric"},
                    {"name": "A点最高", "id": "A点最高", "type": "numeric"},
                    {"name": "A点最低", "id": "A点最低", "type": "numeric"},
                    {"name": "A点成交量", "id": "A点成交量", "type": "numeric"},
                    {"name": "A点实体涨跌幅", "id": "A点实体涨跌幅"},
                    {"name": "A点价格振幅", "id": "A点价格振幅"},
                    # B点信息
                    {"name": "B点日期", "id": "B点日期"},
                    {"name": "B点开盘", "id": "B点开盘", "type": "numeric"},
                    {"name": "B点收盘", "id": "B点收盘", "type": "numeric"},
                    {"name": "B点最高", "id": "B点最高", "type": "numeric"},
                    {"name": "B点最低", "id": "B点最低", "type": "numeric"},
                    {"name": "B点成交量", "id": "B点成交量", "type": "numeric"},
                    {"name": "B点实体涨跌幅", "id": "B点实体涨跌幅"},
                    {"name": "B点价格振幅", "id": "B点价格振幅"},
                    # C点信息
                    {"name": "C点日期", "id": "C点日期"},
                    {"name": "C点开盘", "id": "C点开盘", "type": "numeric"},
                    {"name": "C点收盘", "id": "C点收盘", "type": "numeric"},
                    {"name": "C点最高", "id": "C点最高", "type": "numeric"},
                    {"name": "C点最低", "id": "C点最低", "type": "numeric"},
                    {"name": "C点成交量", "id": "C点成交量", "type": "numeric"},
                    {"name": "C点实体涨跌幅", "id": "C点实体涨跌幅"},
                    {"name": "C点价格振幅", "id": "C点价格振幅"},
                    # D点信息
                    {"name": "D点日期", "id": "D点日期"},
                    {"name": "D点开盘", "id": "D点开盘", "type": "numeric"},
                    {"name": "D点收盘", "id": "D点收盘", "type": "numeric"},
                    {"name": "D点最高", "id": "D点最高", "type": "numeric"},
                    {"name": "D点最低", "id": "D点最低", "type": "numeric"},
                    {"name": "D点成交量", "id": "D点成交量", "type": "numeric"},
                    {"name": "D点实体涨跌幅", "id": "D点实体涨跌幅"},
                    {"name": "D点价格振幅", "id": "D点价格振幅"},
                    # E点信息
                    {"name": "E点日期", "id": "E点日期"},
                    {"name": "E点开盘", "id": "E点开盘", "type": "numeric"},
                    {"name": "E点收盘", "id": "E点收盘", "type": "numeric"},
                    {"name": "E点最高", "id": "E点最高", "type": "numeric"},
                    {"name": "E点最低", "id": "E点最低", "type": "numeric"},
                    {"name": "E点成交量", "id": "E点成交量", "type": "numeric"},
                    {"name": "E点实体涨跌幅", "id": "E点实体涨跌幅"},
                    {"name": "E点价格振幅", "id": "E点价格振幅"},
                    # 区间信息
                    {"name": "A-B涨幅", "id": "A-B涨幅"},
                    {"name": "A-B天数", "id": "A-B天数", "type": "numeric"},
                    {"name": "B-C跌幅", "id": "B-C跌幅"},
                    {"name": "B-C天数", "id": "B-C天数", "type": "numeric"},
                    {"name": "C-D涨幅", "id": "C-D涨幅"},
                    {"name": "C-D天数", "id": "C-D天数", "type": "numeric"},
                    {"name": "D-E涨幅", "id": "D-E涨幅"},
                    {"name": "D-E天数", "id": "D-E天数", "type": "numeric"},
                    # 成交量比例信息
                    {"name": "D点成交量/C-D均量", "id": "D点成交量/C-D均量", "type": "numeric"},
                    {"name": "D点上影线涨幅", "id": "D点上影线涨幅", "type": "numeric"},
                    {"name": "D点上影线/实体", "id": "D点上影线/实体", "type": "numeric"},
                    {"name": "E点成交量/C-D均量", "id": "E点成交量/C-D均量", "type": "numeric"},
                    {"name": "E点成交量/D点成交量", "id": "E点成交量/D点成交量", "type": "numeric"},
                    # 未来收益率
                    {"name": "3日最大涨幅", "id": "3日最大涨幅"},
                    {"name": "3日最大跌幅", "id": "3日最大跌幅"},
                    {"name": "5日最大涨幅", "id": "5日最大涨幅"},
                    {"name": "5日最大跌幅", "id": "5日最大跌幅"},
                    {"name": "10日最大涨幅", "id": "10日最大涨幅"},
                    {"name": "10日最大跌幅", "id": "10日最大跌幅"},
                    # 风险提示
                    {"name": "风险提示", "id": "风险提示"}
                ],
                data=[],
                style_table={'overflowX': 'auto', 'overflowY': 'auto', 'maxHeight': '600px'},
                style_cell={
                    'textAlign': 'center',
                    'minWidth': '80px',
                    'maxWidth': '120px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'fontSize': '12px',
                    'padding': '8px'
                },
                style_cell_conditional=[
                    # 基本信息列 - 较窄
                    {'if': {'column_id': '策略'}, 'minWidth': '120px', 'maxWidth': '150px'},
                    {'if': {'column_id': '日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '股票'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': '买入日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '评分'}, 'minWidth': '60px', 'maxWidth': '80px'},
                    {'if': {'column_id': '评级'}, 'minWidth': '60px', 'maxWidth': '80px'},
                    # 日期列 - 较窄
                    {'if': {'column_id': 'A点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点日期'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 价格列 - 中等宽度
                    {'if': {'column_id': 'A点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点开盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点收盘'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点最高'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'E点最低'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    # 成交量列 - 较宽
                    {'if': {'column_id': 'A点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点成交量'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 百分比列 - 中等宽度
                    {'if': {'column_id': 'A点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'A点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'B点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'C点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点实体涨跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点价格振幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 区间信息列 - 中等宽度
                    {'if': {'column_id': 'A-B涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'A-B天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'B-C跌幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'B-C天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'C-D涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'C-D天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    {'if': {'column_id': 'D-E涨幅'}, 'minWidth': '80px', 'maxWidth': '100px'},
                    {'if': {'column_id': 'D-E天数'}, 'minWidth': '70px', 'maxWidth': '90px'},
                    # 成交量比例列 - 较宽
                    {'if': {'column_id': 'D点成交量/C-D均量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    {'if': {'column_id': 'D点上影线涨幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'D点上影线/实体'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': 'E点成交量/C-D均量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    {'if': {'column_id': 'E点成交量/D点成交量'}, 'minWidth': '120px', 'maxWidth': '140px'},
                    # 未来收益率列 - 中等宽度
                    {'if': {'column_id': '3日最大涨幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '3日最大跌幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '5日最大涨幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '5日最大跌幅'}, 'minWidth': '90px', 'maxWidth': '110px'},
                    {'if': {'column_id': '10日最大涨幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    {'if': {'column_id': '10日最大跌幅'}, 'minWidth': '100px', 'maxWidth': '120px'},
                    # 风险提示列 - 较宽
                    {'if': {'column_id': '风险提示'}, 'minWidth': '150px', 'maxWidth': '200px'}
                ],
                page_size=20,
                sort_action='native',
                filter_action='native',
                sort_mode='multi',
                column_selectable=False,
                row_selectable=False,
                style_as_list_view=True,
                css=[{
                    'selector': '.dash-table-container',
                    'rule': 'font-family: "Microsoft YaHei", Arial, sans-serif;'
                }],
                style_header={
                    'backgroundColor': '#f8f9fa',
                    'fontWeight': 'bold',
                    'textAlign': 'center',
                    'fontSize': '11px',
                    'padding': '6px'
                },
                style_data_conditional=[
                    {
                        'if': {'column_id': '评级', 'filter_query': '{评级} = A+'},
                        'backgroundColor': '#d4edda',
                        'color': '#155724'
                    },
                    {
                        'if': {'column_id': '评级', 'filter_query': '{评级} = A'},
                        'backgroundColor': '#d1ecf1',
                        'color': '#0c5460'
                    },
                    {
                        'if': {'column_id': '评级', 'filter_query': '{评级} = B+'},
                        'backgroundColor': '#fff3cd',
                        'color': '#856404'
                    },
                    {
                        'if': {'column_id': '评级', 'filter_query': '{评级} = B'},
                        'backgroundColor': '#f8d7da',
                        'color': '#721c24'
                    }
                ]
            )
        ])
    ]),
    
    # 统计信息
    dbc.Row([
        dbc.Col([
            html.H5("统计信息", className="mb-3"),
            html.Div(id="analysis-stats")
        ])
    ])
], fluid=True)

# 应用布局
app.layout = dbc.Container([
    dbc.NavbarSimple(
        brand="选股助手",
        brand_href="#",
        color="primary",
        dark=True,
    ),
    dbc.Tabs([
        dbc.Tab(strategy_page, label="策略选股", tab_id="strategy"),
        dbc.Tab(analysis_page, label="选股分析", tab_id="analysis")
    ], id="tabs", active_tab="strategy"),
    dcc.Interval(id="strategy-interval", interval=1000, disabled=True),
], fluid=True)

# 选股参数表单刷新
@app.callback(
    Output("strategy-forms-container", "children"),
    [Input("run-strategy-btn", "n_clicks")],
    prevent_initial_call=False
)
def refresh_strategy_forms(n):
    if os.path.exists("configs.json"):
        with open("configs.json", "r", encoding="utf-8") as f:
            config = json.load(f)
    else:
        config = default_config
    return generate_strategy_forms(config)

# 监听策略选择变化并更新显示
@app.callback(
    Output("selected-strategies-display", "children"),
    [Input({"type": "activate-checkbox", "idx": ALL}, "value")],
    [State("strategy-forms-container", "children")],
    prevent_initial_call=False
)
def update_selected_strategies_display(activate_list, strategy_forms):
    if not activate_list or not strategy_forms:
        return html.Div("请选择要使用的策略", style={"color": "#6c757d", "fontStyle": "italic"})
    
    # 从策略表单中提取策略名称
    selected_strategies = []
    for idx, is_activated in enumerate(activate_list):
        if is_activated:
            # 从策略表单的children中提取策略名称
            if strategy_forms and len(strategy_forms) > 0:
                strategy_row = strategy_forms[0]
                if hasattr(strategy_row, 'children') and len(strategy_row.children) > idx:
                    strategy_col = strategy_row.children[idx]
                    if hasattr(strategy_col, 'children') and len(strategy_col.children) > 0:
                        strategy_card = strategy_col.children[0]
                        if hasattr(strategy_card, 'children') and len(strategy_card.children) > 0:
                            strategy_header = strategy_card.children[0]
                            if hasattr(strategy_header, 'children') and len(strategy_header.children) > 1:
                                strategy_name = strategy_header.children[1].children
                                selected_strategies.append(strategy_name)
    
    if not selected_strategies:
        return html.Div("请选择要使用的策略", style={"color": "#6c757d", "fontStyle": "italic"})
    
    # 显示已选择的策略
    strategy_tags = []
    for strategy in selected_strategies:
        strategy_tags.append(
            dbc.Badge(strategy, color="primary", className="me-2 mb-1", style={"fontSize": "14px"})
        )
    
    return html.Div([
        html.Strong("已选择的策略：", style={"color": "#495057"}),
        html.Br(),
        html.Div(strategy_tags)
    ])

# 股票多选下拉选项自动填充
@app.callback(
    Output("strategy-tickers-dropdown", "options"),
    [Input("strategy-data-dir", "value")]
)
def update_ticker_options(data_dir):
    import os
    options = []
    if os.path.exists(data_dir):
        for f in os.listdir(data_dir):
            if f.endswith(".csv"):
                code = f.replace(".csv", "")
                options.append({"label": code, "value": code})
    return options

# 日期范围自动填充（取所有股票的最大最小日期）
@app.callback(
    [Output("strategy-date-range", "min_date_allowed"), Output("strategy-date-range", "max_date_allowed"), Output("strategy-date-range", "start_date"), Output("strategy-date-range", "end_date")],
    [Input("strategy-tickers-dropdown", "value"), Input("strategy-data-dir", "value")]
)
def update_date_range(tickers, data_dir):
    import os
    import pandas as pd
    min_date, max_date = None, None
    if tickers and os.path.exists(data_dir):
        for code in tickers:
            fp = os.path.join(data_dir, f"{code}.csv")
            if os.path.exists(fp):
                df = pd.read_csv(fp, parse_dates=["date"])
                dmin, dmax = df["date"].min(), df["date"].max()
                if min_date is None or dmin < min_date:
                    min_date = dmin
                if max_date is None or dmax > max_date:
                    max_date = dmax
    if min_date is not None and max_date is not None:
        return min_date, max_date, min_date, max_date
    return None, None, None, None

# 进度状态Store结构已在页面定义

# === 后台线程批量计算函数 ===
def strategy_calc_worker(progress_data, data_dir):
    global strategy_result_buffer, strategy_calc_running, strategy_calc_should_continue
    import os, pandas as pd
    from select_stock import multi_strategy_select
    from collections import Counter, defaultdict
    strategy_result_buffer.clear()
    stock_codes = progress_data["stock_codes"]
    date_strs = progress_data["date_strs"]
    selectors = progress_data["selectors"]
    buy_price_type = progress_data.get("buy_price_type", "open")
    judge_down = float(progress_data.get("judge_down", -3))
    judge_up = float(progress_data.get("judge_up", 5))
    judge_days = int(progress_data.get("judge_days", 10))
    stop_loss = float(progress_data.get("stop_loss", -3))
    take_profit = float(progress_data.get("take_profit", 5))
    arbitrage_stop_loss = float(progress_data.get("arbitrage_stop_loss", -3))
    arbitrage_take_profit = float(progress_data.get("arbitrage_take_profit", 2))
    arbitrage_success_threshold = float(progress_data.get("arbitrage_success_threshold", 1))
    results = []
    log_lines = []
    total = len(stock_codes) * len(date_strs)
    global _selector_instances
    _selector_instances = {}
    current_stock_idx = progress_data.get('current_stock_idx', 0)
    current_day_idx = progress_data.get('current_day_idx', 0)
    step_count = 0
    for stock_idx in range(current_stock_idx, len(stock_codes)):
        code = stock_codes[stock_idx]
        if not strategy_calc_running:
            break
        fp = os.path.join(data_dir, f"{code}.csv")
        if not os.path.exists(fp):
            continue
        df = pd.read_csv(fp, parse_dates=["date"])
        start_day_idx = current_day_idx if stock_idx == current_stock_idx else 0
        for day_idx in range(start_day_idx, len(date_strs)):
            if not strategy_calc_running:
                break
            date = date_strs[day_idx]
            for sel in selectors:
                if not sel.get("activate", True):
                    continue
                try:
                    selector_key = f"{sel['class']}_{sel['alias']}"
                    if selector_key not in _selector_instances:
                        import importlib
                        module = importlib.import_module("Selector")
                        cls = getattr(module, sel["class"])
                        _selector_instances[selector_key] = cls(**sel.get("params", {}))
                    selector = _selector_instances[selector_key]
                    picks = selector.select(pd.to_datetime(date), {code: df})
                    selected = code in picks
                except Exception as e:
                    selected = False
                if not selected:
                    continue
                d = pd.to_datetime(date)
                next_row = df[df["date"] > d].head(1)
                after_next_row = df[df["date"] > d].head(2).tail(1)
                prev_row = df[df["date"] == d]
                if not next_row.empty:
                    if buy_price_type == "open":
                        buy_price = next_row.iloc[0]["open"] if "open" in next_row.columns else (next_row.iloc[0]["开盘价"] if "开盘价" in next_row.columns else None)
                    else:
                        buy_price = next_row.iloc[0]["close"] if "close" in next_row.columns else (next_row.iloc[0]["收盘价"] if "收盘价" in next_row.columns else None)
                    buy_date = next_row.iloc[0]["date"].strftime("%Y-%m-%d") if "date" in next_row.columns else None
                else:
                    buy_price = None
                    buy_date = None
                if buy_date:
                    buy_date_dt = pd.to_datetime(buy_date)
                    future_rows = df[df["date"] > buy_date_dt].head(10)
                else:
                    future_rows = df[df["date"] > d].head(10)
                def get_high_low_str(rows, n):
                    if rows.empty:
                        return None, None
                    high = rows.head(n)["high"].max() if "high" in rows.columns else rows.head(n)["最高价"].max()
                    low = rows.head(n)["low"].min() if "low" in rows.columns else rows.head(n)["最低价"].min()
                    return high, low
                def get_pct(val, base):
                    try:
                        return round((val - base) / base * 100, 2) if val is not None and base else None
                    except Exception as e:
                        return None
                high3, low3 = get_high_low_str(future_rows, 3)
                high5, low5 = get_high_low_str(future_rows, 5)
                high10, low10 = get_high_low_str(future_rows, 10)
                pct_up3 = get_pct(high3, buy_price)
                pct_down3 = get_pct(low3, buy_price)
                pct_up5 = get_pct(high5, buy_price)
                pct_down5 = get_pct(low5, buy_price)
                pct_up10 = get_pct(high10, buy_price)
                pct_down10 = get_pct(low10, buy_price)
                tail_result = "-"
                tail_profit = "-"
                if not after_next_row.empty and buy_price is not None:
                    tail_buy_price = next_row.iloc[0]["close"] if "close" in next_row.columns else (next_row.iloc[0]["收盘价"] if "收盘价" in next_row.columns else None)
                    next_high = after_next_row.iloc[0]["high"] if "high" in after_next_row.columns else (after_next_row.iloc[0]["最高价"] if "最高价" in after_next_row.columns else None)
                    next_low = after_next_row.iloc[0]["low"] if "low" in after_next_row.columns else (after_next_row.iloc[0]["最低价"] if "最低价" in after_next_row.columns else None)
                    next_close = after_next_row.iloc[0]["close"] if "close" in after_next_row.columns else (after_next_row.iloc[0]["收盘价"] if "收盘价" in after_next_row.columns else None)
                    if tail_buy_price is not None and next_high is not None and next_low is not None and next_close is not None:
                        max_gain_pct = round((next_high - tail_buy_price) / tail_buy_price * 100, 2)
                        max_loss_pct = round((next_low - tail_buy_price) / tail_buy_price * 100, 2)
                        if max_loss_pct <= arbitrage_stop_loss:
                            profit = arbitrage_stop_loss
                            tail_result = "套利失败(止损)"
                            tail_profit = f"{profit}%"
                        elif max_loss_pct > arbitrage_stop_loss and max_gain_pct < arbitrage_take_profit:
                            profit = round((next_close - tail_buy_price) / tail_buy_price * 100, 2)
                            if profit > arbitrage_success_threshold:
                                tail_result = "套利成功(尾盘)"
                                tail_profit = f"+{profit}%"
                            else:
                                tail_result = "套利失败(尾盘)"
                                tail_profit = f"{profit}%"
                        elif max_loss_pct > arbitrage_stop_loss and max_gain_pct >= arbitrage_take_profit:
                            profit = arbitrage_take_profit
                            tail_result = "套利成功(止盈)"
                            tail_profit = f"+{profit}%"
                        else:
                            tail_result = "套利失败"
                            tail_profit = "-"
                sim_profit = "-"
                if buy_price is not None:
                    sim_sell_price = None
                    sim_sell_day = None
                    for i, rowf in enumerate(future_rows.itertuples(), 1):
                        high = getattr(rowf, "high", getattr(rowf, "最高价", None))
                        low = getattr(rowf, "low", getattr(rowf, "最低价", None))
                        if high is not None and (high - buy_price) / buy_price * 100 >= take_profit:
                            sim_sell_price = high
                            sim_sell_day = i
                            break
                        if low is not None and (low - buy_price) / buy_price * 100 <= stop_loss:
                            sim_sell_price = low
                            sim_sell_day = i
                            break
                    if sim_sell_price is None and not future_rows.empty:
                        last_row = future_rows.iloc[-1]
                        sim_sell_price = last_row["close"] if "close" in last_row else (last_row["收盘价"] if "收盘价" in last_row else None)
                    if sim_sell_price is not None:
                        sim_profit = f"{round((sim_sell_price - buy_price) / buy_price * 100, 2)}%"
                def fmt_pct(val, is_up):
                    if val is None:
                        return "-"
                    if is_up:
                        return f"{'+' if val >= 0 else ''}{val}%"
                    else:
                        if val >= 0:
                            return f"+{val}%"
                        else:
                            return f"{val}%"
                high3_str = f"{high3}" if high3 is not None else "-"
                low3_str = f"{low3}" if low3 is not None else "-"
                high5_str = f"{high5}" if high5 is not None else "-"
                low5_str = f"{low5}" if low5 is not None else "-"
                high10_str = f"{high10}" if high10 is not None else "-"
                low10_str = f"{low10}" if low10 is not None else "-"
                high_judge, low_judge = get_high_low_str(future_rows, judge_days)
                pct_up_judge = get_pct(high_judge, buy_price)
                pct_down_judge = get_pct(low_judge, buy_price)
                judge = "成功选股" if pct_down_judge is not None and pct_down_judge > judge_down and pct_up_judge is not None and pct_up_judge > judge_up else "普通/失败选股"
                row = {"策略": sel["alias"], "日期": date, "买入日期": buy_date, "股票": code, "买入价": buy_price,
                       "3日最高价": high3_str, "3日最大涨幅%": fmt_pct(pct_up3, True),
                       "3日最低价": low3_str, "3日最大跌幅%": fmt_pct(pct_down3, False),
                       "5日最高价": high5_str, "5日最大涨幅%": fmt_pct(pct_up5, True),
                       "5日最低价": low5_str, "5日最大跌幅%": fmt_pct(pct_down5, False),
                       "10日最高价": high10_str, "10日最大涨幅%": fmt_pct(pct_up10, True),
                       "10日最低价": low10_str, "10日最大跌幅%": fmt_pct(pct_down10, False),
                       "尾盘套利结果": tail_result, "尾盘套利盈利%": tail_profit, "选股评价": judge, "止盈止损模拟收益": sim_profit}
                results.append(row)
            # 步进计数
            step_count += 1
            # 每BUFFER_STEP_SIZE步写入一次缓冲区并更新进度
            if step_count % BUFFER_STEP_SIZE == 0:
                with strategy_result_lock:
                    strategy_result_buffer.clear()
                    strategy_result_buffer.extend(results)
                # 更新进度缓存
                progress_data['current_stock_idx'] = stock_idx
                progress_data['current_day_idx'] = day_idx + 1
                interval_strategy_callback.progress_data_cache = progress_data
                # 等待interval唤醒
                strategy_calc_should_continue.clear()
                strategy_calc_should_continue.wait()
    # 处理完全部，写入最终结果
    with strategy_result_lock:
        strategy_result_buffer.clear()
        strategy_result_buffer.extend(results)
    progress_data['current_stock_idx'] = len(stock_codes)
    progress_data['current_day_idx'] = 0
    interval_strategy_callback.progress_data_cache = progress_data
    strategy_calc_running = False

# === 启动选股按钮回调 ===
@app.callback(
    [Output("strategy-progress", "data", allow_duplicate=True),
     Output("strategy-result-output", "children", allow_duplicate=True),
     Output("progress-info", "children", allow_duplicate=True),
     Output("strategy-log-output", "children", allow_duplicate=True),
     Output("run-strategy-btn", "disabled", allow_duplicate=True),
     Output("stop-strategy-btn", "disabled", allow_duplicate=True),
     Output("strategy-interval", "disabled", allow_duplicate=True)],
    [Input("run-strategy-btn", "n_clicks")],
    [State("strategy-data-dir", "value"),
     State("strategy-config", "value"),
        State("strategy-tickers-dropdown", "value"),
     State("strategy-date-range", "start_date"),
     State("strategy-date-range", "end_date"),
        State({"type": "activate-checkbox", "idx": ALL}, "value"),
        State({"type": "param-input", "idx": ALL, "param": ALL}, "value"),
        State({"type": "param-input", "idx": ALL, "param": ALL}, "id"),
     State("buy-price-type", "value"),
     State("judge-days", "value"),
     State("stop-loss-threshold", "value"),
     State("take-profit-threshold", "value"),
     State("arbitrage-stop-loss", "value"),
     State("arbitrage-take-profit", "value"),
     State("arbitrage-success-threshold", "value")],
    prevent_initial_call=True
)
def start_strategy_callback(n_clicks, data_dir, config_file, tickers, start_date, end_date, activate_list, param_values, param_ids, buy_price_type, judge_days, stop_loss, take_profit, arbitrage_stop_loss, arbitrage_take_profit, arbitrage_success_threshold):
    from dash import no_update
    global strategy_calc_thread, strategy_calc_running
    if not n_clicks:
        return no_update, no_update, no_update, no_update, no_update, no_update, no_update
    import os, pandas as pd
    from collections import defaultdict
    param_dict = defaultdict(dict)
    for v, idd in zip(param_values, param_ids):
        param_dict[idd["idx"]][idd["param"]] = v
    selectors = []
    if os.path.exists(config_file):
        with open(config_file, "r", encoding="utf-8") as f:
            config = json.load(f)
    else:
        config = load_default_config()
    for idx, sel in enumerate(config.get("selectors", [])):
        is_activated = True
        if idx < len(activate_list):
            activate_value = activate_list[idx]
            is_activated = activate_value is not None and activate_value
        selectors.append({
            "class": sel["class"],
            "alias": sel.get("alias", sel["class"]),
            "activate": is_activated,
            "params": param_dict[idx]
        })
    stock_data = {}
    if (not tickers or len(tickers) == 0) and os.path.exists(data_dir):
        tickers = [f.replace('.csv', '') for f in os.listdir(data_dir) if f.endswith('.csv')]
    if (not start_date or not end_date) and tickers and os.path.exists(data_dir):
        all_dates = []
        for code in tickers:
            fp = os.path.join(data_dir, f"{code}.csv")
            if os.path.exists(fp):
                df = pd.read_csv(fp, parse_dates=["date"])
                all_dates.append(df["date"])
        if all_dates:
            all_dates = pd.concat(all_dates)
            if not start_date:
                start_date = all_dates.min()
            if not end_date:
                end_date = all_dates.max()
    if start_date and end_date:
        date_list = pd.date_range(start=start_date, end=end_date, freq="B")
    else:
        date_list = []
    progress_data = {
        "current_stock_idx": 0,
        "current_day_idx": 0,
        "total_stocks": len(tickers or []),
        "total_days": len(date_list),
        "results": [],
        "log_lines": [],
        "is_running": True,
        "selectors": selectors,
        "date_strs": [d.strftime("%Y-%m-%d") for d in date_list],
        "data_dir": data_dir,
        "stock_codes": tickers or [],
        "stock_data": None,
        "buy_price_type": buy_price_type,
        "judge_down": -3,
        "judge_up": 5,
        "judge_days": judge_days,
        "stop_loss": stop_loss,
        "take_profit": take_profit,
        "arbitrage_stop_loss": arbitrage_stop_loss,
        "arbitrage_take_profit": arbitrage_take_profit,
        "arbitrage_success_threshold": arbitrage_success_threshold
    }
    # 缓存进度数据供interval回调用
    interval_strategy_callback.progress_data_cache = progress_data
    # 启动后台线程
    strategy_calc_running = True
    strategy_calc_thread = threading.Thread(target=strategy_calc_worker, args=(progress_data, data_dir))
    strategy_calc_thread.start()
    info = f"已完成0/{(len(tickers or []) * len(date_list))}"
    return progress_data, no_update, info, "", True, False, False

# === interval定时刷新回调 ===
@app.callback(
    [Output("strategy-result-output", "children", allow_duplicate=True),
     Output("progress-info", "children", allow_duplicate=True),
     Output("strategy-log-output", "children", allow_duplicate=True),
     Output("run-strategy-btn", "disabled", allow_duplicate=True),
     Output("stop-strategy-btn", "disabled", allow_duplicate=True),
     Output("strategy-interval", "disabled", allow_duplicate=True),
     Output("strategy-stats-output", "children", allow_duplicate=True)],
    [Input("strategy-interval", "n_intervals")],
    prevent_initial_call=True
)
def interval_strategy_callback(n_intervals):
    import pandas as pd
    from dash import dash_table, html
    from collections import Counter, defaultdict
    global strategy_result_buffer, strategy_calc_running, strategy_result_lock, strategy_calc_should_continue
    with strategy_result_lock:
        results = list(strategy_result_buffer)
    # 唤醒线程推进下一批
    if strategy_calc_running:
        strategy_calc_should_continue.set()
    # 获取全部天数（股票数*日期数）
    all_days = 0
    judged_days = 0
    # 需要获取全部股票和日期数
    if hasattr(interval_strategy_callback, 'progress_data_cache'):
        progress_data = interval_strategy_callback.progress_data_cache
        if progress_data:
            all_days = len(progress_data.get('stock_codes', [])) * len(progress_data.get('date_strs', []))
            judged_days = progress_data.get('current_stock_idx', 0) * len(progress_data.get('date_strs', [])) + progress_data.get('current_day_idx', 0)
    else:
        all_days = 0
        judged_days = 0
    info = f"已完成{judged_days}/{all_days if all_days else '?'}天"
    # 结果表格
    if results:
        df_result = pd.DataFrame(results)
        df_result = df_result.sort_values(by="日期", ascending=True)
        style_data_conditional = []
        for col in ["3日最大涨幅%", "5日最大涨幅%", "10日最大涨幅%"]:
            style_data_conditional.append({
                "if": {"column_id": col, "filter_query": f'{{{col}}} contains "+"'},
                "color": "red",
                "fontWeight": "bold"
            })
        for col in ["3日最大跌幅%", "5日最大跌幅%", "10日最大跌幅%"]:
            style_data_conditional.append({
                "if": {"column_id": col, "filter_query": f'{{{col}}} contains "-"'},
                "color": "green",
                "fontWeight": "bold"
            })
        style_data_conditional.append({
            "if": {"column_id": "尾盘套利盈利%", "filter_query": '{尾盘套利盈利%} contains "+"'},
            "color": "red",
            "fontWeight": "bold"
        })
        style_data_conditional.append({
            "if": {"column_id": "尾盘套利盈利%", "filter_query": '{尾盘套利盈利%} contains "-"'},
            "color": "green",
            "fontWeight": "bold"
        })
        table_comp = dash_table.DataTable(
            columns=[{"name": i, "id": i} for i in df_result.columns],
            data=df_result.to_dict("records"),
            style_table={"overflowX": "auto"},
            style_cell={"fontSize": "14px", "textAlign": "center"},
            style_data_conditional=style_data_conditional,
            page_size=20
        )
    else:
        table_comp = html.Div("无选股结果")
    # 统计
    strat_counter = Counter()
    strat_success = Counter()
    strat_profit_sum = defaultdict(float)
    strat_profit_count = Counter()
    strat_arbitrage_total = defaultdict(int)
    strat_arbitrage_success = defaultdict(int)
    strat_arbitrage_profit_sum = defaultdict(float)
    strat_arbitrage_profit_count = defaultdict(int)
    for r in results:
        strat_counter[r["策略"]] += 1
        if r["选股评价"] == "成功选股":
            strat_success[r["策略"]] += 1
        try:
            profit_val = float(str(r["止盈止损模拟收益"]).replace("%", ""))
            strat_profit_sum[r["策略"]] += profit_val
            strat_profit_count[r["策略"]] += 1
        except ValueError:
            pass
        strat_arbitrage_total[r["策略"]] += 1
        tail_result = r.get("尾盘套利结果", "")
        if "套利成功" in tail_result:
            strat_arbitrage_success[r["策略"]] += 1
        try:
            tail_profit_str = str(r.get("尾盘套利盈利%", "0%")).replace("%", "").replace("+", "")
            if tail_profit_str != "-" and tail_profit_str:
                tail_profit_val = float(tail_profit_str)
                strat_arbitrage_profit_sum[r["策略"]] += tail_profit_val
                strat_arbitrage_profit_count[r["策略"]] += 1
        except ValueError:
            pass
    stats_data = []
    for k in sorted(strat_counter.keys()):
        winrate = strat_success[k] / strat_counter[k] * 100 if strat_counter[k] else 0
        avg_profit = strat_profit_sum[k] / strat_profit_count[k] if strat_profit_count[k] else 0
        arbitrage_winrate = strat_arbitrage_success[k] / strat_arbitrage_total[k] * 100 if strat_arbitrage_total[k] else 0
        arbitrage_avg_profit = strat_arbitrage_profit_sum[k] / strat_arbitrage_profit_count[k] if strat_arbitrage_profit_count[k] else 0
        stats_data.append({
            "策略": k,
            "选股数": strat_counter[k],
            "成功数": strat_success[k],
            "胜率%": f"{winrate:.2f}",
            "平均止盈止损收益%": f"{avg_profit:.2f}",
            "套利胜率%": f"{arbitrage_winrate:.2f}",
            "平均套利收益%": f"{arbitrage_avg_profit:.2f}"
        })
    if stats_data:
        df_stats = pd.DataFrame(stats_data)
        stats_table_comp = dash_table.DataTable(
            columns=[{"name": i, "id": i} for i in df_stats.columns],
            data=df_stats.to_dict("records"),
            style_table={"overflowX": "auto"},
            style_cell={"fontSize": "14px", "textAlign": "center"},
            page_size=20
        )
    else:
        stats_table_comp = html.Div("无策略统计结果")
    log_content = f"共{len(results)}条结果。"
    # 按线程状态控制按钮
    if not strategy_calc_running:
        return table_comp, info, log_content, False, True, True, stats_table_comp
    return table_comp, info, log_content, True, False, False, stats_table_comp

# “停止选股”按钮回调
@app.callback(
    Output("strategy-progress", "data", allow_duplicate=True),
    [Input("stop-strategy-btn", "n_clicks")],
    [State("strategy-progress", "data")],
    prevent_initial_call=True
)
def stop_strategy_callback(n, progress_data):
    global strategy_calc_running
    if not n:
        return progress_data
    strategy_calc_running = False
    return progress_data

# 获取选股结果回调
@app.callback(
    Output("strategy-results-display", "children"),
    [Input("get-strategy-results-btn", "n_clicks")],
    [State("strategy-result-output", "children")],
    prevent_initial_call=True
)
def get_strategy_results_callback(n_clicks, strategy_results):
    if not n_clicks:
        return []
    
    if not strategy_results or not isinstance(strategy_results, list):
        return []
    
    return strategy_results

# === 选股分析相关回调函数 ===

@app.callback(
    Output("log-file-dropdown", "options"),
    [Input("analysis-data-dir", "value")]
)
def update_log_file_options(data_dir):
    """更新日志文件选项"""
    import os
    log_files = []
    logs_dir = "logs"
    
    if os.path.exists(logs_dir):
        for file in os.listdir(logs_dir):
            if file.endswith('.log') and 'JZVolumeShrinkSelector' in file:
                log_files.append({
                    "label": file,
                    "value": os.path.join(logs_dir, file)
                })
    
    return log_files

def parse_log_file(log_file_path):
    """解析日志文件，提取选股结果"""
    import re
    import pandas as pd
    from datetime import datetime
    
    results = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分割每个选股记录
        sections = content.split('=== 详细参数信息 ===')
        
        for section in sections[1:]:  # 跳过第一个空部分
            try:
                # 提取股票代码
                code_match = re.search(r'股票代码: (\d+)', section)
                if not code_match:
                    continue
                code = code_match.group(1)
                
                # 提取日期信息
                date_match = re.search(r'选出E点:.*?日期=(\d{4}-\d{2}-\d{2})', section)
                if not date_match:
                    continue
                date = date_match.group(1)
                
                # 提取A点信息
                a_match = re.search(r'A点: 日期=(\d{4}-\d{2}-\d{2}).*?开盘=([\d.]+).*?收盘=([\d.]+).*?最高=([\d.]+).*?最低=([\d.]+).*?成交量=([\d.]+).*?实体涨跌幅=([\d.]+).*?价格振幅=([\d.]+)', section, re.DOTALL)
                if not a_match:
                    continue
                
                # 提取B点信息
                b_match = re.search(r'B点: 日期=(\d{4}-\d{2}-\d{2}).*?开盘=([\d.]+).*?收盘=([\d.]+).*?最高=([\d.]+).*?最低=([\d.]+).*?成交量=([\d.]+).*?实体涨跌幅=([\d.]+).*?价格振幅=([\d.]+)', section, re.DOTALL)
                if not b_match:
                    continue
                
                # 提取C点信息
                c_match = re.search(r'C点: 日期=(\d{4}-\d{2}-\d{2}).*?开盘=([\d.]+).*?收盘=([\d.]+).*?最高=([\d.]+).*?最低=([\d.]+).*?成交量=([\d.]+).*?实体涨跌幅=([\d.]+).*?价格振幅=([\d.]+)', section, re.DOTALL)
                if not c_match:
                    continue
                
                # 提取D点信息
                d_match = re.search(r'D点: 日期=(\d{4}-\d{2}-\d{2}).*?开盘=([\d.]+).*?收盘=([\d.]+).*?最高=([\d.]+).*?最低=([\d.]+).*?成交量=([\d.]+).*?实体涨跌幅=([\d.]+).*?价格振幅=([\d.]+)', section, re.DOTALL)
                if not d_match:
                    continue
                
                # 提取E点信息
                e_match = re.search(r'E点: 日期=(\d{4}-\d{2}-\d{2}).*?开盘=([\d.]+).*?收盘=([\d.]+).*?最高=([\d.]+).*?最低=([\d.]+).*?成交量=([\d.]+).*?实体涨跌幅=([\d.]+).*?价格振幅=([\d.]+)', section, re.DOTALL)
                if not e_match:
                    continue
                
                # 提取区间信息
                ab_match = re.search(r'A-B区间: 涨幅=([\d.]+), 时间间隔=(\d+)天', section)
                bc_match = re.search(r'B-C区间: 跌幅=([\d.]+), 时间间隔=(\d+)天', section)
                cd_match = re.search(r'C-D区间: 涨幅=([\d.]+), 时间间隔=(\d+)天', section)
                de_match = re.search(r'D-E区间: 涨幅=([\d.-]+), 时间间隔=(\d+)天', section)
                
                # 提取成交量信息
                d_vol_match = re.search(r'D点成交量/C-D均量=([\d.]+)', section)
                e_vol_match = re.search(r'E点成交量/C-D均量=([\d.]+)', section)
                d_shadow_match = re.search(r'D点上影线和实体比例上影线/实体=([\d.]+)', section)
                
                # 构建结果
                result = {
                    'code': code,
                    'date': date,
                    'a_date': a_match.group(1),
                    'a_open': float(a_match.group(2)),
                    'a_close': float(a_match.group(3)),
                    'a_high': float(a_match.group(4)),
                    'a_low': float(a_match.group(5)),
                    'a_vol': float(a_match.group(6)),
                    'a_entity_pct': float(a_match.group(7)),
                    'a_price_amplitude': float(a_match.group(8)),
                    'b_date': b_match.group(1),
                    'b_open': float(b_match.group(2)),
                    'b_close': float(b_match.group(3)),
                    'b_high': float(b_match.group(4)),
                    'b_low': float(b_match.group(5)),
                    'b_vol': float(b_match.group(6)),
                    'b_entity_pct': float(b_match.group(7)),
                    'b_price_amplitude': float(b_match.group(8)),
                    'c_date': c_match.group(1),
                    'c_open': float(c_match.group(2)),
                    'c_close': float(c_match.group(3)),
                    'c_high': float(c_match.group(4)),
                    'c_low': float(c_match.group(5)),
                    'c_vol': float(c_match.group(6)),
                    'c_entity_pct': float(c_match.group(7)),
                    'c_price_amplitude': float(c_match.group(8)),
                    'd_date': d_match.group(1),
                    'd_open': float(d_match.group(2)),
                    'd_close': float(d_match.group(3)),
                    'd_high': float(d_match.group(4)),
                    'd_low': float(d_match.group(5)),
                    'd_vol': float(d_match.group(6)),
                    'd_entity_pct': float(d_match.group(7)),
                    'd_price_amplitude': float(d_match.group(8)),
                    'e_date': e_match.group(1),
                    'e_open': float(e_match.group(2)),
                    'e_close': float(e_match.group(3)),
                    'e_high': float(e_match.group(4)),
                    'e_low': float(e_match.group(5)),
                    'e_vol': float(e_match.group(6)),
                    'e_entity_pct': float(e_match.group(7)),
                    'e_price_amplitude': float(e_match.group(8))
                }
                
                # 添加区间信息
                if ab_match:
                    result['ab_rise'] = float(ab_match.group(1))
                    result['ab_days'] = int(ab_match.group(2))
                if bc_match:
                    result['bc_fall'] = float(bc_match.group(1))
                    result['bc_days'] = int(bc_match.group(2))
                if cd_match:
                    result['cd_rise'] = float(cd_match.group(1))
                    result['cd_days'] = int(cd_match.group(2))
                if de_match:
                    result['de_rise'] = float(de_match.group(1))
                    result['de_days'] = int(de_match.group(2))
                
                # 添加成交量信息
                if d_vol_match:
                    result['d_vol_ratio'] = float(d_vol_match.group(1))
                if e_vol_match:
                    result['e_vol_ratio'] = float(e_vol_match.group(1))
                if d_shadow_match:
                    result['d_shadow_ratio'] = float(d_shadow_match.group(1))
                
                results.append(result)
                
            except Exception as e:
                print(f"解析选股记录时出错: {e}")
                continue
    
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
    
    return results

def calculate_stock_score(result):
    """计算股票评分"""
    # 1. 主升浪强度评分 (0-25分)
    ab_rise = result.get('ab_rise', 0)
    ab_strength_score = min(25, ab_rise * 50)
    
    # 2. 回调深度评分 (0-20分)
    bc_fall = result.get('bc_fall', 0)
    if 0.15 <= bc_fall <= 0.35:
        bc_depth_score = 20
    elif 0.10 <= bc_fall <= 0.45:
        bc_depth_score = 15
    elif 0.05 <= bc_fall <= 0.50:
        bc_depth_score = 10
    else:
        bc_depth_score = 5
    
    # 3. 时间节奏评分 (0-15分)
    time_score = 0
    ab_days = result.get('ab_days', 0)
    bc_days = result.get('bc_days', 0)
    cd_days = result.get('cd_days', 0)
    de_days = result.get('de_days', 0)
    
    if ab_days <= 5: time_score += 4
    if 20 <= bc_days <= 80: time_score += 4
    if cd_days <= 20: time_score += 4
    if de_days <= 5: time_score += 3
    
    # 4. 成交量特征评分 (0-20分)
    volume_score = 0
    d_vol_ratio = result.get('d_vol_ratio', 0)
    e_vol_ratio = result.get('e_vol_ratio', 0)
    
    if 1.2 <= d_vol_ratio <= 2.5: volume_score += 10
    elif 1.0 <= d_vol_ratio <= 3.0: volume_score += 7
    else: volume_score += 3
    
    if e_vol_ratio <= 0.8: volume_score += 10
    elif e_vol_ratio <= 1.0: volume_score += 7
    else: volume_score += 3
    
    # 5. K线形态评分 (0-10分)
    pattern_score = 0
    d_shadow_ratio = result.get('d_shadow_ratio', 0)
    e_entity_pct = abs(result.get('e_close', 0) - result.get('e_open', 0)) / result.get('e_open', 1) if result.get('e_open', 0) != 0 else 0
    
    if d_shadow_ratio >= 2.0: pattern_score += 5
    elif d_shadow_ratio >= 1.5: pattern_score += 3
    else: pattern_score += 1
    
    if e_entity_pct <= 0.01: pattern_score += 5
    elif e_entity_pct <= 0.02: pattern_score += 3
    else: pattern_score += 1
    
    # 6. 价格位置评分 (0-10分)
    ce_rise = (result.get('e_close', 0) - result.get('c_close', 0)) / result.get('c_close', 1) if result.get('c_close', 0) != 0 else 0
    if 0.02 <= ce_rise <= 0.15: position_score = 10
    elif 0.01 <= ce_rise <= 0.20: position_score = 7
    elif 0.00 <= ce_rise <= 0.25: position_score = 4
    else: position_score = 1
    
    # 综合评分
    total_score = ab_strength_score + bc_depth_score + time_score + volume_score + pattern_score + position_score
    
    # 评级
    if total_score >= 85: grade = "A+"
    elif total_score >= 75: grade = "A"
    elif total_score >= 65: grade = "B+"
    elif total_score >= 55: grade = "B"
    elif total_score >= 45: grade = "C+"
    else: grade = "C"
    
    # 风险提示
    risk_warnings = []
    if ab_days > 10: risk_warnings.append("A-B时间过长")
    if bc_days > 100: risk_warnings.append("B-C回调时间过长")
    if d_vol_ratio > 3.0: risk_warnings.append("D点放量过大")
    if e_vol_ratio > 1.2: risk_warnings.append("E点缩量不明显")
    if ce_rise > 0.25: risk_warnings.append("C-E涨幅过大")
    
    return {
        'total_score': total_score,
        'grade': grade,
        'risk_warnings': risk_warnings
    }

def calculate_future_returns(result, data_dir):
    """计算未来收益率"""
    import os
    import pandas as pd
    from datetime import datetime, timedelta
    
    code = result['code']
    date = result['date']
    
    # 读取股票数据
    file_path = os.path.join(data_dir, f"{code}.csv")
    if not os.path.exists(file_path):
        return {}
    
    try:
        df = pd.read_csv(file_path)
        df['date'] = pd.to_datetime(df['date'])
        
        # 找到买入日期（E点后一天）
        buy_date = pd.to_datetime(date) + timedelta(days=1)
        buy_row = df[df['date'] >= buy_date].iloc[0] if not df[df['date'] >= buy_date].empty else None
        
        if buy_row is None:
            return {}
        
        buy_price = buy_row['close']
        
        # 获取未来数据
        future_data = df[df['date'] > buy_date].head(10)
        
        if future_data.empty:
            return {}
        
        # 计算3日、5日、10日最大涨跌幅
        def get_max_returns(data, days):
            if len(data) < days:
                return None, None
            period_data = data.head(days)
            max_high = period_data['high'].max()
            min_low = period_data['low'].min()
            max_return = (max_high - buy_price) / buy_price * 100
            min_return = (min_low - buy_price) / buy_price * 100
            return max_return, min_return
        
        max_3d, min_3d = get_max_returns(future_data, 3)
        max_5d, min_5d = get_max_returns(future_data, 5)
        max_10d, min_10d = get_max_returns(future_data, 10)
        
        return {
            'max_3d': max_3d,
            'min_3d': min_3d,
            'max_5d': max_5d,
            'min_5d': min_5d,
            'max_10d': max_10d,
            'min_10d': min_10d
        }
    
    except Exception as e:
        print(f"计算收益率时出错: {e}")
        return {}

@app.callback(
    [Output("analysis-table", "data"),
     Output("analysis-progress", "children", allow_duplicate=True),
     Output("analysis-stats", "children")],
    [Input("start-analysis-btn", "n_clicks")],
    [State("log-file-dropdown", "value"),
     State("analysis-data-dir", "value")],
    prevent_initial_call=True
)
def start_analysis_callback(n_clicks, log_file, data_dir):
    if not n_clicks or not log_file or not data_dir:
        return [], "", ""
    
    try:
        # 解析日志文件
        results = parse_log_file(log_file)
        
        if not results:
            return [], html.Div("⚠️ 未找到选股结果，请检查日志文件", 
                              style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"}), ""
        
        # 计算评分和收益率
        table_data = []
        for result in results:
            # 计算评分
            score_data = calculate_stock_score(result)
            
            # 计算收益率
            returns = calculate_future_returns(result, data_dir)
            
            # 构建表格行
            row = {
                "策略": "JZVolumeShrinkSelector",
                "日期": result['date'],
                "股票": result['code'],
                "买入日期": (pd.to_datetime(result['date']) + timedelta(days=1)).strftime('%Y-%m-%d'),
                "评分": score_data['total_score'],
                "评级": score_data['grade'],
                # A点信息
                "A点日期": result['a_date'],
                "A点开盘": result['a_open'],
                "A点收盘": result['a_close'],
                "A点最高": result['a_high'],
                "A点最低": result['a_low'],
                "A点成交量": result['a_vol'],
                "A点实体涨跌幅": f"{result['a_entity_pct']:.4f}",
                "A点价格振幅": f"{result['a_price_amplitude']:.4f}",
                # B点信息
                "B点日期": result['b_date'],
                "B点开盘": result['b_open'],
                "B点收盘": result['b_close'],
                "B点最高": result['b_high'],
                "B点最低": result['b_low'],
                "B点成交量": result['b_vol'],
                "B点实体涨跌幅": f"{result['b_entity_pct']:.4f}",
                "B点价格振幅": f"{result['b_price_amplitude']:.4f}",
                # C点信息
                "C点日期": result['c_date'],
                "C点开盘": result['c_open'],
                "C点收盘": result['c_close'],
                "C点最高": result['c_high'],
                "C点最低": result['c_low'],
                "C点成交量": result['c_vol'],
                "C点实体涨跌幅": f"{result['c_entity_pct']:.4f}",
                "C点价格振幅": f"{result['c_price_amplitude']:.4f}",
                # D点信息
                "D点日期": result['d_date'],
                "D点开盘": result['d_open'],
                "D点收盘": result['d_close'],
                "D点最高": result['d_high'],
                "D点最低": result['d_low'],
                "D点成交量": result['d_vol'],
                "D点实体涨跌幅": f"{result['d_entity_pct']:.4f}",
                "D点价格振幅": f"{result['d_price_amplitude']:.4f}",
                # E点信息
                "E点日期": result['e_date'],
                "E点开盘": result['e_open'],
                "E点收盘": result['e_close'],
                "E点最高": result['e_high'],
                "E点最低": result['e_low'],
                "E点成交量": result['e_vol'],
                "E点实体涨跌幅": f"{result['e_entity_pct']:.4f}",
                "E点价格振幅": f"{result['e_price_amplitude']:.4f}",
                # 区间信息
                "A-B涨幅": f"{result.get('ab_rise', 0):.4f}",
                "A-B天数": result.get('ab_days', 0),
                "B-C跌幅": f"{result.get('bc_fall', 0):.4f}",
                "B-C天数": result.get('bc_days', 0),
                "C-D涨幅": f"{result.get('cd_rise', 0):.4f}",
                "C-D天数": result.get('cd_days', 0),
                "D-E涨幅": f"{result.get('de_rise', 0):.4f}",
                "D-E天数": result.get('de_days', 0),
                # 成交量比例信息
                "D点成交量/C-D均量": result.get('d_vol_ratio', 0),
                "D点上影线涨幅": result.get('d_shadow_ratio', 0),
                "D点上影线/实体": result.get('d_shadow_ratio', 0),
                "E点成交量/C-D均量": result.get('e_vol_ratio', 0),
                "E点成交量/D点成交量": result.get('e_vol_ratio', 0) / result.get('d_vol_ratio', 1) if result.get('d_vol_ratio', 1) != 0 else 0,
                # 未来收益率
                "3日最大涨幅": f"{returns.get('max_3d', 0):.1f}%" if returns.get('max_3d') is not None else "-",
                "3日最大跌幅": f"{returns.get('min_3d', 0):.1f}%" if returns.get('min_3d') is not None else "-",
                "5日最大涨幅": f"{returns.get('max_5d', 0):.1f}%" if returns.get('max_5d') is not None else "-",
                "5日最大跌幅": f"{returns.get('min_5d', 0):.1f}%" if returns.get('min_5d') is not None else "-",
                "10日最大涨幅": f"{returns.get('max_10d', 0):.1f}%" if returns.get('max_10d') is not None else "-",
                "10日最大跌幅": f"{returns.get('min_10d', 0):.1f}%" if returns.get('min_10d') is not None else "-",
                # 风险提示
                "风险提示": ", ".join(score_data['risk_warnings']) if score_data['risk_warnings'] else "无"
            }
            table_data.append(row)
        
        # 计算统计信息
        if table_data:
            scores = [row['评分'] for row in table_data]
            grades = [row['评级'] for row in table_data]
            
            stats = [
                html.P(f"总选股数量: {len(table_data)}"),
                html.P(f"平均评分: {sum(scores)/len(scores):.1f}"),
                html.P(f"最高评分: {max(scores)}"),
                html.P(f"最低评分: {min(scores)}"),
                html.P(f"A+级: {grades.count('A+')}个"),
                html.P(f"A级: {grades.count('A')}个"),
                html.P(f"B+级: {grades.count('B+')}个"),
                html.P(f"B级: {grades.count('B')}个"),
                html.P(f"C+级: {grades.count('C+')}个"),
                html.P(f"C级: {grades.count('C')}个")
            ]
        else:
            stats = [html.P("无统计数据")]
        
        return table_data, html.Div(f"✅ 分析完成，共找到 {len(table_data)} 个选股结果", 
                                  style={"color": "#28a745", "fontWeight": "bold", "marginTop": "10px"}), stats
    
    except Exception as e:
        return [], html.Div(f"❌ 分析出错: {str(e)}", 
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"}), ""

@app.callback(
    [Output("export-analysis-btn", "n_clicks"),
     Output("analysis-progress", "children", allow_duplicate=True)],
    [Input("export-analysis-btn", "n_clicks")],
    [State("analysis-table", "data")],
    prevent_initial_call=True
)
def export_analysis_results(n_clicks, table_data):
    if not n_clicks:
        return 0, ""
    
    # 检查是否有数据可以导出
    if not table_data or len(table_data) == 0:
        return 0, html.Div("⚠️ 没有数据可以导出，请先进行选股分析", 
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"})
    
    try:
        import pandas as pd
        from datetime import datetime
        
        # 转换为DataFrame并导出
        df = pd.DataFrame(table_data)
        filename = f"选股分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(filename, index=False)
        
        return 0, html.Div(f"✅ 导出成功！文件已保存为: {filename}", 
                          style={"color": "#28a745", "fontWeight": "bold", "marginTop": "10px"})
    except Exception as e:
        return 0, html.Div(f"❌ 导出失败: {str(e)}", 
                          style={"color": "#dc3545", "fontWeight": "bold", "marginTop": "10px"})

if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050) 