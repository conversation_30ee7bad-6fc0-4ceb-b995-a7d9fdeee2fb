#!/usr/bin/env python3
"""
深度学习股票预测脚本
使用训练好的神经网络模型预测股票涨跌
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

# 添加当前目录到路径
sys.path.append('.')
sys.path.append('deeplearning')

from data_preprocessor import StockDataPreprocessor
from neural_network import StockNeuralNetwork
from html_renderer import HTMLRenderer

class DeepLearningPredictor:
    """深度学习预测器"""
    
    def __init__(self, config_path="deeplearning/config.json"):
        """初始化预测器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.preprocessor = StockDataPreprocessor(config_path)
        self.model = StockNeuralNetwork(config_path)
        self.model_loaded = False

        # 创建时间戳子文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"deeplearning/output/prediction_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def load_trained_models(self, model_dir=None):
        """加载训练好的模型"""
        print("📊 加载训练好的模型...")

        if model_dir is None:
            model_dir = self.config['data']['model_path']

        # 加载标准化器
        scaler_success = self.preprocessor.load_scaler()
        if not scaler_success:
            print("⚠️ 标准化器加载失败，预测可能不准确")

        # 尝试加载所有模型
        model_names = ['PrecisionMLP', 'ResidualNet', 'AttentionNet']
        loaded_models = []

        for model_name in model_names:
            latest_model_path = os.path.join(model_dir, f"latest_{model_name}.pth")

            if os.path.exists(latest_model_path):
                success = self.model.load_model(model_name, latest_model_path)
                if success:
                    loaded_models.append(model_name)
                    print(f"✅ {model_name} 加载成功")
                else:
                    print(f"❌ {model_name} 加载失败")
            else:
                print(f"⚠️ 未找到 {model_name} 模型文件: {latest_model_path}")

        if loaded_models:
            self.model_loaded = True
            print(f"✅ 成功加载 {len(loaded_models)} 个模型: {loaded_models}")
            return True
        else:
            print("❌ 没有成功加载任何模型")
            return False
    
    def predict_stocks(self, data_path, actual_gain_col=None):
        """预测股票涨跌"""
        print(f"🔮 开始深度学习预测...")
        print("=" * 60)
        
        if not self.model_loaded:
            print("❌ 模型未加载")
            return None
        
        # 1. 加载数据
        print(f"📊 加载预测数据: {data_path}")
        try:
            df = pd.read_excel(data_path)
            print(f"   原始数据: {len(df)} 行")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
        
        # 2. 数据预处理
        print(f"🔧 预处理数据...")
        X_scaled = self.preprocessor.preprocess_prediction_data(df)
        if X_scaled is None:
            return None
        
        # 3. 模型预测
        print(f"🤖 神经网络预测...")

        # 使用集成预测
        predictions, probabilities = self.model.predict_ensemble(X_scaled, method='average')

        if predictions is None:
            print("❌ 预测失败")
            return None
        
        # 4. 整理预测结果
        print(f"📋 整理预测结果...")
        
        # 创建结果DataFrame
        result_df = df.copy()
        
        # 添加预测结果
        result_df['涨超10%概率'] = probabilities.flatten()
        result_df['预测结果'] = predictions.flatten()
        result_df['置信度等级'] = result_df['涨超10%概率'].apply(self.get_confidence_level)
        result_df['投资建议'] = result_df['置信度等级'].apply(self.get_investment_advice)
        result_df['预测买入日期'] = datetime.now().strftime('%Y-%m-%d')

        # 添加原始买入日期（如果存在）
        if '买入日期' in result_df.columns:
            result_df['原始买入日期'] = result_df['买入日期']
        
        # 5. 分析预测结果
        self.analyze_predictions(result_df, actual_gain_col)
        
        # 6. 按概率排序
        result_df = result_df.sort_values('涨超10%概率', ascending=False).reset_index(drop=True)
        result_df['排名'] = range(1, len(result_df) + 1)
        
        # 7. 显示Top推荐
        self.display_top_recommendations(result_df)
        
        # 8. 立即显示TopN预测结果与实际对比
        self.display_topn_results(result_df, actual_gain_col)

        # 9. 保存结果
        output_path = self.save_results(result_df)

        # 10. 生成HTML报告
        print(f"\n🎨 生成HTML预测报告...")
        try:
            renderer = HTMLRenderer()
            html_path = renderer.create_prediction_html(output_path)
            print(f"📄 HTML预测报告: {html_path}")
        except Exception as e:
            print(f"⚠️ HTML报告生成失败: {e}")

        print(f"\n🎉 深度学习预测完成！")
        print(f"📁 结果文件: {output_path}")

        return result_df
    
    def get_confidence_level(self, probability):
        """根据概率获取置信度等级"""
        if probability >= 0.80:
            return '超高置信度'
        elif probability >= 0.70:
            return '高置信度'
        elif probability >= 0.60:
            return '中高置信度'
        elif probability >= 0.50:
            return '中等置信度'
        elif probability >= 0.40:
            return '中低置信度'
        elif probability >= 0.30:
            return '低置信度'
        else:
            return '极低置信度'
    
    def get_investment_advice(self, confidence_level):
        """获取投资建议"""
        advice_map = {
            '超高置信度': '🔥 强烈推荐',
            '高置信度': '⭐ 积极关注',
            '中高置信度': '📈 适度关注',
            '中等置信度': '👀 观察',
            '中低置信度': '⚠️ 谨慎',
            '低置信度': '❌ 不建议',
            '极低置信度': '🚫 避免'
        }
        return advice_map.get(confidence_level, '👀 观察')
    
    def analyze_predictions(self, result_df, actual_gain_col):
        """分析预测结果"""
        print(f"\n📊 预测结果分析:")
        print("-" * 40)
        
        total_stocks = len(result_df)
        
        # 按概率阈值统计
        thresholds = [0.8, 0.7, 0.6, 0.5, 0.4, 0.3]
        for threshold in thresholds:
            count = (result_df['涨超10%概率'] >= threshold).sum()
            percentage = count / total_stocks * 100
            print(f"   概率≥{threshold:.0%}: {count} 只 ({percentage:.1f}%)")
        
        # 如果有实际涨幅数据，计算准确率
        if actual_gain_col and actual_gain_col in result_df.columns:
            print(f"\n📈 预测准确性分析 (基于{actual_gain_col}):")
            print("-" * 40)
            
            # 创建实际标签
            actual_labels = (result_df[actual_gain_col] >= 0.10).astype(int)
            predicted_labels = result_df['预测结果']
            
            # 计算准确率
            accuracy = (actual_labels == predicted_labels).mean()
            
            # 计算各阈值下的精确率
            for threshold in [0.8, 0.7, 0.6, 0.5]:
                mask = result_df['涨超10%概率'] >= threshold
                if mask.sum() > 0:
                    precision = actual_labels[mask].mean()
                    count = mask.sum()
                    print(f"   概率≥{threshold:.0%}: 精确率{precision:.1%} ({count}只)")
            
            print(f"   总体准确率: {accuracy:.1%}")
            
            # Top N 准确率
            for n in [3, 5, 10]:
                if len(result_df) >= n:
                    top_n_accuracy = actual_labels.head(n).mean()
                    print(f"   Top{n}准确率: {top_n_accuracy:.1%}")
    
    def display_top_recommendations(self, result_df):
        """显示Top推荐"""
        print(f"\n🏆 Top10 深度学习推荐股票:")
        print("-" * 100)
        print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'涨超10%概率':<12} {'置信度等级':<12} {'投资建议':<12}")
        print("-" * 100)

        # 获取股票代码列
        stock_col = '股票'
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                stock_col = col
                break

        for i in range(min(10, len(result_df))):
            row = result_df.iloc[i]
            stock_code = row.get(stock_col, f"股票_{i+1}")
            buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
            probability = row['涨超10%概率']
            confidence_level = row['置信度等级']
            advice = row['投资建议']

            print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<12.1%} {confidence_level:<12} {advice:<12}")

    def display_topn_results(self, result_df, actual_gain_col=None):
        """立即显示TopN预测结果与实际对比"""
        print(f"\n📊 TopN 预测结果与实际对比:")
        print("=" * 100)

        # 获取股票代码列
        stock_col = '股票'
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                stock_col = col
                break

        # 显示Top5, Top10, Top20的结果
        for n in [5, 10, 20]:
            if len(result_df) >= n:
                print(f"\n🏆 Top{n} 预测结果:")
                print("-" * 100)

                if actual_gain_col and actual_gain_col in result_df.columns:
                    print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'预测概率':<10} {'实际涨幅':<10} {'预测结果':<8} {'置信度等级':<12}")
                    print("-" * 120)

                    correct_predictions = 0
                    for i in range(n):
                        row = result_df.iloc[i]
                        stock_code = row.get(stock_col, f"股票_{i+1}")
                        buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
                        probability = row['涨超10%概率']
                        actual_gain = row[actual_gain_col]
                        confidence_level = row['置信度等级']

                        # 判断预测是否正确
                        if isinstance(actual_gain, (int, float)):
                            actual_success = actual_gain >= 0.10
                            # 对于TopN推荐，如果实际涨超10%就算预测成功
                            predicted_success = True  # TopN本身就是预测会涨的股票
                            is_correct = actual_success == predicted_success
                            if is_correct:
                                correct_predictions += 1

                            result_icon = "✅" if actual_success else "❌"
                            actual_gain_str = f"{actual_gain:.1%}"
                        else:
                            result_icon = "❓"
                            actual_gain_str = str(actual_gain)

                        print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<10.1%} {actual_gain_str:<10} {result_icon:<8} {confidence_level:<12}")

                    accuracy = correct_predictions / n
                    success_rate = sum(1 for i in range(n) if isinstance(result_df.iloc[i][actual_gain_col], (int, float)) and result_df.iloc[i][actual_gain_col] >= 0.10) / n

                    print(f"\n📈 Top{n} 统计:")
                    print(f"   预测准确率: {accuracy:.1%} ({correct_predictions}/{n})")
                    print(f"   实际成功率: {success_rate:.1%}")

                else:
                    print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'预测概率':<10} {'置信度等级':<12} {'投资建议':<12}")
                    print("-" * 120)

                    for i in range(n):
                        row = result_df.iloc[i]
                        stock_code = row.get(stock_col, f"股票_{i+1}")
                        buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
                        probability = row['涨超10%概率']
                        confidence_level = row['置信度等级']
                        advice = row['投资建议']

                        print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<10.1%} {confidence_level:<12} {advice:<12}")

        print("\n" + "=" * 100)
    
    def save_results(self, result_df):
        """保存预测结果"""
        # 保存Excel文件
        output_path = os.path.join(self.output_dir, f"dl_prediction_results_{self.timestamp}.xlsx")
        
        # 选择要保存的列
        save_columns = []
        
        # 添加股票标识列
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                save_columns.append(col)
                break
        
        # 添加预测相关列
        prediction_columns = [
            '排名', '涨超10%概率', '预测结果', '置信度等级',
            '投资建议', '预测买入日期', '原始买入日期'
        ]
        save_columns.extend([col for col in prediction_columns if col in result_df.columns])
        
        # 添加原始特征列
        feature_columns = self.config['data']['feature_columns']
        save_columns.extend([col for col in feature_columns if col in result_df.columns])
        
        # 如果有实际涨幅列，也保存
        for col in ['5日最大涨幅', '实际涨幅']:
            if col in result_df.columns:
                save_columns.append(col)
        
        # 保存文件
        result_df[save_columns].to_excel(output_path, index=False)
        print(f"💾 预测结果已保存: {output_path}")
        
        return output_path

    def compare_with_traditional_method(self, result_df, traditional_results_path=None):
        """与传统方法比较"""
        if traditional_results_path is None:
            print("⚠️ 未提供传统方法结果，跳过比较")
            return

        try:
            print(f"\n🔄 与传统方法比较...")
            print("-" * 40)

            # 加载传统方法结果
            traditional_df = pd.read_excel(traditional_results_path)

            # 假设传统方法有置信度列
            if '涨超10%置信度' in traditional_df.columns:
                # 比较Top10推荐的重叠度
                dl_top10 = set(result_df.head(10).index)
                traditional_top10 = set(traditional_df.head(10).index)

                overlap = len(dl_top10.intersection(traditional_top10))
                overlap_rate = overlap / 10

                print(f"   Top10重叠股票: {overlap} 只 ({overlap_rate:.1%})")

                # 比较平均置信度/概率
                dl_avg_prob = result_df.head(10)['涨超10%概率'].mean()
                traditional_avg_conf = traditional_df.head(10)['涨超10%置信度'].str.rstrip('%').astype(float).mean() / 100

                print(f"   深度学习Top10平均概率: {dl_avg_prob:.1%}")
                print(f"   传统方法Top10平均置信度: {traditional_avg_conf:.1%}")

        except Exception as e:
            print(f"⚠️ 比较失败: {e}")

def main():
    """主函数"""
    print("🎯 深度学习股票预测系统")
    print("=" * 80)
    
    # 创建预测器
    predictor = DeepLearningPredictor()
    
    # 检查配置
    if predictor.config is None:
        print("❌ 配置文件加载失败，退出程序")
        return
    
    # 加载模型
    if not predictor.load_trained_models():
        print("❌ 模型加载失败，请先训练模型")
        return
    
    # 获取预测数据路径
    data_path = input("请输入预测数据文件路径: ").strip()
    if not data_path:
        print("❌ 未输入数据路径")
        return
    
    if not os.path.exists(data_path):
        print(f"❌ 文件不存在: {data_path}")
        return
    
    # 开始预测
    results = predictor.predict_stocks(data_path, actual_gain_col='5日最大涨幅')
    
    if results is not None:
        print(f"\n🎉 预测完成！")
        print(f"📊 共预测 {len(results)} 只股票")
    else:
        print(f"\n❌ 预测失败")

if __name__ == "__main__":
    main()
