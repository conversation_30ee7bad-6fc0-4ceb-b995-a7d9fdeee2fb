#!/usr/bin/env python3
"""
深度学习股票预测脚本
使用训练好的神经网络模型预测股票涨跌
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

# 添加当前目录到路径
sys.path.append('.')
sys.path.append('deeplearning')

from data_preprocessor import StockDataPreprocessor
from neural_network import StockNeuralNetwork
from html_renderer import HTMLRenderer

class DeepLearningPredictor:
    """深度学习预测器"""
    
    def __init__(self, config_path="deeplearning/config.json"):
        """初始化预测器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.preprocessor = StockDataPreprocessor(config_path)
        self.model = StockNeuralNetwork(config_path)
        self.model_loaded = False

        # 创建时间戳子文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"deeplearning/output/prediction_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def load_trained_models(self, model_dir=None):
        """加载训练好的模型 - 优先使用AttentionNet（100%精确率）"""
        print("📊 加载训练好的模型...")

        if model_dir is None:
            model_dir = self.config['data']['model_path']

        # 加载标准化器
        scaler_success = self.preprocessor.load_scaler()
        if not scaler_success:
            print("⚠️ 标准化器加载失败，预测可能不准确")

        # 优先加载AttentionNet模型（训练中达到100%精确率）
        model_names = ['AttentionNet', 'PrecisionMLP', 'ResidualNet']
        loaded_models = []

        for model_name in model_names:
            # 尝试加载最新的模型文件
            model_files = [f for f in os.listdir(model_dir) if f.startswith(model_name) and f.endswith('.pth')]
            if model_files:
                # 选择最新的模型文件
                latest_model_file = sorted(model_files)[-1]
                latest_model_path = os.path.join(model_dir, latest_model_file)

                success = self.model.load_model(model_name, latest_model_path)
                if success:
                    loaded_models.append(model_name)
                    print(f"✅ {model_name} 加载成功: {latest_model_file}")
                else:
                    print(f"❌ {model_name} 加载失败")
            else:
                print(f"⚠️ 未找到 {model_name} 模型文件")

        if loaded_models:
            self.model_loaded = True
            print(f"✅ 成功加载 {len(loaded_models)} 个模型: {loaded_models}")
            # 如果AttentionNet加载成功，优先使用它
            if 'AttentionNet' in loaded_models:
                print(f"🎯 将优先使用AttentionNet模型（训练中达到100%精确率）")
            return True
        else:
            print("❌ 没有成功加载任何模型")
            return False
    
    def predict_stocks(self, data_path, actual_gain_col=None):
        """预测股票涨跌 - 只输出满足正样本条件的股票"""
        print(f"🔮 开始深度学习预测（精确率优化模式）...")
        print("=" * 60)

        if not self.model_loaded:
            print("❌ 模型未加载")
            return None

        # 1. 加载数据
        print(f"📊 加载预测数据: {data_path}")
        try:
            df = pd.read_excel(data_path)
            print(f"   原始数据: {len(df)} 行")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None

        # 2. 数据预处理
        print(f"🔧 预处理数据...")
        X_scaled = self.preprocessor.preprocess_prediction_data(df)
        if X_scaled is None:
            return None

        # 3. 模型预测 - 优先使用AttentionNet
        print(f"🤖 神经网络预测（使用高精确率模型）...")

        # 如果AttentionNet可用，优先使用它（训练中达到100%精确率）
        if 'AttentionNet' in self.model.models:
            print(f"🎯 使用AttentionNet模型（100%精确率）")
            predictions, probabilities = self.model.predict_single_model(X_scaled, 'AttentionNet')
            # 使用训练时的最优阈值0.86
            optimal_threshold = 0.86
            predictions = (probabilities >= optimal_threshold).astype(int)
        else:
            # 如果AttentionNet不可用，使用集成预测
            print(f"⚠️ AttentionNet不可用，使用集成预测")
            predictions, probabilities = self.model.predict_ensemble(X_scaled, method='average')

        if predictions is None:
            print("❌ 预测失败")
            return None

        # 4. 筛选正样本 - 只保留预测为正样本的股票
        print(f"🎯 筛选正样本股票...")

        # 创建结果DataFrame
        result_df = df.copy()

        # 添加预测结果
        result_df['涨超10%概率'] = probabilities.flatten()
        result_df['预测结果'] = predictions.flatten()

        # 只保留预测为正样本的股票
        positive_mask = result_df['预测结果'] == 1
        result_df = result_df[positive_mask].copy()

        if len(result_df) == 0:
            print(f"⚠️ 没有股票满足正样本条件（高精确率模式）")
            print(f"💡 这说明模型非常保守，只选择最有把握的股票")
            return None

        print(f"✅ 筛选出 {len(result_df)} 只满足正样本条件的股票（原始数据: {len(df)} 只）")
        print(f"📊 筛选比例: {len(result_df)/len(df):.1%}")

        # 添加其他信息
        result_df['置信度等级'] = result_df['涨超10%概率'].apply(self.get_confidence_level)
        result_df['投资建议'] = result_df['置信度等级'].apply(self.get_investment_advice)
        result_df['预测买入日期'] = datetime.now().strftime('%Y-%m-%d')

        # 添加原始买入日期（如果存在）
        if '买入日期' in result_df.columns:
            result_df['原始买入日期'] = result_df['买入日期']

        # 5. 分析预测结果
        self.analyze_positive_predictions(result_df, actual_gain_col)

        # 6. 按概率排序
        result_df = result_df.sort_values('涨超10%概率', ascending=False).reset_index(drop=True)
        result_df['排名'] = range(1, len(result_df) + 1)

        # 7. 显示推荐股票
        self.display_positive_recommendations(result_df)

        # 8. 显示预测结果与实际对比
        self.display_positive_results(result_df, actual_gain_col)

        # 9. 保存结果
        output_path = self.save_results(result_df)

        # 10. 生成HTML报告
        print(f"\n🎨 生成HTML预测报告...")
        try:
            renderer = HTMLRenderer()
            html_path = renderer.create_prediction_html(output_path)
            print(f"📄 HTML预测报告: {html_path}")
        except Exception as e:
            print(f"⚠️ HTML报告生成失败: {e}")

        print(f"\n🎉 深度学习预测完成（精确率优化模式）！")
        print(f"📁 结果文件: {output_path}")
        print(f"💡 只输出了模型认为满足正样本条件的 {len(result_df)} 只股票")

        return result_df
    
    def get_confidence_level(self, probability):
        """根据概率获取置信度等级"""
        if probability >= 0.80:
            return '超高置信度'
        elif probability >= 0.70:
            return '高置信度'
        elif probability >= 0.60:
            return '中高置信度'
        elif probability >= 0.50:
            return '中等置信度'
        elif probability >= 0.40:
            return '中低置信度'
        elif probability >= 0.30:
            return '低置信度'
        else:
            return '极低置信度'
    
    def get_investment_advice(self, confidence_level):
        """获取投资建议"""
        advice_map = {
            '超高置信度': '🔥 强烈推荐',
            '高置信度': '⭐ 积极关注',
            '中高置信度': '📈 适度关注',
            '中等置信度': '👀 观察',
            '中低置信度': '⚠️ 谨慎',
            '低置信度': '❌ 不建议',
            '极低置信度': '🚫 避免'
        }
        return advice_map.get(confidence_level, '👀 观察')
    
    def analyze_positive_predictions(self, result_df, actual_gain_col):
        """分析正样本预测结果"""
        print(f"\n📊 正样本预测结果分析:")
        print("-" * 40)

        total_stocks = len(result_df)
        print(f"   预测正样本数量: {total_stocks} 只")

        # 概率分布统计
        avg_prob = result_df['涨超10%概率'].mean()
        min_prob = result_df['涨超10%概率'].min()
        max_prob = result_df['涨超10%概率'].max()

        print(f"   平均预测概率: {avg_prob:.1%}")
        print(f"   概率范围: {min_prob:.1%} - {max_prob:.1%}")

        # 按概率阈值统计
        thresholds = [0.9, 0.8, 0.7, 0.6, 0.5]
        for threshold in thresholds:
            count = (result_df['涨超10%概率'] >= threshold).sum()
            percentage = count / total_stocks * 100 if total_stocks > 0 else 0
            print(f"   概率≥{threshold:.0%}: {count} 只 ({percentage:.1f}%)")

        # 如果有实际涨幅数据，计算精确率
        if actual_gain_col and actual_gain_col in result_df.columns:
            print(f"\n📈 预测精确率分析 (基于{actual_gain_col}):")
            print("-" * 40)

            # 创建实际标签
            actual_labels = (result_df[actual_gain_col] >= 0.10).astype(int)

            # 计算总体精确率（所有预测为正样本的股票中，实际涨超10%的比例）
            if len(actual_labels) > 0:
                overall_precision = actual_labels.mean()
                success_count = actual_labels.sum()
                print(f"   总体精确率: {overall_precision:.1%} ({success_count}/{len(actual_labels)})")

                # 计算各阈值下的精确率
                for threshold in [0.9, 0.8, 0.7, 0.6]:
                    mask = result_df['涨超10%概率'] >= threshold
                    if mask.sum() > 0:
                        precision = actual_labels[mask].mean()
                        count = mask.sum()
                        success = actual_labels[mask].sum()
                        print(f"   概率≥{threshold:.0%}: 精确率{precision:.1%} ({success}/{count})")

                # 显示失败案例
                failed_mask = actual_labels == 0
                if failed_mask.sum() > 0:
                    print(f"\n❌ 预测失败案例: {failed_mask.sum()} 只")
                    failed_probs = result_df[failed_mask]['涨超10%概率']
                    print(f"   失败案例平均概率: {failed_probs.mean():.1%}")
            else:
                print(f"   无法计算精确率：缺少实际涨幅数据")

    def analyze_predictions(self, result_df, actual_gain_col):
        """分析预测结果（保留原方法以兼容）"""
        return self.analyze_positive_predictions(result_df, actual_gain_col)
    
    def display_positive_recommendations(self, result_df):
        """显示正样本推荐股票"""
        print(f"\n🏆 精确率优化推荐股票 (共{len(result_df)}只):")
        print("-" * 100)
        print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'涨超10%概率':<12} {'置信度等级':<12} {'投资建议':<12}")
        print("-" * 100)

        # 获取股票代码列
        stock_col = '股票'
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                stock_col = col
                break

        for i in range(len(result_df)):
            row = result_df.iloc[i]
            stock_code = row.get(stock_col, f"股票_{i+1}")
            buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
            probability = row['涨超10%概率']
            confidence_level = row['置信度等级']
            advice = row['投资建议']

            print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<12.1%} {confidence_level:<12} {advice:<12}")

        print("-" * 100)
        print(f"💡 以上股票均为模型预测的正样本，具有高精确率保证")

    def display_top_recommendations(self, result_df):
        """显示Top推荐（保留原方法以兼容）"""
        return self.display_positive_recommendations(result_df)

    def display_positive_results(self, result_df, actual_gain_col=None):
        """显示正样本预测结果与实际对比"""
        print(f"\n📊 正样本预测结果与实际对比:")
        print("=" * 100)

        # 获取股票代码列
        stock_col = '股票'
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                stock_col = col
                break

        total_stocks = len(result_df)
        print(f"\n� 所有正样本预测结果 (共{total_stocks}只):")
        print("-" * 100)

        if actual_gain_col and actual_gain_col in result_df.columns:
            print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'预测概率':<10} {'实际涨幅':<10} {'预测结果':<8} {'置信度等级':<12}")
            print("-" * 120)

            correct_predictions = 0
            for i in range(total_stocks):
                row = result_df.iloc[i]
                stock_code = row.get(stock_col, f"股票_{i+1}")
                buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
                probability = row['涨超10%概率']
                actual_gain = row[actual_gain_col]
                confidence_level = row['置信度等级']

                # 判断预测是否正确
                if isinstance(actual_gain, (int, float)):
                    actual_success = actual_gain >= 0.10
                    if actual_success:
                        correct_predictions += 1

                    result_icon = "✅" if actual_success else "❌"
                    actual_gain_str = f"{actual_gain:.1%}"
                else:
                    result_icon = "❓"
                    actual_gain_str = str(actual_gain)

                print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<10.1%} {actual_gain_str:<10} {result_icon:<8} {confidence_level:<12}")

            if total_stocks > 0:
                precision = correct_predictions / total_stocks
                print(f"\n📈 正样本预测统计:")
                print(f"   预测精确率: {precision:.1%} ({correct_predictions}/{total_stocks})")
                print(f"   💡 这是模型在实际数据上的精确率表现")

        else:
            print(f"{'排名':<4} {'股票代码':<10} {'买入日期':<12} {'预测概率':<10} {'置信度等级':<12} {'投资建议':<12}")
            print("-" * 120)

            for i in range(total_stocks):
                row = result_df.iloc[i]
                stock_code = row.get(stock_col, f"股票_{i+1}")
                buy_date = row.get('原始买入日期', row.get('买入日期', 'N/A'))
                probability = row['涨超10%概率']
                confidence_level = row['置信度等级']
                advice = row['投资建议']

                print(f"{i+1:<4} {stock_code:<10} {buy_date:<12} {probability:<10.1%} {confidence_level:<12} {advice:<12}")

        print("\n" + "=" * 100)
        print(f"💡 以上所有股票均为模型预测的正样本，采用高精确率策略")

    def display_topn_results(self, result_df, actual_gain_col=None):
        """显示TopN预测结果（保留原方法以兼容）"""
        return self.display_positive_results(result_df, actual_gain_col)
    
    def save_results(self, result_df):
        """保存预测结果"""
        # 保存Excel文件
        output_path = os.path.join(self.output_dir, f"dl_prediction_results_{self.timestamp}.xlsx")
        
        # 选择要保存的列
        save_columns = []
        
        # 添加股票标识列
        for col in ['股票', '股票代码', '股票名称']:
            if col in result_df.columns:
                save_columns.append(col)
                break
        
        # 添加预测相关列
        prediction_columns = [
            '排名', '涨超10%概率', '预测结果', '置信度等级',
            '投资建议', '预测买入日期', '原始买入日期'
        ]
        save_columns.extend([col for col in prediction_columns if col in result_df.columns])
        
        # 添加原始特征列
        feature_columns = self.config['data']['feature_columns']
        save_columns.extend([col for col in feature_columns if col in result_df.columns])
        
        # 如果有实际涨幅列，也保存
        for col in ['5日最大涨幅', '实际涨幅']:
            if col in result_df.columns:
                save_columns.append(col)
        
        # 保存文件
        result_df[save_columns].to_excel(output_path, index=False)
        print(f"💾 预测结果已保存: {output_path}")
        
        return output_path

    def compare_with_traditional_method(self, result_df, traditional_results_path=None):
        """与传统方法比较"""
        if traditional_results_path is None:
            print("⚠️ 未提供传统方法结果，跳过比较")
            return

        try:
            print(f"\n🔄 与传统方法比较...")
            print("-" * 40)

            # 加载传统方法结果
            traditional_df = pd.read_excel(traditional_results_path)

            # 假设传统方法有置信度列
            if '涨超10%置信度' in traditional_df.columns:
                # 比较Top10推荐的重叠度
                dl_top10 = set(result_df.head(10).index)
                traditional_top10 = set(traditional_df.head(10).index)

                overlap = len(dl_top10.intersection(traditional_top10))
                overlap_rate = overlap / 10

                print(f"   Top10重叠股票: {overlap} 只 ({overlap_rate:.1%})")

                # 比较平均置信度/概率
                dl_avg_prob = result_df.head(10)['涨超10%概率'].mean()
                traditional_avg_conf = traditional_df.head(10)['涨超10%置信度'].str.rstrip('%').astype(float).mean() / 100

                print(f"   深度学习Top10平均概率: {dl_avg_prob:.1%}")
                print(f"   传统方法Top10平均置信度: {traditional_avg_conf:.1%}")

        except Exception as e:
            print(f"⚠️ 比较失败: {e}")

def main():
    """主函数 - 精确率优化预测"""
    print("🎯 深度学习股票预测系统（精确率优化模式）")
    print("=" * 80)
    print("💡 本系统只输出模型认为满足正样本条件的股票")
    print("🎯 目标：宁愿低召回率，也要保证高精确率（80%+）")
    print("=" * 80)

    # 创建预测器
    predictor = DeepLearningPredictor()

    # 检查配置
    if predictor.config is None:
        print("❌ 配置文件加载失败，退出程序")
        return

    # 加载模型
    if not predictor.load_trained_models():
        print("❌ 模型加载失败，请先训练模型")
        print("💡 请先运行 python deeplearning/train_dl_model.py 训练模型")
        return

    # 获取预测数据路径
    data_path = input("请输入预测数据文件路径: ").strip()
    if not data_path:
        print("❌ 未输入数据路径")
        return

    if not os.path.exists(data_path):
        print(f"❌ 文件不存在: {data_path}")
        return

    # 开始预测
    results = predictor.predict_stocks(data_path, actual_gain_col='5日最大涨幅')

    if results is not None:
        print(f"\n🎉 精确率优化预测完成！")
        print(f"📊 筛选出 {len(results)} 只满足正样本条件的股票")
        print(f"💡 这些股票具有高精确率保证，建议重点关注")
    else:
        print(f"\n⚠️ 没有股票满足正样本条件")
        print(f"💡 这说明模型非常保守，当前数据中没有高把握的投资机会")

if __name__ == "__main__":
    main()
