# 🎯 两步预测系统使用说明

## 📊 系统概述

我已经为您创建了一个标准的两步预测系统，完全基于您现有的 `add_prediction_columns.py` 脚本改造：

### **脚本1: train_prediction_rules.py** 
- **功能**: 训练预测规则并保存
- **输入**: 包含特征和涨幅的训练数据
- **输出**: 保存训练好的规则和参数

### **脚本2: predict_new_stocks.py**
- **功能**: 对新数据进行预测
- **输入**: 只有特征列的新数据（没有涨幅列）
- **输出**: 添加预测涨幅、买入日期等列的完整数据

## 🚀 使用流程

### **第一步: 训练规则**

```bash
python scripts/train_prediction_rules.py
```

**训练结果:**
- ✅ 总股票数: 1,182
- ✅ 选中股票数: 305
- ✅ 成功股票数: 43
- ✅ **选股成功率: 14.1%**
- ✅ 选股提升倍数: 1.6x

**保存的文件:**
- `models/prediction_rules_20250802_213037.json` - 带时间戳的规则文件
- `models/latest_prediction_rules.json` - 最新规则文件（预测脚本使用）

### **第二步: 预测新数据**

```bash
python scripts/predict_new_stocks.py
```

**系统会提示输入新数据文件路径:**
```
请输入新数据文件路径: your_new_data.xlsx
```

**预测输出:**
- 🎯 高潜力股(预测>10%): X只
- 🥇 最推荐的5只股票
- 🏆 最推荐的3只股票
- 💾 保存预测结果到Excel文件

## 📁 数据格式要求

### **训练数据格式** (脚本1输入)
必须包含以下列：

#### **特征列** (31个)
```
A点实体涨跌幅, A点价格振幅, B点成交量, B点实体涨跌幅, B点价格振幅
C点最低, C点成交量, C点实体涨跌幅, C点价格振幅, D点成交量
D点实体涨跌幅, D点价格振幅, E点成交量, E点实体涨跌幅, E点价格振幅
A-B涨幅, A-B天数, B-C跌幅, B-C天数, C-D涨幅, C-D天数
D-E涨幅, D-E天数, D点成交量/C-D均量, D点上影线涨幅, D点上影线/实体
E点成交量/C-D均量, E点成交量/D点成交量, E点J值, E点J值相对D点J值涨幅
E点相对D点收盘价涨幅
```

#### **目标列** (1个)
```
5日最大涨幅
```

#### **可选列**
```
股票, 股票代码, 股票名称, E点日期
```

### **新数据格式** (脚本2输入)
只需要包含：
- ✅ **31个特征列** (与训练数据相同)
- ✅ **股票标识列** (股票/股票代码/股票名称)
- ✅ **可选**: E点日期 (用于计算买入日期)
- ❌ **不需要**: 5日最大涨幅列

## 🎯 核心预测规则

系统使用4个核心规则进行预测：

| 规则 | 特征 | 条件 | 精确率 |
|------|------|------|--------|
| 1 | E点价格振幅 | ≥ 3.968% | 19.3% |
| 2 | D点上影线/实体 | ≥ 8.0 | 17.2% |
| 3 | A-B涨幅 | ≥ 78.62% | 15.7% |
| 4 | E点实体涨跌幅 | ≤ -2.4% | 15.7% |

## 📊 预测输出说明

### **预测结果列**
脚本2会在新数据基础上添加以下列：

| 列名 | 说明 | 示例值 |
|------|------|--------|
| 选股状态 | 股票分类 | 🎯 高潜力股 / ⚠️ 涨幅不足10% |
| 预测买入日期 | 建议买入日期 | 2025-08-03 |
| 预测5日涨幅 | 预测涨幅数值 | 0.1523 (15.23%) |
| 预测涨幅等级 | 涨幅等级 | 超高潜力/高潜力/中高潜力 |
| 置信度分数 | 预测置信度 | 0.169 |
| 投资建议 | 投资建议 | 🔥 重点关注 |
| 推荐等级 | 推荐排名 | Top3-1 / Top5-2 |

### **最推荐股票**
- **Top5推荐**: 综合评分最高的5只股票
- **Top3推荐**: 精选推荐的3只股票
- **评分算法**: 预测涨幅 × 置信度

## 💻 实际使用示例

### **示例1: 完整流程**

```bash
# 1. 训练规则（使用历史数据）
python scripts/train_prediction_rules.py

# 2. 预测新数据
python scripts/predict_new_stocks.py
# 输入: new_stocks_data.xlsx
# 输出: 新股票预测结果_20250802_213045.xlsx
```

### **示例2: 预测结果**

```
🏆 预测结果:
   高潜力股(预测>10%): 45 只

🥇 最推荐的5只股票:
   1. 000001 (预测涨幅: 23.5%, 置信度: 0.169)
   2. 000002 (预测涨幅: 21.2%, 置信度: 0.174)
   3. 000003 (预测涨幅: 19.8%, 置信度: 0.157)
   4. 000004 (预测涨幅: 18.5%, 置信度: 0.163)
   5. 000005 (预测涨幅: 17.2%, 置信度: 0.151)

🏆 最推荐的3只股票:
   1. 000001 (预测涨幅: 23.5%, 置信度: 0.169)
   2. 000002 (预测涨幅: 21.2%, 置信度: 0.174)
   3. 000003 (预测涨幅: 19.8%, 置信度: 0.157)
```

## 🔧 系统特点

### **✅ 优势**
1. **标准流程**: 训练-预测两步分离
2. **规则保存**: 训练结果可重复使用
3. **专注10%**: 只预测涨幅>10%的股票
4. **明确推荐**: 每次给出Top5和Top3
5. **完全兼容**: 基于您现有代码改造

### **📊 性能指标**
- **选股成功率**: 14.1%
- **基础成功率**: 8.7%
- **提升倍数**: 1.6x
- **覆盖率**: 25.8%

### **🎯 预测精度**
- **高潜力股筛选**: 只预测涨幅>10%
- **个性化涨幅**: 基于具体特征值计算
- **置信度评估**: 多规则综合评分
- **买入时间**: 自动计算最佳买入日期

## ⚠️ 注意事项

### **数据要求**
1. 新数据必须包含所有31个特征列
2. 特征列名必须与训练数据完全一致
3. 数据格式可以是百分比字符串（如"5.23%"）

### **使用建议**
1. 定期重新训练规则（当有新的历史数据时）
2. 重点关注Top3推荐股票
3. 结合实际市场情况调整投资策略
4. 设置合理的止损点

## 🎉 总结

现在您有了一个完整的两步预测系统：

1. **🎯 脚本1**: 从历史数据学习规则
2. **🔮 脚本2**: 对新数据进行预测
3. **📊 输出**: 明确的Top5和Top3推荐
4. **💾 保存**: 完整的预测结果Excel文件

**立即开始使用**: 
1. `python scripts/train_prediction_rules.py` (已完成)
2. `python scripts/predict_new_stocks.py` (输入您的新数据)

🚀 **系统已准备就绪，可以开始预测新股票了！**
