{"prediction_timestamp": "2025-08-03 02:30:28", "input_file": "选股分析结果/选股分析结果_20250730_225041.xlsx", "output_file": "ml/dual_loss_prediction_20250803_023028/预测结果_20250803_023028.xlsx", "model_folder": "ml/dual_loss_training_20250803_022752/", "total_predictions": 8, "models_used": ["ResidualNet"], "prediction_folder": "ml/dual_loss_prediction_20250803_023028", "has_actual_data": true, "accuracy_stats": {"total_predictions": 8, "successful_predictions": 0, "accuracy_rate": 0.0}}