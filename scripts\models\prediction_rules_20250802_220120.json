{"version": "1.0", "training_date": "2025-08-02 22:01:20", "selection_rules": {"E点价格振幅": {"threshold": 3.968, "operator": ">=", "precision": 0.193}, "D点上影线/实体": {"threshold": 8.0, "operator": ">=", "precision": 0.172}, "A-B涨幅": {"threshold": 78.62, "operator": ">=", "precision": 0.157}, "E点实体涨跌幅": {"threshold": -2.4, "operator": "<=", "precision": 0.157}}, "feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"], "target_column": "5日最大涨幅", "training_stats": {"total_stocks": 1182, "selected_stocks": 1, "successful_stocks": 1, "success_rate": 1.0, "selection_rate": 0.0008460236886632825, "feature_stats": {"A点实体涨跌幅": {"mean": 0.21, "std": NaN, "min": 0.21, "max": 0.21, "median": 0.21}, "A点价格振幅": {"mean": 3.64, "std": NaN, "min": 3.64, "max": 3.64, "median": 3.64}, "B点成交量": {"mean": 4111158.0, "std": NaN, "min": 4111158.0, "max": 4111158.0, "median": 4111158.0}, "B点实体涨跌幅": {"mean": -4.5, "std": NaN, "min": -4.5, "max": -4.5, "median": -4.5}, "B点价格振幅": {"mean": 13.62, "std": NaN, "min": 13.62, "max": 13.62, "median": 13.62}, "C点最低": {"mean": 6.55, "std": NaN, "min": 6.55, "max": 6.55, "median": 6.55}, "C点成交量": {"mean": 2337468.0, "std": NaN, "min": 2337468.0, "max": 2337468.0, "median": 2337468.0}, "C点实体涨跌幅": {"mean": -8.06, "std": NaN, "min": -8.06, "max": -8.06, "median": -8.06}, "C点价格振幅": {"mean": 10.69, "std": NaN, "min": 10.69, "max": 10.69, "median": 10.69}, "D点成交量": {"mean": 2841761.0, "std": NaN, "min": 2841761.0, "max": 2841761.0, "median": 2841761.0}, "D点实体涨跌幅": {"mean": 0.55, "std": NaN, "min": 0.55, "max": 0.55, "median": 0.55}, "D点价格振幅": {"mean": 6.66, "std": NaN, "min": 6.66, "max": 6.66, "median": 6.66}, "E点成交量": {"mean": 1767926.0, "std": NaN, "min": 1767926.0, "max": 1767926.0, "median": 1767926.0}, "E点实体涨跌幅": {"mean": -4.53, "std": NaN, "min": -4.53, "max": -4.53, "median": -4.53}, "E点价格振幅": {"mean": 8.32, "std": NaN, "min": 8.32, "max": 8.32, "median": 8.32}, "A-B涨幅": {"mean": 89.29, "std": NaN, "min": 89.29, "max": 89.29, "median": 89.29}, "A-B天数": {"mean": 10.0, "std": NaN, "min": 10.0, "max": 10.0, "median": 10.0}, "B-C跌幅": {"mean": 25.9, "std": NaN, "min": 25.9, "max": 25.9, "median": 25.9}, "B-C天数": {"mean": 7.0, "std": NaN, "min": 7.0, "max": 7.0, "median": 7.0}, "C-D涨幅": {"mean": 11.18, "std": NaN, "min": 11.18, "max": 11.18, "median": 11.18}, "C-D天数": {"mean": 4.0, "std": NaN, "min": 4.0, "max": 4.0, "median": 4.0}, "D-E涨幅": {"mean": -11.28, "std": NaN, "min": -11.28, "max": -11.28, "median": -11.28}, "D-E天数": {"mean": 3.0, "std": NaN, "min": 3.0, "max": 3.0, "median": 3.0}, "D点成交量/C-D均量": {"mean": 1.0, "std": NaN, "min": 1.0, "max": 1.0, "median": 1.0}, "D点上影线涨幅": {"mean": 8.25, "std": NaN, "min": 8.25, "max": 8.25, "median": 8.25}, "D点上影线/实体": {"mean": 8.25, "std": NaN, "min": 8.25, "max": 8.25, "median": 8.25}, "E点成交量/C-D均量": {"mean": 0.62, "std": NaN, "min": 0.62, "max": 0.62, "median": 0.62}, "E点成交量/D点成交量": {"mean": 0.62, "std": NaN, "min": 0.62, "max": 0.62, "median": 0.62}, "E点J值": {"mean": 4.18, "std": NaN, "min": 4.18, "max": 4.18, "median": 4.18}, "E点J值相对D点J值涨幅": {"mean": -0.62, "std": NaN, "min": -0.62, "max": -0.62, "median": -0.62}, "E点相对D点收盘价涨幅": {"mean": -0.11, "std": NaN, "min": -0.11, "max": -0.11, "median": -0.11}}, "target_stats": {"mean": 0.0414803050980451, "std": 0.04187156300082011, "high_gain_ratio": 0.08714043993231811}}, "prediction_thresholds": {"high_gain_threshold": 0.1, "min_confidence": 0.12, "min_predicted_gain": 0.1}, "recommendation": {"top_n_stocks": 5, "top_priority_stocks": 3}, "original_config": {"version": "1.0", "description": "股票预测系统配置文件", "paths": {"training_data": "选股分析结果/选股分析结果_20250730_225530.xlsx", "models_dir": "scripts/models", "output_dir": "scripts/output", "temp_dir": "scripts/temp"}, "training": {"selection_rules": {"E点价格振幅": {"threshold": 3.968, "operator": ">=", "precision": 0.193}, "D点上影线/实体": {"threshold": 8.0, "operator": ">=", "precision": 0.172}, "A-B涨幅": {"threshold": 78.62, "operator": ">=", "precision": 0.157}, "E点实体涨跌幅": {"threshold": -2.4, "operator": "<=", "precision": 0.157}}, "feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"], "target_column": "5日最大涨幅", "model_params": {"test_size": 0.2, "random_state": 42}}, "prediction": {"thresholds": {"high_gain_threshold": 0.1, "min_confidence": 0.12, "min_predicted_gain": 0.1}, "recommendation": {"top_n_stocks": 5, "top_priority_stocks": 3}, "actual_gain_columns": ["5日最大涨幅", "实际5日涨幅", "5日涨幅"], "stock_id_columns": ["股票", "股票代码", "股票名称"], "date_columns": ["E点日期", "日期"]}, "output": {"file_naming": {"training_rules": "prediction_rules_{timestamp}.json", "latest_rules": "latest_prediction_rules.json", "prediction_results": "新股票预测结果_{timestamp}.xlsx", "accuracy_report": "预测准确性报告_{timestamp}.txt"}, "excel_columns": {"ranking": "最终排名", "selection_status": "选股状态", "buy_date": "预测买入日期", "predicted_gain": "预测5日涨幅", "prediction_level": "预测涨幅等级", "confidence": "置信度分数", "investment_advice": "投资建议", "recommendation_level": "推荐等级", "actual_gain": "实际5日涨幅", "prediction_error": "预测误差", "prediction_success": "预测成功"}}, "display": {"console_output": {"show_progress": true, "show_statistics": true, "show_top_recommendations": true, "max_display_rows": 20}, "prediction_levels": {"超高潜力": {"min_gain": 0.2, "advice": "🔥 重点关注"}, "高潜力": {"min_gain": 0.15, "advice": "⭐ 积极配置"}, "中高潜力": {"min_gain": 0.12, "advice": "📈 适度配置"}, "中等潜力": {"min_gain": 0.08, "advice": "⚠️ 谨慎观察"}, "低潜力": {"min_gain": 0.0, "advice": "❌ 避免投资"}}}, "validation": {"required_features_ratio": 0.8, "max_missing_values_ratio": 0.1, "min_data_rows": 10}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s", "file": "scripts/logs/prediction_system.log"}}}