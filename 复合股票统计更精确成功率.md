# 查看帮助
python run_stock_analysis.py

# 使用默认参数
python run_stock_analysis.py "选股分析结果/选股分析结果_20250804_222043.xlsx"

# 自定义分析参数
python run_stock_analysis.py "data.xlsx" --max-days-gap 30 --success-threshold 8.0

# 指定输出目录
python run_stock_analysis.py "data.xlsx" --output-dir "./analysis_results"

python run_stock_analysis.py "C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250804_2025-01-01-2025-07-15.xlsx" --max-days-gap 15 --success-threshold 10.0