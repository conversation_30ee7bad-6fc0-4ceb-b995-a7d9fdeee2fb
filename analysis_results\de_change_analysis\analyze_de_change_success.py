#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多条件股票筛选与成功率分析系统
支持D-E点涨跌幅、J值、成交量等多种条件的自定义组合分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
import json
from typing import Dict, List, Tuple, Any
warnings.filterwarnings('ignore')

# 设置字体 - 使用英文避免中文渲染问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class MultiConditionAnalyzer:
    """多条件股票筛选与分析器"""

    def __init__(self):
        self.available_features = [
            "D点收盘", "E点收盘", "D-E涨跌幅",
            "E点J值", "D点J值", "E点J值相对D点J值涨幅",
            "E点成交量", "D点成交量", "E点成交量/D点成交量",
            "A点实体涨跌幅", "B点实体涨跌幅", "C点实体涨跌幅", "D点实体涨跌幅", "E点实体涨跌幅",
            "A点价格振幅", "B点价格振幅", "C点价格振幅", "D点价格振幅", "E点价格振幅",
            "A-B涨幅", "B-C跌幅", "C-D涨幅", "D-E涨幅",
            "A-B天数", "B-C天数", "C-D天数", "D-E天数",
            "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量"
        ]

        self.predefined_conditions = {
            "de_decline_strong": {
                "name": "D-E大幅下跌",
                "description": "D-E点涨跌幅在-15%~-5%区间",
                "conditions": [{"feature": "D-E涨跌幅", "operator": "between", "value": [-15, -5]}]
            },
            "de_decline_moderate": {
                "name": "D-E中度下跌",
                "description": "D-E点涨跌幅在-5%~-1%区间",
                "conditions": [{"feature": "D-E涨跌幅", "operator": "between", "value": [-5, -1]}]
            },
            "de_rise_small": {
                "name": "D-E小幅上涨",
                "description": "D-E点涨跌幅在0%~3%区间",
                "conditions": [{"feature": "D-E涨跌幅", "operator": "between", "value": [0, 3]}]
            },
            "j_value_stable": {
                "name": "J值相对稳定",
                "description": "E点J值相对D点J值涨幅在-5%~0%区间",
                "conditions": [{"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": [-5, 0]}]
            },
            "volume_increase": {
                "name": "成交量放大",
                "description": "E点成交量相对D点成交量增长>50%",
                "conditions": [{"feature": "E点成交量/D点成交量", "operator": ">", "value": 1.5}]
            },
            "combined_optimal": {
                "name": "组合最优条件",
                "description": "D-E大幅下跌 + J值稳定 + 成交量适中",
                "conditions": [
                    {"feature": "D-E涨跌幅", "operator": "between", "value": [-15, -5]},
                    {"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": [-5, 0]},
                    {"feature": "E点成交量/D点成交量", "operator": "between", "value": [0.8, 2.0]}
                ]
            }
        }

    def load_data(self, data_file: str) -> pd.DataFrame:
        """加载数据并预处理"""
        print(f"📂 加载数据: {data_file}")
        df = pd.read_excel(data_file)
        print(f"   总数据: {len(df)} 条记录")

        # 计算D-E涨跌幅（如果不存在）
        if "D-E涨跌幅" not in df.columns and "D点收盘" in df.columns and "E点收盘" in df.columns:
            d_prices = pd.to_numeric(df['D点收盘'], errors='coerce')
            e_prices = pd.to_numeric(df['E点收盘'], errors='coerce')
            df['D-E涨跌幅'] = (e_prices - d_prices) / d_prices * 100
            print("   ✅ 自动计算D-E涨跌幅")

        return df

    def apply_condition(self, df: pd.DataFrame, condition: Dict[str, Any]) -> pd.Series:
        """应用单个筛选条件"""
        feature = condition["feature"]
        operator = condition["operator"]
        value = condition["value"]

        if feature not in df.columns:
            print(f"⚠️ 特征列 '{feature}' 不存在")
            return pd.Series([False] * len(df))

        # 预处理特征值
        feature_values = df[feature].copy()

        # 处理百分比格式
        if feature_values.dtype == 'object':
            feature_values = pd.to_numeric(feature_values.astype(str).str.replace('%', ''), errors='coerce')

        # 应用条件
        if operator == ">":
            mask = feature_values > value
        elif operator == ">=":
            mask = feature_values >= value
        elif operator == "<":
            mask = feature_values < value
        elif operator == "<=":
            mask = feature_values <= value
        elif operator == "==":
            mask = feature_values == value
        elif operator == "between":
            mask = (feature_values >= value[0]) & (feature_values <= value[1])
        elif operator == "not_between":
            mask = (feature_values < value[0]) | (feature_values > value[1])
        else:
            print(f"⚠️ 不支持的操作符: {operator}")
            mask = pd.Series([False] * len(df))

        return mask.fillna(False)

    def apply_conditions(self, df: pd.DataFrame, conditions: List[Dict[str, Any]], logic: str = "and") -> pd.Series:
        """应用多个筛选条件"""
        if not conditions:
            return pd.Series([True] * len(df))

        masks = []
        for condition in conditions:
            mask = self.apply_condition(df, condition)
            masks.append(mask)

        if logic == "and":
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask & mask
        elif logic == "or":
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask | mask
        else:
            print(f"⚠️ 不支持的逻辑操作: {logic}")
            final_mask = pd.Series([False] * len(df))

        return final_mask

def analyze_multi_conditions(data_file: str = "../../选股分析结果/2025-05-01-2025-06-01.xlsx",
                           custom_conditions: List[Dict[str, Any]] = None,
                           condition_name: str = "自定义条件"):
    """多条件分析主函数"""
    print("📊 多条件股票筛选与成功率分析系统")
    print("=" * 80)

    # 创建分析器
    analyzer = MultiConditionAnalyzer()

    # 加载数据
    df = analyzer.load_data(data_file)

    # 检查目标列
    if '5日成功选股' not in df.columns:
        print("❌ 未找到目标列: 5日成功选股")
        return

    # 如果没有提供自定义条件，使用预定义条件进行分析
    if custom_conditions is None:
        print("\n🔍 使用预定义条件进行分析...")
        analyze_predefined_conditions(analyzer, df)
    else:
        print(f"\n🔍 分析自定义条件: {condition_name}")
        analyze_custom_conditions(analyzer, df, custom_conditions, condition_name)

def analyze_predefined_conditions(analyzer: MultiConditionAnalyzer, df: pd.DataFrame):
    """分析预定义条件"""
    print("\n📋 预定义筛选条件:")
    for key, condition_set in analyzer.predefined_conditions.items():
        print(f"   {key}: {condition_set['name']} - {condition_set['description']}")

    print("\n" + "=" * 80)

    results = []

    for key, condition_set in analyzer.predefined_conditions.items():
        print(f"\n🎯 分析条件: {condition_set['name']}")
        print(f"   描述: {condition_set['description']}")

        # 应用筛选条件
        mask = analyzer.apply_conditions(df, condition_set['conditions'])
        filtered_df = df[mask]

        if len(filtered_df) == 0:
            print("   ❌ 没有符合条件的数据")
            continue

        # 计算成功率
        success_mask = filtered_df['5日成功选股'] == "成功"
        success_count = success_mask.sum()
        total_count = len(filtered_df)
        success_rate = success_count / total_count if total_count > 0 else 0

        print(f"   📊 筛选结果:")
        print(f"      符合条件: {total_count} 只股票")
        print(f"      成功案例: {success_count} 只")
        print(f"      成功率: {success_rate:.1%}")

        # 分析成功案例的实际涨幅
        if success_count > 0 and '5日最大涨幅' in filtered_df.columns:
            success_cases = filtered_df[success_mask]
            actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
            if len(actual_gains) > 0:
                print(f"      平均涨幅: {actual_gains.mean():.1%}")
                print(f"      中位数涨幅: {actual_gains.median():.1%}")
                print(f"      最大涨幅: {actual_gains.max():.1%}")

        results.append({
            'condition': condition_set['name'],
            'total_count': total_count,
            'success_count': success_count,
            'success_rate': success_rate
        })

    # 汇总结果
    print(f"\n📊 条件筛选结果汇总:")
    print("-" * 80)
    print(f"{'条件名称':<20} {'符合数量':<10} {'成功数量':<10} {'成功率':<10}")
    print("-" * 80)

    # 按成功率排序
    results.sort(key=lambda x: x['success_rate'], reverse=True)

    for result in results:
        print(f"{result['condition']:<20} {result['total_count']:<10} {result['success_count']:<10} {result['success_rate']:<10.1%}")

def analyze_custom_conditions(analyzer: MultiConditionAnalyzer, df: pd.DataFrame,
                            conditions: List[Dict[str, Any]], condition_name: str):
    """分析自定义条件"""
    print(f"   条件详情:")
    for i, condition in enumerate(conditions):
        feature = condition.get('feature', 'N/A')
        operator = condition.get('operator', 'N/A')
        value = condition.get('value', 'N/A')
        print(f"      {i+1}. {feature} {operator} {value}")

    # 应用筛选条件
    mask = analyzer.apply_conditions(df, conditions)
    filtered_df = df[mask]

    if len(filtered_df) == 0:
        print("   ❌ 没有符合条件的数据")
        return

    # 计算成功率
    success_mask = filtered_df['5日成功选股'] == "成功"
    success_count = success_mask.sum()
    total_count = len(filtered_df)
    success_rate = success_count / total_count if total_count > 0 else 0

    print(f"\n   📊 筛选结果:")
    print(f"      符合条件: {total_count} 只股票")
    print(f"      成功案例: {success_count} 只")
    print(f"      成功率: {success_rate:.1%}")

    # 显示符合条件的股票
    if total_count > 0:
        print(f"\n   📋 符合条件的股票 (前10只):")
        display_cols = ['股票', '买入日期', '5日成功选股', '5日最大涨幅']
        available_cols = [col for col in display_cols if col in filtered_df.columns]

        for i, (idx, row) in enumerate(filtered_df.head(10).iterrows()):
            stock_info = " | ".join([f"{col}: {row.get(col, 'N/A')}" for col in available_cols])
            print(f"      {i+1}. {stock_info}")

    # 分析成功案例的实际涨幅
    if success_count > 0 and '5日最大涨幅' in filtered_df.columns:
        success_cases = filtered_df[success_mask]
        actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
        if len(actual_gains) > 0:
            print(f"\n   📈 成功案例涨幅分析:")
            print(f"      平均涨幅: {actual_gains.mean():.1%}")
            print(f"      中位数涨幅: {actual_gains.median():.1%}")
            print(f"      最大涨幅: {actual_gains.max():.1%}")
            print(f"      最小涨幅: {actual_gains.min():.1%}")

def analyze_de_change_success():
    """原始D-E点涨跌幅分析（保持向后兼容）"""
    print("📊 D-E点涨跌幅与5日选股成功率分析")
    print("=" * 80)

    # 使用多条件分析器进行D-E点分析
    analyzer = MultiConditionAnalyzer()
    data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
    df = analyzer.load_data(data_file)

    # 检查关键列
    if '5日成功选股' not in df.columns:
        print("❌ 未找到目标列: 5日成功选股")
        return

    # 过滤有效数据
    if "D-E涨跌幅" in df.columns:
        valid_mask = pd.notna(df["D-E涨跌幅"])
        valid_df = df[valid_mask].copy()
        valid_df['de_change'] = df["D-E涨跌幅"]
    else:
        print("❌ 未找到D-E涨跌幅列")
        return

    print(f"✅ 有效数据: {len(valid_df)} 条")

    # 基本统计
    de_change = valid_df['de_change']
    print(f"\n📈 D-E点涨跌幅基本统计:")
    print(f"   平均涨跌幅: {de_change.mean():.2f}%")
    print(f"   中位数涨跌幅: {de_change.median():.2f}%")
    print(f"   标准差: {de_change.std():.2f}%")
    print(f"   最大涨幅: {de_change.max():.2f}%")
    print(f"   最大跌幅: {de_change.min():.2f}%")

    # 统计成功率
    success_mask = valid_df['5日成功选股'] == "成功"
    total_success = success_mask.sum()
    total_samples = len(valid_df)
    overall_success_rate = total_success / total_samples

    print(f"\n📊 整体成功率:")
    print(f"   总样本: {total_samples}")
    print(f"   成功案例: {total_success}")
    print(f"   整体成功率: {overall_success_rate:.1%}")

    # 按涨跌幅区间分析成功率
    analyze_by_change_ranges(valid_df)

    # 按涨跌幅百分位数分析
    analyze_by_percentiles(valid_df)

    # 寻找最优涨跌幅区间
    find_optimal_ranges(valid_df)

    # 可视化分析
    create_visualizations(valid_df)

def analyze_by_change_ranges(valid_df):
    """按涨跌幅区间分析成功率"""
    print(f"\n📊 按D-E涨跌幅区间分析成功率:")
    print("-" * 70)
    
    # 定义更详细的区间，包含15%和10%范围
    ranges = [
        ("极大下跌", -float('inf'), -15),
        ("很大下跌", -15, -10),
        ("大幅下跌", -10, -5),
        ("中度下跌", -5, -3),
        ("小幅下跌", -3, -1),
        ("微幅下跌", -1, 0),
        ("持平", 0, 0),
        ("微幅上涨", 0, 1),
        ("小幅上涨", 1, 3),
        ("中度上涨", 3, 5),
        ("大幅上涨", 5, 10),
        ("很大上涨", 10, 15),
        ("极大上涨", 15, float('inf'))
    ]
    
    print(f"{'区间':<12} {'涨跌幅范围':<15} {'总数量':<8} {'成功数量':<8} {'成功率':<8}")
    print("-" * 70)
    
    range_stats = []
    
    for range_name, min_val, max_val in ranges:
        if min_val == -float('inf'):
            mask = valid_df['de_change'] <= max_val
            range_str = f"≤{max_val}%"
        elif max_val == float('inf'):
            mask = valid_df['de_change'] > min_val
            range_str = f">{min_val}%"
        elif min_val == max_val:
            mask = valid_df['de_change'] == min_val
            range_str = f"={min_val}%"
        else:
            mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
            range_str = f"{min_val}%~{max_val}%"
        
        range_data = valid_df[mask]
        total_count = len(range_data)
        
        if total_count > 0:
            success_count = (range_data['5日成功选股'] == "成功").sum()
            success_rate = success_count / total_count
            
            print(f"{range_name:<12} {range_str:<15} {total_count:<8} {success_count:<8} {success_rate:<8.1%}")
            
            range_stats.append({
                'range_name': range_name,
                'range_str': range_str,
                'total_count': total_count,
                'success_count': success_count,
                'success_rate': success_rate,
                'min_val': min_val,
                'max_val': max_val
            })
    
    # 找出成功率最高的区间
    if range_stats:
        best_ranges = sorted([r for r in range_stats if r['total_count'] >= 10], 
                           key=lambda x: x['success_rate'], reverse=True)
        
        print(f"\n🏆 成功率最高的区间 (样本≥10):")
        for i, range_info in enumerate(best_ranges[:5]):
            print(f"   {i+1}. {range_info['range_name']} ({range_info['range_str']}): "
                  f"{range_info['success_rate']:.1%} ({range_info['success_count']}/{range_info['total_count']})")

def analyze_by_percentiles(valid_df):
    """按涨跌幅百分位数分析"""
    print(f"\n📊 按D-E涨跌幅百分位数分析:")
    print("-" * 60)
    
    # 计算百分位数
    percentiles = [10, 20, 30, 40, 50, 60, 70, 80, 90]
    thresholds = np.percentile(valid_df['de_change'], percentiles)
    
    print(f"{'百分位':<8} {'阈值':<8} {'≤阈值数量':<10} {'≤阈值成功率':<12} {'>阈值成功率':<12}")
    print("-" * 60)
    
    for percentile, threshold in zip(percentiles, thresholds):
        below_mask = valid_df['de_change'] <= threshold
        above_mask = valid_df['de_change'] > threshold
        
        below_data = valid_df[below_mask]
        above_data = valid_df[above_mask]
        
        below_success_rate = (below_data['5日成功选股'] == "成功").mean() if len(below_data) > 0 else 0
        above_success_rate = (above_data['5日成功选股'] == "成功").mean() if len(above_data) > 0 else 0
        
        print(f"{percentile:<8}% {threshold:<8.2f}% {len(below_data):<10} {below_success_rate:<12.1%} {above_success_rate:<12.1%}")

def find_optimal_ranges(valid_df):
    """寻找最优涨跌幅区间"""
    print(f"\n🎯 寻找最优D-E涨跌幅区间:")
    
    # 测试不同的阈值组合，包含15%和10%范围
    test_ranges = [
        (-20, -15), (-15, -10), (-10, -5), (-5, -3), (-3, -1), (-1, 0),
        (0, 1), (1, 3), (3, 5), (5, 10), (10, 15), (15, 20),
        (-15, -5), (-10, 0), (-5, 0), (-3, 1), (-1, 1), (0, 3), (0, 5), (5, 15),
        (-15, 0), (-10, 5), (-5, 5), (0, 10), (0, 15)
    ]
    
    optimal_ranges = []
    
    for min_val, max_val in test_ranges:
        mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
        range_data = valid_df[mask]
        
        if len(range_data) >= 20:  # 至少20个样本
            success_count = (range_data['5日成功选股'] == "成功").sum()
            success_rate = success_count / len(range_data)
            
            optimal_ranges.append({
                'range': f"{min_val}%~{max_val}%",
                'count': len(range_data),
                'success_count': success_count,
                'success_rate': success_rate
            })
    
    # 按成功率排序
    optimal_ranges.sort(key=lambda x: x['success_rate'], reverse=True)
    
    print(f"   最优区间 (样本≥20, 按成功率排序):")
    for i, range_info in enumerate(optimal_ranges[:10]):
        print(f"   {i+1}. {range_info['range']}: {range_info['success_rate']:.1%} "
              f"({range_info['success_count']}/{range_info['count']})")
    
    # 分析最优区间的特征
    if optimal_ranges:
        best_range = optimal_ranges[0]
        print(f"\n✅ 推荐区间: {best_range['range']}")
        print(f"   成功率: {best_range['success_rate']:.1%}")
        print(f"   样本数: {best_range['count']}")
        
        # 提取该区间的数据进行详细分析
        range_parts = best_range['range'].replace('%', '').split('~')
        min_val, max_val = float(range_parts[0]), float(range_parts[1])
        
        best_mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
        best_data = valid_df[best_mask]
        
        print(f"\n📈 推荐区间详细分析:")
        print(f"   平均D-E涨跌幅: {best_data['de_change'].mean():.2f}%")
        print(f"   中位数D-E涨跌幅: {best_data['de_change'].median():.2f}%")
        
        # 分析成功案例的实际涨幅
        success_cases = best_data[best_data['5日成功选股'] == "成功"]
        if len(success_cases) > 0 and '5日最大涨幅' in success_cases.columns:
            actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
            if len(actual_gains) > 0:
                print(f"   成功案例平均实际涨幅: {actual_gains.mean():.1%}")
                print(f"   成功案例中位数实际涨幅: {actual_gains.median():.1%}")

def create_visualizations(valid_df):
    """创建可视化图表"""
    print(f"\n🎨 生成可视化图表...")
    
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('D-E Change vs 5-Day Success Rate Analysis', fontsize=16, fontweight='bold')
    
    # 1. D-E涨跌幅分布直方图
    ax1 = axes[0, 0]
    success_data = valid_df[valid_df['5日成功选股'] == "成功"]['de_change']
    fail_data = valid_df[valid_df['5日成功选股'] != "成功"]['de_change']
    
    ax1.hist([success_data, fail_data], bins=30, alpha=0.7, 
             label=['Success Cases', 'Failure Cases'], color=['green', 'red'])
    ax1.set_title('D-E Change Distribution Comparison', fontweight='bold')
    ax1.set_xlabel('D-E Change (%)')
    ax1.set_ylabel('Frequency')
    ax1.legend()
    ax1.axvline(0, color='black', linestyle='--', alpha=0.5)
    
    # 2. 按区间统计成功率
    ax2 = axes[0, 1]
    ranges = [(-20, -15), (-15, -10), (-10, -5), (-5, -3), (-3, -1), (-1, 0), (0, 1), (1, 3), (3, 5), (5, 10), (10, 15), (15, 20)]
    range_names = ['<-15%', '-15~-10%', '-10~-5%', '-5~-3%', '-3~-1%', '-1~0%', '0~1%', '1~3%', '3~5%', '5~10%', '10~15%', '>15%']
    success_rates = []
    sample_counts = []
    
    for min_val, max_val in ranges:
        if min_val == -20:
            mask = valid_df['de_change'] <= max_val
        elif max_val == 20:
            mask = valid_df['de_change'] > min_val
        else:
            mask = (valid_df['de_change'] > min_val) & (valid_df['de_change'] <= max_val)
        
        range_data = valid_df[mask]
        if len(range_data) > 0:
            success_rate = (range_data['5日成功选股'] == "成功").mean()
            success_rates.append(success_rate * 100)
            sample_counts.append(len(range_data))
        else:
            success_rates.append(0)
            sample_counts.append(0)
    
    bars = ax2.bar(range_names, success_rates, 
                   color=['red' if x < 10 else 'orange' if x < 15 else 'green' for x in success_rates])
    ax2.set_title('Success Rate by D-E Change Range', fontweight='bold')
    ax2.set_xlabel('D-E Change Range')
    ax2.set_ylabel('Success Rate (%)')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for i, (bar, count) in enumerate(zip(bars, sample_counts)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%\n({count})', ha='center', va='bottom', fontsize=8)
    
    # 3. 样本数量分布
    ax3 = axes[1, 0]
    bars = ax3.bar(range_names, sample_counts, alpha=0.7, color='skyblue')
    ax3.set_title('Sample Count Distribution by Range', fontweight='bold')
    ax3.set_xlabel('D-E Change Range')
    ax3.set_ylabel('Sample Count')
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. 散点图：D-E涨跌幅 vs 实际涨幅
    ax4 = axes[1, 1]
    if '5日最大涨幅' in valid_df.columns:
        actual_gains = pd.to_numeric(valid_df['5日最大涨幅'], errors='coerce')
        success_mask = valid_df['5日成功选股'] == "成功"
        
        # 成功案例
        ax4.scatter(valid_df[success_mask]['de_change'], 
                   actual_gains[success_mask] * 100,
                   alpha=0.6, color='green', label='Success Cases', s=20)
        
        # 失败案例
        ax4.scatter(valid_df[~success_mask]['de_change'], 
                   actual_gains[~success_mask] * 100,
                   alpha=0.6, color='red', label='Failure Cases', s=20)
        
        ax4.set_title('D-E Change vs Actual 5-Day Gain', fontweight='bold')
        ax4.set_xlabel('D-E Change (%)')
        ax4.set_ylabel('5-Day Actual Gain (%)')
        ax4.legend()
        ax4.axhline(10, color='blue', linestyle='--', alpha=0.5, label='10% Baseline')
        ax4.axvline(0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = "de_change_success_analysis.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {output_file}")
    
    plt.show()

def interactive_analysis():
    """交互式分析"""
    print("🎯 多条件股票筛选与成功率分析系统")
    print("=" * 80)
    print("选择分析模式:")
    print("1. 预定义条件分析")
    print("2. 自定义条件分析")
    print("3. 原始D-E点分析")
    print("4. 批量条件测试")

    choice = input("\n请输入选择 (1-4): ").strip()

    data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"

    if choice == "1":
        analyze_multi_conditions(data_file)
    elif choice == "2":
        custom_conditions = create_custom_conditions()
        if custom_conditions:
            analyze_multi_conditions(data_file, custom_conditions, "用户自定义条件")
    elif choice == "3":
        analyze_de_change_success()
    elif choice == "4":
        batch_condition_test(data_file)
    else:
        print("❌ 无效选择，使用预定义条件分析")
        analyze_multi_conditions(data_file)

def create_custom_conditions() -> List[Dict[str, Any]]:
    """创建自定义条件"""
    print("\n🔧 创建自定义筛选条件")
    print("可用特征列:")

    analyzer = MultiConditionAnalyzer()
    for i, feature in enumerate(analyzer.available_features, 1):
        print(f"   {i:2d}. {feature}")

    conditions = []

    while True:
        print(f"\n当前已添加 {len(conditions)} 个条件")
        print("1. 添加新条件")
        print("2. 完成并开始分析")
        print("3. 取消")

        choice = input("请选择 (1-3): ").strip()

        if choice == "1":
            condition = create_single_condition(analyzer.available_features)
            if condition:
                conditions.append(condition)
                print(f"✅ 已添加条件: {condition['feature']} {condition['operator']} {condition['value']}")
        elif choice == "2":
            if conditions:
                return conditions
            else:
                print("❌ 请至少添加一个条件")
        elif choice == "3":
            return None
        else:
            print("❌ 无效选择")

def create_single_condition(available_features: List[str]) -> Dict[str, Any]:
    """创建单个条件"""
    try:
        # 选择特征
        print("\n选择特征:")
        feature_input = input("输入特征名称或编号: ").strip()

        if feature_input.isdigit():
            idx = int(feature_input) - 1
            if 0 <= idx < len(available_features):
                feature = available_features[idx]
            else:
                print("❌ 编号超出范围")
                return None
        else:
            if feature_input in available_features:
                feature = feature_input
            else:
                print("❌ 特征不存在")
                return None

        # 选择操作符
        print("\n选择操作符:")
        print("1. > (大于)")
        print("2. >= (大于等于)")
        print("3. < (小于)")
        print("4. <= (小于等于)")
        print("5. == (等于)")
        print("6. between (区间)")

        op_choice = input("请选择 (1-6): ").strip()
        op_map = {"1": ">", "2": ">=", "3": "<", "4": "<=", "5": "==", "6": "between"}

        if op_choice not in op_map:
            print("❌ 无效操作符")
            return None

        operator = op_map[op_choice]

        # 输入值
        if operator == "between":
            min_val = float(input("输入最小值: "))
            max_val = float(input("输入最大值: "))
            value = [min_val, max_val]
        else:
            value = float(input("输入比较值: "))

        return {
            "feature": feature,
            "operator": operator,
            "value": value
        }

    except ValueError:
        print("❌ 输入值格式错误")
        return None
    except Exception as e:
        print(f"❌ 创建条件失败: {e}")
        return None

def batch_condition_test(data_file: str):
    """批量测试条件组合"""
    print("\n🧪 批量条件测试")

    analyzer = MultiConditionAnalyzer()
    df = analyzer.load_data(data_file)

    if '5日成功选股' not in df.columns:
        print("❌ 未找到目标列")
        return

    # 测试不同D-E涨跌幅区间
    de_ranges = [
        [-20, -15], [-15, -10], [-10, -5], [-5, -3], [-3, -1], [-1, 0],
        [0, 1], [1, 3], [3, 5], [5, 10], [10, 15]
    ]

    # 测试不同J值区间
    j_ranges = [
        [-10, -5], [-5, -2], [-2, 0], [0, 2], [2, 5], [5, 10]
    ]

    print("\n📊 批量测试结果:")
    print("-" * 100)
    print(f"{'D-E区间':<15} {'J值区间':<15} {'符合数量':<10} {'成功数量':<10} {'成功率':<10} {'平均涨幅':<10}")
    print("-" * 100)

    best_combinations = []

    for de_range in de_ranges:
        for j_range in j_ranges:
            conditions = [
                {"feature": "D-E涨跌幅", "operator": "between", "value": de_range},
                {"feature": "E点J值相对D点J值涨幅", "operator": "between", "value": j_range}
            ]

            mask = analyzer.apply_conditions(df, conditions)
            filtered_df = df[mask]

            if len(filtered_df) >= 5:  # 至少5个样本
                success_mask = filtered_df['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(filtered_df)
                success_rate = success_count / total_count

                # 计算平均涨幅
                avg_gain = 0
                if success_count > 0 and '5日最大涨幅' in filtered_df.columns:
                    success_cases = filtered_df[success_mask]
                    actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                    if len(actual_gains) > 0:
                        avg_gain = actual_gains.mean()

                de_str = f"{de_range[0]}~{de_range[1]}%"
                j_str = f"{j_range[0]}~{j_range[1]}%"

                print(f"{de_str:<15} {j_str:<15} {total_count:<10} {success_count:<10} {success_rate:<10.1%} {avg_gain:<10.1%}")

                best_combinations.append({
                    'de_range': de_range,
                    'j_range': j_range,
                    'success_rate': success_rate,
                    'total_count': total_count,
                    'success_count': success_count,
                    'avg_gain': avg_gain
                })

    # 显示最佳组合
    if best_combinations:
        best_combinations.sort(key=lambda x: x['success_rate'], reverse=True)

        print(f"\n🏆 最佳条件组合 (前5名):")
        for i, combo in enumerate(best_combinations[:5]):
            de_str = f"{combo['de_range'][0]}~{combo['de_range'][1]}%"
            j_str = f"{combo['j_range'][0]}~{combo['j_range'][1]}%"
            print(f"   {i+1}. D-E: {de_str}, J值: {j_str}")
            print(f"      成功率: {combo['success_rate']:.1%}, 样本: {combo['total_count']}, 平均涨幅: {combo['avg_gain']:.1%}")

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == "interactive":
            interactive_analysis()
        elif mode == "predefined":
            analyze_multi_conditions()
        elif mode == "original":
            analyze_de_change_success()
        else:
            print("使用方法:")
            print("  python script.py interactive  # 交互式模式")
            print("  python script.py predefined   # 预定义条件分析")
            print("  python script.py original     # 原始D-E分析")
    else:
        # 默认运行交互式分析
        interactive_analysis()

if __name__ == "__main__":
    main()
