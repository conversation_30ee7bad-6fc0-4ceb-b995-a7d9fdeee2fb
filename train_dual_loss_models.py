#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双损失函数深度学习股票预测模型训练脚本
包含MLP、残差网络、注意力网络三个模型
使用精确率损失和比例损失的组合
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import roc_auc_score, precision_score, recall_score, f1_score, accuracy_score
import matplotlib.pyplot as plt
from datetime import datetime
import json
import os
import pickle
import warnings
warnings.filterwarnings('ignore')

class StockDataset(Dataset):
    """股票数据集"""
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

class PrecisionMLP(nn.Module):
    """深度精确率导向的MLP模型 - 5层深度网络"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(PrecisionMLP, self).__init__()

        # 更深的网络架构，专注于精确率提升
        self.model = nn.Sequential(
            # 第1层：输入扩展
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第2层：特征学习
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            # 第3层：深度特征提取
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.8),  # 稍微降低dropout

            # 第4层：特征压缩
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.6),

            # 第5层：进一步压缩
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.BatchNorm1d(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.4),

            # 输出层
            nn.Linear(hidden_dim // 4, 2)
        )

    def forward(self, x):
        return self.model(x)

class ResidualBlock(nn.Module):
    """残差块"""
    def __init__(self, dim, dropout_rate=0.3):
        super(ResidualBlock, self).__init__()
        self.block = nn.Sequential(
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim)
        )
        self.relu = nn.ReLU()
        
    def forward(self, x):
        residual = x
        out = self.block(x)
        out += residual
        return self.relu(out)

class ResidualNet(nn.Module):
    """深度残差网络模型 - 增强版"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(ResidualNet, self).__init__()

        # 输入层
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )

        # 更多残差块，增强学习能力
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate),
            ResidualBlock(hidden_dim, dropout_rate * 0.8),  # 逐渐降低dropout
            ResidualBlock(hidden_dim, dropout_rate * 0.6)
        ])

        # 中间压缩层
        self.compression = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5)
        )

        # 额外的残差块用于压缩后的特征
        self.final_blocks = nn.ModuleList([
            ResidualBlock(hidden_dim // 2, dropout_rate * 0.4),
            ResidualBlock(hidden_dim // 2, dropout_rate * 0.3)
        ])

        # 输出层
        self.output_layer = nn.Linear(hidden_dim // 2, 2)

    def forward(self, x):
        x = self.input_layer(x)

        # 通过所有残差块
        for block in self.residual_blocks:
            x = block(x)

        # 压缩特征
        x = self.compression(x)

        # 最终残差块
        for block in self.final_blocks:
            x = block(x)

        return self.output_layer(x)

class AttentionNet(nn.Module):
    """深度多头注意力网络模型 - 增强版"""
    def __init__(self, input_dim, hidden_dim=256, dropout_rate=0.3):
        super(AttentionNet, self).__init__()

        # 深度特征嵌入
        self.feature_embedding = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.8)
        )

        # 增强的多头注意力（8个头）
        self.attention_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 4),
                nn.Tanh(),
                nn.Linear(hidden_dim // 4, 1)
            ) for _ in range(8)  # 增加到8个注意力头
        ])

        # 深度特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),

            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.6)
        )

        # 深度分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),

            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.BatchNorm1d(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.3),

            nn.Linear(hidden_dim // 4, 2)
        )
    
    def forward(self, x):
        # 特征嵌入
        embedded = self.feature_embedding(x)
        
        # 多头注意力
        attention_outputs = []
        for head in self.attention_heads:
            attention_weights = torch.softmax(head(embedded), dim=1)
            attention_output = embedded * attention_weights
            attention_outputs.append(attention_output)
        
        # 注意力融合
        fused_features = torch.stack(attention_outputs, dim=0).mean(dim=0)
        fused_features = self.feature_fusion(fused_features)
        
        return self.classifier(fused_features)

class EnsembleModel(nn.Module):
    """集成模型 - 融合三个模型的预测结果"""
    def __init__(self, input_dim, hidden_dim=128, dropout_rate=0.3):
        super(EnsembleModel, self).__init__()

        # 三个基础模型
        self.precision_mlp = PrecisionMLP(input_dim, hidden_dim, dropout_rate)
        self.residual_net = ResidualNet(input_dim, hidden_dim, dropout_rate)
        self.attention_net = AttentionNet(input_dim, hidden_dim, dropout_rate)

        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(6, 32),  # 3个模型 * 2个输出
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(32, 16),
            nn.BatchNorm1d(16),
            nn.ReLU(),
            nn.Dropout(dropout_rate),

            nn.Linear(16, 2)
        )

    def forward(self, x):
        # 获取三个模型的输出
        mlp_out = self.precision_mlp(x)
        residual_out = self.residual_net(x)
        attention_out = self.attention_net(x)

        # 拼接所有输出
        combined = torch.cat([mlp_out, residual_out, attention_out], dim=1)

        # 通过融合层
        return self.fusion_layer(combined)

    def load_pretrained_models(self, model_paths, device):
        """加载预训练的模型权重"""
        if 'PrecisionMLP' in model_paths:
            self.precision_mlp.load_state_dict(torch.load(model_paths['PrecisionMLP'], map_location=device))
        if 'ResidualNet' in model_paths:
            self.residual_net.load_state_dict(torch.load(model_paths['ResidualNet'], map_location=device))
        if 'AttentionNet' in model_paths:
            self.attention_net.load_state_dict(torch.load(model_paths['AttentionNet'], map_location=device))

        # 冻结预训练模型的参数
        for param in self.precision_mlp.parameters():
            param.requires_grad = False
        for param in self.residual_net.parameters():
            param.requires_grad = False
        for param in self.attention_net.parameters():
            param.requires_grad = False

class DualLossTrainer:
    """双损失函数训练器"""
    
    def __init__(self, config_file="ml_config.json"):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 使用设备: {self.device}")
        
        # 创建输出文件夹
        self.create_output_folder()
        
        # 加载配置
        self.load_config(config_file)
        
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
    
    def create_output_folder(self):
        """创建带时间戳的输出文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.output_folder = f"ml/dual_loss_training_{timestamp}"
        
        # 创建主文件夹和子文件夹
        os.makedirs(self.output_folder, exist_ok=True)
        os.makedirs(f"{self.output_folder}/models", exist_ok=True)
        os.makedirs(f"{self.output_folder}/reports", exist_ok=True)
        os.makedirs(f"{self.output_folder}/plots", exist_ok=True)
        os.makedirs(f"{self.output_folder}/config", exist_ok=True)
        
        print(f"📁 输出文件夹: {self.output_folder}")
        
        return self.output_folder
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            # 使用默认配置
            self.config = {
                "data": {
                    "target_column": "5日成功选股",
                    "stock_column": "股票"
                },
                "training": {"epochs": 200, "batch_size": 32, "learning_rate": 0.001},
                "dual_loss": {"precision_weight": 1.0, "proportion_weight": 0.5, "target_proportion": 0.1}
            }
        
        # 设置属性
        self.feature_columns = self.config["data"]["feature_columns"]
        self.target_column = self.config["data"]["target_column"]
        self.stock_column = self.config["data"]["stock_column"]
    
    def dual_loss_function(self, outputs, targets):
        """增强双损失函数：更激进的精确率优化"""
        # 基础交叉熵损失
        ce_loss = nn.CrossEntropyLoss()(outputs, targets)

        # 获取预测概率
        probs = torch.softmax(outputs, dim=1)[:, 1]  # 正类概率

        # 使用多个阈值进行精确率计算，选择最佳的
        thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
        best_precision = 0
        best_proportion = 0

        for threshold in thresholds:
            predicted_positive = (probs > threshold).float()
            true_positive = (predicted_positive * targets.float()).sum()
            predicted_positive_count = predicted_positive.sum()

            if predicted_positive_count > 0:
                precision = true_positive / predicted_positive_count
                if precision > best_precision:
                    best_precision = precision
                    best_proportion = predicted_positive_count / len(targets)

        # 精确率损失：更激进的优化
        precision_loss = (1.0 - best_precision) ** 2  # 平方损失，更激进

        # 比例损失：允许更大的灵活性
        target_proportion = self.config["dual_loss"]["target_proportion"]
        proportion_loss = abs(best_proportion - target_proportion)

        # 添加置信度损失：鼓励模型对预测更有信心
        confidence_loss = -torch.mean(torch.max(probs, 1 - probs))  # 鼓励极端概率

        # 确保所有损失都是tensor
        if not isinstance(precision_loss, torch.Tensor):
            precision_loss = torch.tensor(precision_loss, device=outputs.device)
        if not isinstance(proportion_loss, torch.Tensor):
            proportion_loss = torch.tensor(proportion_loss, device=outputs.device)
        if not isinstance(best_precision, torch.Tensor):
            best_precision = torch.tensor(best_precision, device=outputs.device)
        if not isinstance(best_proportion, torch.Tensor):
            best_proportion = torch.tensor(best_proportion, device=outputs.device)

        # 组合损失 - 更重视精确率
        precision_weight = self.config["dual_loss"]["precision_weight"]
        proportion_weight = self.config["dual_loss"]["proportion_weight"]
        confidence_weight = 0.1  # 置信度权重

        total_loss = (ce_loss +
                     precision_weight * precision_loss +
                     proportion_weight * proportion_loss +
                     confidence_weight * confidence_loss)

        return total_loss, precision_loss, proportion_loss, best_precision, best_proportion

    def load_and_preprocess_data(self, data_file):
        """加载和预处理数据"""
        print(f"\n📊 数据加载和预处理")
        print("=" * 60)

        # 加载数据
        df = pd.read_excel(data_file)
        print(f"📁 原始数据: {df.shape}")

        # 检查特征列
        missing_features = [col for col in self.feature_columns if col not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features}")
            return None, None, None

        available_features = [col for col in self.feature_columns if col in df.columns]
        print(f"✅ 可用特征: {len(available_features)}/{len(self.feature_columns)}")

        # 提取特征和目标
        X = df[available_features].copy()
        y = df[self.target_column].copy()

        # 处理百分号格式的数据
        for col in X.columns:
            if X[col].dtype == 'object':
                # 尝试转换百分号格式
                try:
                    X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                except:
                    # 如果转换失败，尝试直接转换为数值
                    try:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                    except:
                        pass

        # 处理缺失值
        X = X.fillna(X.median())

        # 移除无效行
        valid_mask = ~(X.isnull().any(axis=1) | y.isnull())
        X = X[valid_mask]
        y = y[valid_mask]

        print(f"🧹 清洗后数据: X={X.shape}, y={y.shape}")

        # 目标变量分布
        y_counts = y.value_counts()
        print(f"📊 目标变量分布: {dict(y_counts)}")

        success_rate = y_counts.get('成功', 0) / len(y) if len(y) > 0 else 0
        print(f"🎯 基础成功率: {success_rate:.2%}")

        return X.values, y.values, success_rate

    def split_data_by_stock(self, df, X, y):
        """按股票分离数据，确保同一股票的数据不会同时出现在训练集和测试集中"""
        print(f"\n📊 按股票分离数据集")
        print("-" * 50)

        # 按股票排序
        df_sorted = df.sort_values(self.stock_column).reset_index(drop=True)

        # 重新排列X和y以匹配排序后的DataFrame
        X_sorted = X[df_sorted.index]
        y_sorted = y[df_sorted.index]

        # 获取唯一股票列表
        unique_stocks = df_sorted[self.stock_column].unique()
        print(f"   📈 总股票数: {len(unique_stocks)}")

        # 按股票分割
        np.random.seed(self.config["data_split"]["random_state"])
        shuffled_stocks = np.random.permutation(unique_stocks)

        test_size = self.config["data_split"]["test_size"]
        val_size = self.config["data_split"]["val_size"]

        n_test_stocks = int(len(unique_stocks) * test_size)
        n_val_stocks = int(len(unique_stocks) * val_size)

        test_stocks = shuffled_stocks[:n_test_stocks]
        val_stocks = shuffled_stocks[n_test_stocks:n_test_stocks + n_val_stocks]
        train_stocks = shuffled_stocks[n_test_stocks + n_val_stocks:]

        print(f"   🎯 训练股票数: {len(train_stocks)}")
        print(f"   🎯 验证股票数: {len(val_stocks)}")
        print(f"   🎯 测试股票数: {len(test_stocks)}")

        # 创建索引掩码
        train_mask = df_sorted[self.stock_column].isin(train_stocks)
        val_mask = df_sorted[self.stock_column].isin(val_stocks)
        test_mask = df_sorted[self.stock_column].isin(test_stocks)

        # 分割数据
        X_train = X_sorted[train_mask]
        X_val = X_sorted[val_mask]
        X_test = X_sorted[test_mask]

        y_train = y_sorted[train_mask]
        y_val = y_sorted[val_mask]
        y_test = y_sorted[test_mask]

        print(f"   ✅ 训练集: {X_train.shape[0]} 样本")
        print(f"   ✅ 验证集: {X_val.shape[0]} 样本")
        print(f"   ✅ 测试集: {X_test.shape[0]} 样本")

        return X_train, X_val, X_test, y_train, y_val, y_test

    def create_data_loader(self, X, y, batch_size, shuffle=True):
        """创建数据加载器"""
        dataset = StockDataset(X, y)

        if shuffle:
            # 计算类别权重
            unique_labels, counts = np.unique(y, return_counts=True)
            class_weights = len(y) / (len(unique_labels) * counts)

            # 创建样本权重
            sample_weights = np.array([class_weights[label] for label in y])
            sampler = WeightedRandomSampler(sample_weights, len(sample_weights))

            return DataLoader(dataset, batch_size=batch_size, sampler=sampler)
        else:
            return DataLoader(dataset, batch_size=batch_size, shuffle=False)

    def train_model(self, model, train_loader, val_loader, model_name):
        """训练单个模型"""
        print(f"\n🔄 训练 {model_name}")
        print("-" * 50)

        model.to(self.device)
        optimizer = optim.Adam(
            model.parameters(),
            lr=self.config["training"]["learning_rate"],
            weight_decay=self.config["training"]["weight_decay"]
        )
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            patience=self.config["training"]["lr_scheduler_patience"],
            factor=self.config["training"]["lr_scheduler_factor"]
        )

        best_val_score = 0
        best_model_state = None
        patience_counter = 0

        train_losses = []
        val_scores = []

        for epoch in range(self.config["training"]["epochs"]):
            # 训练阶段
            model.train()
            epoch_loss = 0
            epoch_precision_loss = 0
            epoch_proportion_loss = 0
            epoch_precision = 0
            epoch_proportion = 0

            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)

                optimizer.zero_grad()
                outputs = model(batch_X)

                # 计算增强双损失
                total_loss, precision_loss, proportion_loss, precision, proportion = self.dual_loss_function(
                    outputs, batch_y
                )

                total_loss.backward()
                optimizer.step()

                epoch_loss += total_loss.item()
                epoch_precision_loss += precision_loss.item()
                epoch_proportion_loss += proportion_loss.item()
                epoch_precision += precision.item()
                epoch_proportion += proportion.item()

            # 验证阶段
            model.eval()
            val_precision = 0
            val_proportion = 0
            val_count = 0

            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_X)

                    probs = torch.softmax(outputs, dim=1)[:, 1]
                    predicted_positive = (probs > 0.5).float()
                    true_positive = (predicted_positive * batch_y.float()).sum()
                    predicted_positive_count = predicted_positive.sum()

                    if predicted_positive_count > 0:
                        val_precision += (true_positive / predicted_positive_count).item()
                    val_proportion += (predicted_positive_count / len(batch_y)).item()
                    val_count += 1

            val_precision /= val_count
            val_proportion /= val_count

            # 综合评分：精确率 - |比例偏差|
            target_proportion = self.config["dual_loss"]["target_proportion"]
            val_score = val_precision - abs(val_proportion - target_proportion)

            train_losses.append(epoch_loss / len(train_loader))
            val_scores.append(val_score)

            scheduler.step(epoch_loss / len(train_loader))

            if epoch % 20 == 0:
                print(f"Epoch {epoch:3d}: Loss: {epoch_loss/len(train_loader):.4f}, "
                      f"Val精确率: {val_precision:.3f}, Val比例: {val_proportion:.3f}, "
                      f"Val评分: {val_score:.3f}")

            # 早停检查
            if val_score > best_val_score:
                best_val_score = val_score
                best_model_state = model.state_dict().copy()
                # 保存最佳模型
                model_path = f'{self.output_folder}/models/best_{model_name.lower()}_model.pth'
                torch.save(model.state_dict(), model_path)
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= self.config["training"]["early_stopping_patience"]:
                print(f"Early stopping at epoch {epoch}")
                break

        # 恢复最佳模型
        if best_model_state is not None:
            model.load_state_dict(best_model_state)

        return model, {'train_losses': train_losses, 'val_scores': val_scores, 'best_score': best_val_score}

    def evaluate_model(self, model, X_test, y_test, model_name, base_success_rate):
        """评估模型"""
        print(f"\n📊 {model_name} 评估结果:")

        model.eval()
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        y_test_tensor = torch.LongTensor(y_test).to(self.device)

        with torch.no_grad():
            outputs = model(X_test_tensor)
            probabilities = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()

        # 寻找最佳阈值
        best_score = 0
        best_threshold = 0.5
        best_metrics = {}

        for threshold in np.arange(0.3, 0.8, 0.05):
            predictions = (probabilities > threshold).astype(int)

            if predictions.sum() == 0:
                continue

            precision = precision_score(y_test, predictions, zero_division=0)
            proportion = predictions.sum() / len(predictions)
            target_proportion = self.config["dual_loss"]["target_proportion"]

            # 综合评分
            score = precision - abs(proportion - target_proportion)

            if score > best_score:
                best_score = score
                best_threshold = threshold
                best_metrics = {
                    'threshold': threshold,
                    'predictions_count': predictions.sum(),
                    'precision': precision,
                    'recall': recall_score(y_test, predictions, zero_division=0),
                    'f1': f1_score(y_test, predictions, zero_division=0),
                    'accuracy': accuracy_score(y_test, predictions),
                    'auc': roc_auc_score(y_test, probabilities),
                    'proportion': proportion,
                    'proportion_deviation': abs(proportion - target_proportion),
                    'score': score
                }

        if best_metrics:
            print(f"   最佳阈值: {best_metrics['threshold']:.3f}")
            print(f"   预测数量: {best_metrics['predictions_count']} / {len(y_test)}")
            print(f"   精确率: {best_metrics['precision']:.2%}")
            print(f"   召回率: {best_metrics['recall']:.2%}")
            print(f"   F1分数: {best_metrics['f1']:.4f}")
            print(f"   准确率: {best_metrics['accuracy']:.2%}")
            print(f"   AUC: {best_metrics['auc']:.4f}")
            print(f"   预测比例: {best_metrics['proportion']:.2%} (目标: {target_proportion:.1%})")
            print(f"   比例偏差: {best_metrics['proportion_deviation']:.3f}")
            print(f"   综合评分: {best_metrics['score']:.3f}")

            return best_metrics
        else:
            print("   ❌ 无法找到有效的预测结果")
            return None

    def save_training_artifacts(self, results, base_success_rate, training_histories):
        """保存训练相关文件"""
        print(f"\n💾 保存训练文件...")

        # 保存配置文件
        config_path = f'{self.output_folder}/config/ml_config.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        print(f"   ✅ 配置文件: {config_path}")

        # 保存预处理器
        scaler_path = f'{self.output_folder}/models/scaler.pkl'
        with open(scaler_path, 'wb') as f:
            pickle.dump(self.scaler, f)
        print(f"   ✅ 标准化器: {scaler_path}")

        label_encoder_path = f'{self.output_folder}/models/label_encoder.pkl'
        with open(label_encoder_path, 'wb') as f:
            pickle.dump(self.label_encoder, f)
        print(f"   ✅ 标签编码器: {label_encoder_path}")

        # 保存模型评估结果
        results_path = f'{self.output_folder}/reports/evaluation_results.json'

        # 转换结果为可序列化格式
        serializable_results = {}
        for model_name, result in results.items():
            if result is not None:
                serializable_results[model_name] = {
                    'threshold': float(result['threshold']),
                    'predictions_count': int(result['predictions_count']),
                    'precision': float(result['precision']),
                    'recall': float(result['recall']),
                    'f1': float(result['f1']),
                    'accuracy': float(result['accuracy']),
                    'auc': float(result['auc']),
                    'proportion': float(result['proportion']),
                    'proportion_deviation': float(result['proportion_deviation']),
                    'score': float(result['score'])
                }

        # 添加训练信息
        training_info = {
            'training_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'base_success_rate': float(base_success_rate),
            'output_folder': self.output_folder,
            'model_results': serializable_results,
            'training_histories': training_histories
        }

        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(training_info, f, ensure_ascii=False, indent=2)
        print(f"   ✅ 评估结果: {results_path}")

        # 创建模型使用说明
        readme_path = f'{self.output_folder}/README.md'
        readme_content = f"""# 双损失函数深度学习股票预测模型训练结果

## 训练信息
- **训练时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **基础成功率**: {base_success_rate:.2%}
- **目标预测比例**: {self.config['dual_loss']['target_proportion']:.1%}
- **输出文件夹**: {self.output_folder}

## 模型架构
1. **PrecisionMLP**: 多层感知机，专注精确率优化
2. **ResidualNet**: 残差网络，使用跳跃连接防止梯度消失
3. **AttentionNet**: 注意力网络，多头注意力机制

## 双损失函数
1. **精确率损失**: 鼓励预测为成功且实际为成功的比例
2. **比例损失**: 控制预测为成功的股票占总数的比例接近{self.config['dual_loss']['target_proportion']:.1%}

## 文件结构
```
{self.output_folder}/
├── models/                 # 模型文件
│   ├── best_*_model.pth   # 训练好的模型
│   ├── scaler.pkl         # 标准化器
│   └── label_encoder.pkl  # 标签编码器
├── reports/               # 报告文件
│   └── evaluation_results.json     # 评估结果JSON
├── config/                # 配置文件
│   └── ml_config.json     # 训练配置
└── README.md              # 说明文件
```

## 模型性能
"""

        # 添加模型性能信息
        if serializable_results:
            best_model = max(serializable_results.keys(), key=lambda x: serializable_results[x]['score'])
            best_result = serializable_results[best_model]

            readme_content += f"""
### 最佳模型: {best_model}
- **精确率**: {best_result['precision']:.2%}
- **预测数量**: {best_result['predictions_count']}个
- **预测比例**: {best_result['proportion']:.2%} (目标: {self.config['dual_loss']['target_proportion']:.1%})
- **比例偏差**: {best_result['proportion_deviation']:.3f}
- **综合评分**: {best_result['score']:.3f}

### 所有模型对比
| 模型 | 精确率 | 预测数量 | 预测比例 | 比例偏差 | 综合评分 |
|------|--------|----------|----------|----------|----------|
"""
            for model_name, result in serializable_results.items():
                readme_content += f"| {model_name} | {result['precision']:.2%} | {result['predictions_count']} | {result['proportion']:.2%} | {result['proportion_deviation']:.3f} | {result['score']:.3f} |\n"

        readme_content += f"""

## 使用方法

### 进行预测
```bash
python predict_dual_loss.py --input "新数据.xlsx" --output "预测结果.xlsx" --model_folder "{self.output_folder}"
```

### 注意事项
- 模型同时优化精确率和预测比例
- 预测结果仅供参考，投资需谨慎
- 建议结合其他分析方法综合判断
"""

        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"   ✅ 说明文件: {readme_path}")

        return self.output_folder

    def generate_prediction_report(self, trained_models, df_full, X_test_scaled, y_test, test_indices):
        """生成各个模型预测结果报告"""
        print(f"\n📊 生成模型预测结果报告...")

        # 确保reports文件夹存在
        reports_folder = f'{self.output_folder}/reports'
        os.makedirs(reports_folder, exist_ok=True)

        # 获取测试集对应的原始数据
        test_df = df_full.iloc[test_indices].copy()

        # 需要的列
        required_columns = ['股票', '买入日期', '5日成功选股',
                           '5日最大涨幅', '5日最大跌幅']

        # 检查列是否存在
        missing_columns = [col for col in required_columns if col not in test_df.columns]
        if missing_columns:
            print(f"❌ 缺失列: {missing_columns}")
            return None

        # 创建基础报告数据
        report_data = test_df[required_columns].copy()

        # 将涨跌幅转换为百分比格式
        for col in ['5日最大涨幅', '5日最大跌幅']:
            if col in report_data.columns:
                # 将数值转换为百分比字符串
                report_data[col] = report_data[col].apply(lambda x: f"{x*100:.2f}%" if pd.notna(x) else "N/A")

        # 为每个模型添加预测结果
        model_predictions = {}

        for model_name, model in trained_models.items():
            print(f"   🔄 生成 {model_name} 预测结果...")

            model.eval()
            X_test_tensor = torch.FloatTensor(X_test_scaled).to(self.device)

            with torch.no_grad():
                outputs = model(X_test_tensor)
                probabilities = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()

            # 使用最佳阈值进行预测
            threshold = 0.5  # 可以从之前的评估结果中获取最佳阈值
            predictions = (probabilities > threshold).astype(int)

            # 将预测结果转换为成功/失败
            prediction_labels = ['成功' if pred == 1 else '失败' for pred in predictions]

            # 添加到报告数据
            report_data[f'{model_name}_预测'] = prediction_labels
            model_predictions[model_name] = predictions

        # 计算股票交集（所有模型都预测为成功的股票）
        all_models = list(model_predictions.keys())

        # 只添加指定的模型组合
        from itertools import combinations

        # 单个模型
        for model in all_models:
            combo_name = f"{model}_选中"
            combo_predictions = []
            for idx in range(len(report_data)):
                success = model_predictions[model][idx] == 1
                combo_predictions.append('是' if success else '否')
            report_data[combo_name] = combo_predictions

        # 三个原始模型组合（不包括EnsembleModel）
        original_models = ['PrecisionMLP', 'ResidualNet', 'AttentionNet']
        combo_name = "PrecisionMLP&ResidualNet&AttentionNet_选中"
        combo_predictions = []
        for idx in range(len(report_data)):
            all_success = all(model_predictions[model][idx] == 1 for model in original_models)
            combo_predictions.append('是' if all_success else '否')
        report_data[combo_name] = combo_predictions

        # 所有四个模型组合
        combo_name = "PrecisionMLP&ResidualNet&AttentionNet&EnsembleModel_选中"
        combo_predictions = []
        for idx in range(len(report_data)):
            all_success = all(model_predictions[model][idx] == 1 for model in all_models)
            combo_predictions.append('是' if all_success else '否')
        report_data[combo_name] = combo_predictions

        # 添加实际成功情况
        actual_success_3d = (test_df['5日成功选股'] == '成功').astype(int)

        # 计算各模型的准确性统计
        accuracy_stats = {}
        for model_name in all_models:
            model_pred = model_predictions[model_name]

            # 5日成功选股准确率
            acc_3d = (model_pred == actual_success_3d).mean()

            # 预测为成功的股票中实际成功的比例（精确率）
            if model_pred.sum() > 0:
                precision_3d = (model_pred * actual_success_3d).sum() / model_pred.sum()
            else:
                precision_3d = 0

            accuracy_stats[model_name] = {
                '预测数量': model_pred.sum(),
                '5日准确率': acc_3d,
                '5日精确率': precision_3d
            }

        # 计算每行被模型组合选中的次数并排序（用于Excel保存）
        combo_cols = [col for col in report_data.columns if '_选中' in col]

        # 计算每行被选中的总次数
        report_data['被选中次数'] = 0
        for col in combo_cols:
            report_data['被选中次数'] += (report_data[col] == '是').astype(int)

        # 按被选中次数从高到低排序，次数相同的按股票代码排序
        report_data_sorted = report_data.sort_values(['被选中次数', '股票'], ascending=[False, True]).reset_index(drop=True)

        # 保存详细预测结果到Excel
        excel_path = f'{reports_folder}/model_predictions_detail.xlsx'
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 主要预测结果（排序后）
            report_data_sorted.to_excel(writer, sheet_name='预测结果', index=False)

            # 准确性统计
            accuracy_df = pd.DataFrame(accuracy_stats).T
            accuracy_df.to_excel(writer, sheet_name='模型准确性统计')

            # 模型组合统计
            combo_stats = []

            # 单个模型统计
            for model in all_models:
                combo_col = f"{model}_选中"
                selected_count = (report_data_sorted[combo_col] == '是').sum()

                # 计算该组合选中的股票中实际成功的比例
                selected_mask = report_data_sorted[combo_col] == '是'
                if selected_count > 0:
                    success_3d_rate = (report_data_sorted.loc[selected_mask, '5日成功选股'] == '成功').mean()
                else:
                    success_3d_rate = 0

                combo_stats.append({
                    '模型组合': model,
                    '模型数量': 1,
                    '选中股票数': selected_count,
                    '5日成功率': success_3d_rate
                })

            # 三个原始模型组合
            combo_col = "PrecisionMLP&ResidualNet&AttentionNet_选中"
            selected_count = (report_data_sorted[combo_col] == '是').sum()
            selected_mask = report_data_sorted[combo_col] == '是'
            if selected_count > 0:
                success_3d_rate = (report_data_sorted.loc[selected_mask, '5日成功选股'] == '成功').mean()
            else:
                success_3d_rate = 0

            combo_stats.append({
                '模型组合': 'PrecisionMLP&ResidualNet&AttentionNet',
                '模型数量': 3,
                '选中股票数': selected_count,
                '5日成功率': success_3d_rate
            })

            # 所有四个模型组合
            combo_col = "PrecisionMLP&ResidualNet&AttentionNet&EnsembleModel_选中"
            selected_count = (report_data_sorted[combo_col] == '是').sum()
            selected_mask = report_data_sorted[combo_col] == '是'
            if selected_count > 0:
                success_3d_rate = (report_data_sorted.loc[selected_mask, '5日成功选股'] == '成功').mean()
            else:
                success_3d_rate = 0

            combo_stats.append({
                '模型组合': 'PrecisionMLP&ResidualNet&AttentionNet&EnsembleModel',
                '模型数量': 4,
                '选中股票数': selected_count,
                '5日成功率': success_3d_rate
            })

            combo_stats_df = pd.DataFrame(combo_stats)
            combo_stats_df.to_excel(writer, sheet_name='模型组合统计', index=False)

        print(f"   ✅ 详细预测结果已保存: {excel_path}")

        # 生成HTML报告
        html_path = f'{reports_folder}/prediction_report.html'
        self._generate_prediction_html(report_data_sorted, accuracy_stats, combo_stats_df, html_path)

        return excel_path, html_path

    def _generate_prediction_html(self, report_data_sorted, accuracy_stats, combo_stats_df, html_path):
        """生成预测结果HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型预测结果报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1, h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .success {{
            background-color: #d4edda !important;
            color: #155724;
        }}
        .failure {{
            background-color: #f8d7da !important;
            color: #721c24;
        }}
        .model-pred {{
            font-weight: bold;
        }}
        .combo-yes {{
            background-color: #d1ecf1 !important;
            color: #0c5460;
            font-weight: bold;
        }}
        .high-selection {{
            background-color: #28a745 !important;
            color: white;
            font-weight: bold;
        }}
        .medium-selection {{
            background-color: #ffc107 !important;
            color: #212529;
            font-weight: bold;
        }}
        .low-selection {{
            background-color: #6c757d !important;
            color: white;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 模型预测结果报告</h1>
        <p><strong>生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>

        <h2>📊 模型准确性统计</h2>
        <div class="stats-grid">
"""

        # 添加模型统计卡片
        for model_name, stats in accuracy_stats.items():
            html_content += f"""
            <div class="stat-card">
                <h3>{model_name}</h3>
                <div class="stat-value">{stats['预测数量']}</div>
                <div>预测数量</div>
                <div>5日精确率: {stats['5日精确率']:.2%}</div>
            </div>
"""

        html_content += """
        </div>

        <h2>📈 模型组合统计</h2>
        <table>
            <thead>
                <tr>
                    <th>模型组合</th>
                    <th>模型数量</th>
                    <th>选中股票数</th>
                    <th>5日成功率</th>
                </tr>
            </thead>
            <tbody>
"""

        # 添加组合统计表格
        for _, row in combo_stats_df.iterrows():
            html_content += f"""
                <tr>
                    <td>{row['模型组合']}</td>
                    <td>{row['模型数量']}</td>
                    <td>{row['选中股票数']}</td>
                    <td>{row['5日成功率']:.2%}</td>
                </tr>
"""

        html_content += """
            </tbody>
        </table>

        <h2>📋 详细预测结果 (按被选中次数排序)</h2>
        <table>
            <thead>
                <tr>
"""

        # 添加表头
        for col in report_data_sorted.columns:
            html_content += f"<th>{col}</th>"

        html_content += """
                </tr>
            </thead>
            <tbody>
"""

        # 添加全部数据（按被选中次数排序）
        for idx, row in report_data_sorted.iterrows():
            html_content += "<tr>"
            for col in report_data_sorted.columns:
                value = row[col]
                css_class = ""

                if col in ['5日成功选股']:
                    css_class = "success" if value == '成功' else "failure"
                elif '_预测' in col:
                    css_class = "model-pred success" if value == '成功' else "model-pred failure"
                elif '_选中' in col:
                    css_class = "combo-yes" if value == '是' else ""
                elif col == '被选中次数':
                    # 为被选中次数添加特殊样式
                    if value >= 4:
                        css_class = "high-selection"
                    elif value >= 2:
                        css_class = "medium-selection"
                    else:
                        css_class = "low-selection"

                html_content += f'<td class="{css_class}">{value}</td>'
            html_content += "</tr>"

        html_content += f"""
            </tbody>
        </table>

        <p><em>数据已按被模型组合选中次数从高到低排序</em></p>
        <p><em>总共 {len(report_data_sorted)} 条预测记录</em></p>
    </div>
</body>
</html>
"""

        # 保存HTML文件
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"   ✅ HTML预测报告已生成: {html_path}")

    def generate_html_report(self, results, base_success_rate, training_histories):
        """生成HTML可视化报告"""
        print(f"\n📊 生成HTML可视化报告...")

        # 过滤有效结果
        valid_results = {k: v for k, v in results.items() if v is not None}

        if not valid_results:
            print("   ⚠️ 没有有效结果，跳过HTML报告生成")
            return

        # HTML模板
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双损失函数深度学习股票预测报告</title>

    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }}
        .header h1 {{
            color: #2E7D32;
            margin-bottom: 10px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }}
        .summary-card .value {{
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }}

        .model-comparison {{
            margin: 30px 0;
        }}
        .model-card {{
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .model-card.best {{
            border-color: #4CAF50;
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
        }}
        .model-name {{
            font-size: 1.3em;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 15px;
        }}
        .metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }}
        .metric {{
            text-align: center;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }}
        .metric-label {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }}
        .metric-value {{
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }}
        .best-model {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }}
        .training-info {{
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #2196F3;
        }}

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 双损失函数深度学习股票预测报告</h1>
            <p>训练时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>

        <div class="training-info">
            <h3>📋 训练信息</h3>
            <p><strong>基础成功率:</strong> {base_success_rate:.2%}</p>
            <p><strong>目标预测比例:</strong> {self.config['dual_loss']['target_proportion']:.1%}</p>
            <p><strong>数据分割:</strong> 按股票分离，确保无数据泄露</p>
            <p><strong>损失函数:</strong> 精确率损失 + 比例损失</p>
        </div>
"""

        # 添加汇总卡片
        best_model = max(valid_results.keys(), key=lambda x: valid_results[x]['score'])
        best_result = valid_results[best_model]

        html_content += f"""
        <div class="summary">
            <div class="summary-card">
                <h3>🏆 最佳模型</h3>
                <div class="value">{best_model}</div>
                <p>综合评分: {best_result['score']:.3f}</p>
            </div>
            <div class="summary-card">
                <h3>📈 最高精确率</h3>
                <div class="value">{best_result['precision']:.1%}</div>
                <p>vs 基础成功率 {base_success_rate:.1%}</p>
            </div>
            <div class="summary-card">
                <h3>🎯 预测比例</h3>
                <div class="value">{best_result['proportion']:.1%}</div>
                <p>目标: {self.config['dual_loss']['target_proportion']:.1%}</p>
            </div>
            <div class="summary-card">
                <h3>📊 预测数量</h3>
                <div class="value">{best_result['predictions_count']}</div>
                <p>个股票</p>
            </div>
        </div>
"""



        # 添加模型详细信息
        html_content += """
        <div class="model-comparison">
            <h2>🤖 模型详细对比</h2>
"""

        for model_name, result in valid_results.items():
            is_best = model_name == best_model
            card_class = "model-card best" if is_best else "model-card"

            html_content += f"""
            <div class="{card_class}">
                <div class="model-name">
                    {model_name} {'🏆' if is_best else ''}
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-label">精确率</div>
                        <div class="metric-value">{result['precision']:.2%}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">召回率</div>
                        <div class="metric-value">{result['recall']:.2%}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">F1分数</div>
                        <div class="metric-value">{result['f1']:.3f}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">AUC</div>
                        <div class="metric-value">{result['auc']:.3f}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测数量</div>
                        <div class="metric-value">{result['predictions_count']}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测比例</div>
                        <div class="metric-value">{result['proportion']:.2%}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">比例偏差</div>
                        <div class="metric-value">{result['proportion_deviation']:.3f}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">综合评分</div>
                        <div class="metric-value">{result['score']:.3f}</div>
                    </div>
                </div>
            </div>
"""

        html_content += """
        </div>
"""





        html_content += """
    </div>
</body>
</html>
"""

        # 保存HTML文件
        report_path = f'{self.output_folder}/reports/training_report.html'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"   ✅ HTML报告已生成: {report_path}")
        return report_path

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='双损失函数深度学习股票预测')
    parser.add_argument('--config', type=str, default='ml_config.json',
                       help='配置文件路径 (默认: ml_config.json)')
    args = parser.parse_args()

    print("双损失函数深度学习股票预测")
    print("=" * 80)
    print(f"📋 使用配置文件: {args.config}")

    # 创建训练器
    trainer = DualLossTrainer(args.config)

    # 加载数据
    data_file = trainer.config["data"]["input_file"]
    df_full = pd.read_excel(data_file)

    X, y, base_success_rate = trainer.load_and_preprocess_data(data_file)

    # 编码
    y_encoded = trainer.label_encoder.fit_transform(y)

    # 保持索引一致性
    df_clean = df_full.iloc[:len(X)].copy()

    # 使用股票分离的数据分割
    X_train, X_val, X_test, y_train, y_val, y_test = trainer.split_data_by_stock(
        df_clean, X, y_encoded
    )

    # 获取测试集索引（用于后续生成预测报告）
    # 重新按股票排序以获取正确的索引
    df_sorted = df_clean.sort_values(trainer.stock_column).reset_index(drop=True)
    unique_stocks = df_sorted[trainer.stock_column].unique()

    # 按照相同的随机种子分割股票
    np.random.seed(trainer.config["data_split"]["random_state"])
    shuffled_stocks = np.random.permutation(unique_stocks)

    test_size = trainer.config["data_split"]["test_size"]
    val_size = trainer.config["data_split"]["val_size"]

    n_test_stocks = int(len(unique_stocks) * test_size)
    n_val_stocks = int(len(unique_stocks) * val_size)

    test_stocks = shuffled_stocks[:n_test_stocks]
    test_mask = df_sorted[trainer.stock_column].isin(test_stocks)
    test_indices = df_sorted[test_mask].index.tolist()

    # 标准化
    X_train_scaled = trainer.scaler.fit_transform(X_train)
    X_val_scaled = trainer.scaler.transform(X_val)
    X_test_scaled = trainer.scaler.transform(X_test)

    print(f"\n📊 数据分割: 训练{X_train_scaled.shape} 验证{X_val_scaled.shape} 测试{X_test_scaled.shape}")
    print(f"🎯 基础成功率: {base_success_rate:.2%}")

    # 创建数据加载器
    train_loader = trainer.create_data_loader(
        X_train_scaled, y_train,
        batch_size=trainer.config["training"]["batch_size"]
    )
    val_loader = trainer.create_data_loader(
        X_val_scaled, y_val,
        batch_size=trainer.config["training"]["batch_size"],
        shuffle=False
    )

    # 定义模型
    input_dim = X_train_scaled.shape[1]
    models = {}

    for model_name, model_config in trainer.config["models"].items():
        if model_name == 'PrecisionMLP':
            models[model_name] = PrecisionMLP(
                input_dim=input_dim,
                hidden_dim=model_config["hidden_dim"],
                dropout_rate=model_config["dropout_rate"]
            )
        elif model_name == 'ResidualNet':
            models[model_name] = ResidualNet(
                input_dim=input_dim,
                hidden_dim=model_config["hidden_dim"],
                dropout_rate=model_config["dropout_rate"]
            )
        elif model_name == 'AttentionNet':
            models[model_name] = AttentionNet(
                input_dim=input_dim,
                hidden_dim=model_config["hidden_dim"],
                dropout_rate=model_config["dropout_rate"]
            )

    print(f"\n🏗️ 模型架构:")
    for name, model in models.items():
        param_count = sum(p.numel() for p in model.parameters())
        print(f"   {name}: {param_count:,} 参数")

    # 训练
    print(f"\n🚀 开始训练...")
    trained_models = {}
    training_histories = {}

    for model_name, model in models.items():
        trained_model, history = trainer.train_model(model, train_loader, val_loader, model_name)
        trained_models[model_name] = trained_model
        training_histories[model_name] = history

    # 训练集成模型
    print(f"\n🔗 训练集成模型...")
    ensemble_model = EnsembleModel(
        input_dim=input_dim,
        hidden_dim=128,
        dropout_rate=0.3
    )

    # 检查是否存在兼容的预训练模型权重
    model_paths = {}
    load_pretrained = True

    for model_name in trained_models.keys():
        model_path = f'{trainer.output_folder}/models/best_{model_name.lower()}_model.pth'
        model_paths[model_name] = model_path

        # 检查文件是否存在
        if not os.path.exists(model_path):
            load_pretrained = False
            break

    # 尝试加载预训练权重，如果失败则跳过
    if load_pretrained:
        try:
            ensemble_model.load_pretrained_models(model_paths, trainer.device)
            print("✅ 成功加载预训练模型权重")
        except Exception as e:
            print(f"⚠️ 无法加载预训练权重（配置可能已更改）: {str(e)}")
            print("🔄 将从头开始训练集成模型...")
    else:
        print("🔄 未找到预训练权重，从头开始训练集成模型...")

    # 训练集成模型
    ensemble_trained, ensemble_history = trainer.train_model(
        ensemble_model, train_loader, val_loader, 'EnsembleModel'
    )
    trained_models['EnsembleModel'] = ensemble_trained
    training_histories['EnsembleModel'] = ensemble_history

    # 评估
    print(f"\n📊 模型评估:")
    print("=" * 60)
    results = {}

    for model_name, model in trained_models.items():
        result = trainer.evaluate_model(model, X_test_scaled, y_test, model_name, base_success_rate)
        results[model_name] = result

    # 生成模型预测结果报告
    trainer.generate_prediction_report(trained_models, df_full, X_test_scaled, y_test, test_indices)

    # 生成HTML可视化报告
    trainer.generate_html_report(results, base_success_rate, training_histories)

    # 保存训练文件
    trainer.save_training_artifacts(results, base_success_rate, training_histories)

    # 总结
    valid_results = {k: v for k, v in results.items() if v is not None}
    if valid_results:
        print(f"\n🎯 训练总结:")
        print("=" * 60)

        best_model = max(valid_results.keys(), key=lambda x: valid_results[x]['score'])
        best_result = valid_results[best_model]

        print(f"🏆 最佳模型: {best_model}")
        print(f"   精确率: {best_result['precision']:.2%}")
        print(f"   预测数量: {best_result['predictions_count']} 个")
        print(f"   预测比例: {best_result['proportion']:.2%} (目标: {trainer.config['dual_loss']['target_proportion']:.1%})")
        print(f"   综合评分: {best_result['score']:.3f}")

        print(f"\n📊 所有模型对比:")
        for model_name, result in valid_results.items():
            print(f"   {model_name:15s}: 精确率={result['precision']:.2%}, "
                  f"预测={result['predictions_count']}个, "
                  f"比例={result['proportion']:.2%}, "
                  f"评分={result['score']:.3f}")

        print(f"\n✅ 训练完成！")
        print(f"📁 输出文件夹: {trainer.output_folder}")
        print(f"🤖 模型文件: {trainer.output_folder}/models/")
        print(f"📖 使用说明: {trainer.output_folder}/README.md")

if __name__ == "__main__":
    main()
