#!/usr/bin/env python3
"""
模型比较脚本
比较深度学习方法与传统规则方法的预测效果
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
import seaborn as sns
from datetime import datetime
import json

# 添加路径
sys.path.append('.')
sys.path.append('scripts')
sys.path.append('deeplearning')

class ModelComparator:
    """模型比较器"""
    
    def __init__(self):
        """初始化比较器"""
        self.dl_results = None
        self.traditional_results = None
        self.comparison_results = {}
        
    def load_results(self, dl_results_path, traditional_results_path):
        """加载两种方法的预测结果"""
        print("📊 加载预测结果...")
        
        try:
            # 加载深度学习结果
            self.dl_results = pd.read_excel(dl_results_path)
            print(f"   深度学习结果: {len(self.dl_results)} 只股票")
            
            # 加载传统方法结果
            self.traditional_results = pd.read_excel(traditional_results_path)
            print(f"   传统方法结果: {len(self.traditional_results)} 只股票")
            
            return True
            
        except Exception as e:
            print(f"❌ 结果加载失败: {e}")
            return False
    
    def align_data(self):
        """对齐两个结果的股票数据"""
        print("🔄 对齐股票数据...")
        
        # 找到股票标识列
        dl_stock_col = self.find_stock_column(self.dl_results)
        traditional_stock_col = self.find_stock_column(self.traditional_results)
        
        if dl_stock_col is None or traditional_stock_col is None:
            print("❌ 未找到股票标识列")
            return False
        
        # 找到共同股票
        dl_stocks = set(self.dl_results[dl_stock_col].astype(str))
        traditional_stocks = set(self.traditional_results[traditional_stock_col].astype(str))
        common_stocks = dl_stocks.intersection(traditional_stocks)
        
        print(f"   共同股票: {len(common_stocks)} 只")
        
        if len(common_stocks) == 0:
            print("❌ 没有共同股票")
            return False
        
        # 筛选共同股票
        self.dl_results = self.dl_results[
            self.dl_results[dl_stock_col].astype(str).isin(common_stocks)
        ].copy()
        
        self.traditional_results = self.traditional_results[
            self.traditional_results[traditional_stock_col].astype(str).isin(common_stocks)
        ].copy()
        
        # 按股票代码排序
        self.dl_results = self.dl_results.sort_values(dl_stock_col).reset_index(drop=True)
        self.traditional_results = self.traditional_results.sort_values(traditional_stock_col).reset_index(drop=True)
        
        return True
    
    def find_stock_column(self, df):
        """找到股票标识列"""
        for col in ['股票', '股票代码', '股票名称']:
            if col in df.columns:
                return col
        return None
    
    def compare_top_recommendations(self):
        """比较Top推荐的重叠度"""
        print("\n🏆 Top推荐比较:")
        print("-" * 40)
        
        # 获取股票列
        dl_stock_col = self.find_stock_column(self.dl_results)
        traditional_stock_col = self.find_stock_column(self.traditional_results)
        
        # 按预测分数排序
        dl_sorted = self.dl_results.sort_values('涨超10%概率', ascending=False)
        
        # 传统方法可能有不同的置信度列名
        traditional_score_col = None
        for col in ['涨超10%置信度', '置信度分数', '预测5日涨幅']:
            if col in self.traditional_results.columns:
                traditional_score_col = col
                break
        
        if traditional_score_col is None:
            print("❌ 未找到传统方法的评分列")
            return
        
        # 处理百分比格式
        if traditional_score_col == '涨超10%置信度':
            traditional_scores = self.traditional_results[traditional_score_col].str.rstrip('%').astype(float) / 100
        else:
            traditional_scores = self.traditional_results[traditional_score_col]
        
        traditional_sorted = self.traditional_results.iloc[traditional_scores.argsort()[::-1]]
        
        # 比较不同Top N的重叠度
        for n in [3, 5, 10, 20]:
            if len(dl_sorted) >= n and len(traditional_sorted) >= n:
                dl_top_n = set(dl_sorted.head(n)[dl_stock_col].astype(str))
                traditional_top_n = set(traditional_sorted.head(n)[traditional_stock_col].astype(str))
                
                overlap = len(dl_top_n.intersection(traditional_top_n))
                overlap_rate = overlap / n
                
                print(f"   Top{n:2d}重叠: {overlap:2d}/{n:2d} ({overlap_rate:.1%})")
                
                self.comparison_results[f'top_{n}_overlap'] = overlap_rate
    
    def compare_accuracy(self, actual_gain_col='5日最大涨幅'):
        """比较预测准确率"""
        print(f"\n📊 预测准确率比较 (基于{actual_gain_col}):")
        print("-" * 40)
        
        if actual_gain_col not in self.dl_results.columns:
            print(f"❌ 深度学习结果中未找到{actual_gain_col}列")
            return
        
        if actual_gain_col not in self.traditional_results.columns:
            print(f"❌ 传统方法结果中未找到{actual_gain_col}列")
            return
        
        # 创建实际标签
        dl_actual = (self.dl_results[actual_gain_col] >= 0.10).astype(int)
        traditional_actual = (self.traditional_results[actual_gain_col] >= 0.10).astype(int)
        
        # 深度学习预测
        dl_predicted = self.dl_results['预测结果'] if '预测结果' in self.dl_results.columns else (self.dl_results['涨超10%概率'] >= 0.5).astype(int)
        
        # 传统方法预测
        traditional_predicted = None
        if '预测成功' in self.traditional_results.columns:
            traditional_predicted = (self.traditional_results['预测成功'] == '✅ 成功').astype(int)
        elif '涨超10%置信度' in self.traditional_results.columns:
            traditional_conf = self.traditional_results['涨超10%置信度'].str.rstrip('%').astype(float) / 100
            traditional_predicted = (traditional_conf >= 0.5).astype(int)
        
        if traditional_predicted is None:
            print("❌ 无法确定传统方法的预测结果")
            return
        
        # 计算准确率
        dl_accuracy = (dl_actual == dl_predicted).mean()
        traditional_accuracy = (traditional_actual == traditional_predicted).mean()
        
        print(f"   深度学习准确率: {dl_accuracy:.1%}")
        print(f"   传统方法准确率: {traditional_accuracy:.1%}")
        print(f"   准确率提升: {dl_accuracy - traditional_accuracy:+.1%}")
        
        # Top N 准确率比较
        print(f"\n📈 Top N 准确率比较:")
        for n in [3, 5, 10]:
            if len(self.dl_results) >= n:
                # 深度学习Top N
                dl_top_n_actual = dl_actual.head(n).mean()
                
                # 传统方法Top N
                traditional_top_n_actual = traditional_actual.head(n).mean()
                
                print(f"   Top{n:2d} - 深度学习: {dl_top_n_actual:.1%}, 传统方法: {traditional_top_n_actual:.1%}")
                
                self.comparison_results[f'top_{n}_dl_accuracy'] = dl_top_n_actual
                self.comparison_results[f'top_{n}_traditional_accuracy'] = traditional_top_n_actual
        
        self.comparison_results['overall_dl_accuracy'] = dl_accuracy
        self.comparison_results['overall_traditional_accuracy'] = traditional_accuracy
    
    def compare_confidence_distribution(self):
        """比较置信度/概率分布"""
        print(f"\n📊 置信度/概率分布比较:")
        print("-" * 40)
        
        # 深度学习概率分布
        dl_probs = self.dl_results['涨超10%概率']
        print(f"   深度学习概率:")
        print(f"     平均值: {dl_probs.mean():.1%}")
        print(f"     中位数: {dl_probs.median():.1%}")
        print(f"     标准差: {dl_probs.std():.1%}")
        print(f"     最大值: {dl_probs.max():.1%}")
        print(f"     最小值: {dl_probs.min():.1%}")
        
        # 传统方法置信度分布
        if '涨超10%置信度' in self.traditional_results.columns:
            traditional_conf = self.traditional_results['涨超10%置信度'].str.rstrip('%').astype(float) / 100
            print(f"   传统方法置信度:")
            print(f"     平均值: {traditional_conf.mean():.1%}")
            print(f"     中位数: {traditional_conf.median():.1%}")
            print(f"     标准差: {traditional_conf.std():.1%}")
            print(f"     最大值: {traditional_conf.max():.1%}")
            print(f"     最小值: {traditional_conf.min():.1%}")
    
    def generate_comparison_plots(self, save_dir="deeplearning/output/"):
        """生成比较图表"""
        print(f"\n📊 生成比较图表...")
        
        os.makedirs(save_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 置信度/概率分布对比图
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        plt.hist(self.dl_results['涨超10%概率'], bins=20, alpha=0.7, label='深度学习', color='blue')
        if '涨超10%置信度' in self.traditional_results.columns:
            traditional_conf = self.traditional_results['涨超10%置信度'].str.rstrip('%').astype(float) / 100
            plt.hist(traditional_conf, bins=20, alpha=0.7, label='传统方法', color='red')
        plt.xlabel('概率/置信度')
        plt.ylabel('频次')
        plt.title('概率/置信度分布对比')
        plt.legend()
        
        # 2. Top N 准确率对比
        plt.subplot(1, 2, 2)
        top_ns = [3, 5, 10]
        dl_accuracies = [self.comparison_results.get(f'top_{n}_dl_accuracy', 0) for n in top_ns]
        traditional_accuracies = [self.comparison_results.get(f'top_{n}_traditional_accuracy', 0) for n in top_ns]
        
        x = np.arange(len(top_ns))
        width = 0.35
        
        plt.bar(x - width/2, dl_accuracies, width, label='深度学习', color='blue', alpha=0.7)
        plt.bar(x + width/2, traditional_accuracies, width, label='传统方法', color='red', alpha=0.7)
        
        plt.xlabel('Top N')
        plt.ylabel('准确率')
        plt.title('Top N 准确率对比')
        plt.xticks(x, [f'Top{n}' for n in top_ns])
        plt.legend()
        plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.0%}'.format(y)))
        
        plt.tight_layout()
        plot_path = os.path.join(save_dir, f"model_comparison_{timestamp}.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   比较图表已保存: {plot_path}")
    
    def save_comparison_report(self, save_dir="deeplearning/output/"):
        """保存比较报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(save_dir, f"model_comparison_report_{timestamp}.json")
        
        report = {
            'timestamp': timestamp,
            'comparison_results': self.comparison_results,
            'summary': {
                'dl_stocks_count': len(self.dl_results),
                'traditional_stocks_count': len(self.traditional_results),
                'common_stocks_count': len(self.dl_results)  # 已对齐
            }
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"💾 比较报告已保存: {report_path}")
    
    def run_comparison(self, dl_results_path, traditional_results_path):
        """运行完整比较流程"""
        print("🔄 开始模型比较...")
        print("=" * 60)
        
        # 加载结果
        if not self.load_results(dl_results_path, traditional_results_path):
            return False
        
        # 对齐数据
        if not self.align_data():
            return False
        
        # 执行各项比较
        self.compare_top_recommendations()
        self.compare_accuracy()
        self.compare_confidence_distribution()
        
        # 生成图表和报告
        self.generate_comparison_plots()
        self.save_comparison_report()
        
        print(f"\n✅ 模型比较完成！")
        return True

def main():
    """主函数"""
    print("🎯 深度学习 vs 传统方法 模型比较")
    print("=" * 80)
    
    # 获取文件路径
    dl_path = input("请输入深度学习预测结果文件路径: ").strip()
    traditional_path = input("请输入传统方法预测结果文件路径: ").strip()
    
    if not dl_path or not traditional_path:
        print("❌ 文件路径不能为空")
        return
    
    if not os.path.exists(dl_path):
        print(f"❌ 深度学习结果文件不存在: {dl_path}")
        return
    
    if not os.path.exists(traditional_path):
        print(f"❌ 传统方法结果文件不存在: {traditional_path}")
        return
    
    # 创建比较器并运行比较
    comparator = ModelComparator()
    success = comparator.run_comparison(dl_path, traditional_path)
    
    if success:
        print(f"\n🎉 比较分析完成！")
    else:
        print(f"\n❌ 比较分析失败")

if __name__ == "__main__":
    main()
