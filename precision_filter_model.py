#!/usr/bin/env python3
"""
精准筛选模型 - 专门用于从候选集中筛选出高概率成功的股票
目标：从100个候选中选出10个，争取5个成功（50%精确率）
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class PrecisionFocusedLoss(nn.Module):
    """专注精确率的损失函数"""
    
    def __init__(self, alpha=2.0, beta=0.5):
        super().__init__()
        self.alpha = alpha  # 正样本权重
        self.beta = beta    # 负样本权重
        
    def forward(self, outputs, targets):
        # 计算概率
        probs = torch.sigmoid(outputs)
        
        # 分离正负样本
        pos_mask = targets == 1
        neg_mask = targets == 0
        
        # 正样本损失：鼓励高概率预测
        pos_loss = -self.alpha * torch.log(probs[pos_mask] + 1e-8).mean() if pos_mask.sum() > 0 else 0
        
        # 负样本损失：适度惩罚，避免过度保守
        neg_loss = -self.beta * torch.log(1 - probs[neg_mask] + 1e-8).mean() if neg_mask.sum() > 0 else 0
        
        return pos_loss + neg_loss

class TopKPrecisionLoss(nn.Module):
    """TopK精确率损失函数 - 专门优化TopK的精确率"""
    
    def __init__(self, k=10, lambda_reg=0.1):
        super().__init__()
        self.k = k
        self.lambda_reg = lambda_reg
        
    def forward(self, outputs, targets):
        batch_size = outputs.size(0)
        probs = torch.sigmoid(outputs.squeeze())
        
        # 获取TopK预测
        _, topk_indices = torch.topk(probs, min(self.k, batch_size))
        
        # TopK中的正样本数量
        topk_targets = targets[topk_indices]
        topk_precision = topk_targets.float().mean()
        
        # 损失 = 1 - TopK精确率 + 正则化项
        precision_loss = 1.0 - topk_precision
        
        # 正则化：鼓励模型给正样本更高的概率
        pos_mask = targets == 1
        if pos_mask.sum() > 0:
            pos_probs = probs[pos_mask]
            reg_loss = -torch.log(pos_probs + 1e-8).mean()
        else:
            reg_loss = 0
            
        return precision_loss + self.lambda_reg * reg_loss

class PrecisionFilterNet(nn.Module):
    """精准筛选网络"""
    
    def __init__(self, input_dim, hidden_dims=[128, 64, 32]):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.network(x)

def evaluate_topk_performance(model, X_test, y_test, k_values=[5, 10, 20]):
    """评估TopK性能"""
    model.eval()
    
    with torch.no_grad():
        X_tensor = torch.FloatTensor(X_test)
        outputs = model(X_tensor)
        probs = torch.sigmoid(outputs).squeeze().numpy()
    
    # 按概率排序
    sorted_indices = np.argsort(probs)[::-1]
    
    results = {}
    for k in k_values:
        if k <= len(sorted_indices):
            topk_indices = sorted_indices[:k]
            topk_targets = y_test[topk_indices]
            precision = topk_targets.mean()
            success_count = topk_targets.sum()
            
            results[f'Top{k}'] = {
                'precision': precision,
                'success_count': success_count,
                'total': k
            }
    
    return results, probs

def train_precision_filter():
    """训练精准筛选模型"""
    print("🎯 精准筛选模型训练")
    print("=" * 50)
    
    # 加载数据
    train_df = pd.read_excel("选股分析结果/选股分析结果_20250730_225530.xlsx")
    test_df = pd.read_excel("选股分析结果/选股分析结果_20250730_225041.xlsx")
    
    print(f"训练数据: {len(train_df)} 行")
    print(f"测试数据: {len(test_df)} 行")
    
    # 特征选择
    feature_cols = [
        'D点实体涨跌幅', 'E点实体涨跌幅', 'D-E涨幅',
        'E点成交量', 'D点成交量', 'C-D涨幅',
        'A点实体涨跌幅', 'B点实体涨跌幅', 'C点实体涨跌幅'
    ]
    
    # 数据预处理
    def preprocess_data(df, feature_cols):
        df_clean = df.copy()
        
        # 转换百分比格式
        percentage_cols = ['D点实体涨跌幅', 'E点实体涨跌幅', 'D-E涨幅', 'C-D涨幅', 
                          'A点实体涨跌幅', 'B点实体涨跌幅', 'C点实体涨跌幅']
        
        for col in percentage_cols:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].astype(str).str.replace('%', '').astype(float) / 100
        
        # 提取特征
        available_features = [col for col in feature_cols if col in df_clean.columns]
        X = df_clean[available_features].fillna(0).values
        y = (df_clean['5日最大涨幅'] >= 0.10).astype(int).values
        
        return X, y, available_features
    
    # 准备训练数据
    X_train, y_train, features = preprocess_data(train_df, feature_cols)
    X_test, y_test, _ = preprocess_data(test_df, feature_cols)
    
    print(f"特征数量: {len(features)}")
    print(f"训练集正样本率: {y_train.mean():.1%}")
    print(f"测试集正样本率: {y_test.mean():.1%}")
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 创建模型
    model = PrecisionFilterNet(input_dim=len(features))
    
    # 使用TopK精确率损失
    criterion = TopKPrecisionLoss(k=10, lambda_reg=0.2)
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=0.01)
    
    # 训练
    print("\n🚀 开始训练...")
    model.train()
    
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.LongTensor(y_train)
    
    best_score = 0
    patience = 20
    patience_counter = 0
    
    for epoch in range(200):
        optimizer.zero_grad()
        outputs = model(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()
        
        # 每10轮评估一次
        if (epoch + 1) % 10 == 0:
            results, _ = evaluate_topk_performance(model, X_test_scaled, y_test)
            top10_precision = results.get('Top10', {}).get('precision', 0)
            
            print(f"Epoch {epoch+1:3d}: Loss={loss.item():.4f}, Top10精确率={top10_precision:.1%}")
            
            if top10_precision > best_score:
                best_score = top10_precision
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), 'best_precision_filter.pth')
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                print("早停触发")
                break
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_precision_filter.pth'))
    
    # 最终评估
    print(f"\n📊 最终评估结果:")
    results, probs = evaluate_topk_performance(model, X_test_scaled, y_test, [5, 10, 15, 20])
    
    for k, metrics in results.items():
        precision = metrics['precision']
        success = metrics['success_count']
        total = metrics['total']
        print(f"{k}: {precision:.1%} ({success}/{total})")
    
    # 显示Top10详细结果
    print(f"\n🏆 Top10详细结果:")
    sorted_indices = np.argsort(probs)[::-1]
    
    for i in range(min(10, len(sorted_indices))):
        idx = sorted_indices[i]
        actual = y_test[idx]
        prob = probs[idx]
        success = "✅" if actual == 1 else "❌"
        
        # 获取股票代码
        stock_code = test_df.iloc[idx].get('股票', f'Stock_{idx}')
        actual_gain = test_df.iloc[idx].get('5日最大涨幅', 0)
        
        print(f"{i+1:2d}. {stock_code} {prob:.1%} -> {actual_gain:.1%} {success}")
    
    return model, scaler, features

if __name__ == "__main__":
    model, scaler, features = train_precision_filter()
