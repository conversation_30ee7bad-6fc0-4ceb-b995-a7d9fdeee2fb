# 置信度预测系统说明

## 🎯 系统概述

本系统已从"预测具体涨幅"改为"预测5日涨超10%的置信度"，这种方法更加科学和实用。

### ✅ 改进优势

1. **更加现实**：预测具体涨幅误差很大，置信度预测更符合实际
2. **更易理解**：置信度直观表示股票涨超10%的可能性
3. **更好排序**：直接按置信度排序，越高越值得关注
4. **更准确评估**：避免了具体数值预测的巨大误差

## 🔧 核心修改

### 1. 训练脚本 (train_prediction_rules.py)
- **目标变量**：改为二分类（涨超10% = 1，否则 = 0）
- **特征选择**：基于统计显著性分析
- **参数优化**：网格搜索最优阈值
- **规则组合**：寻找最佳多规则组合

### 2. 预测脚本 (predict_new_stocks.py)
- **预测方法**：`predict_high_gain_probability()` 替代 `calculate_predicted_gain()`
- **输出列**：`涨超10%置信度` 替代 `预测5日涨幅`
- **排序方式**：按置信度降序排列
- **等级划分**：置信度等级（超高/高/中等/低/极低）

### 3. 筛选模块 (high_gain_filter.py)
- **支持自定义规则**：可传入优化后的规则
- **置信度计算**：集成置信度预测功能

## 📊 置信度计算方法

### 基础置信度
```python
base_confidence = calculate_confidence_score(row)
```
基于选股规则的匹配程度

### 特征增强
```python
confidence_features = {
    'E点价格振幅': {'threshold': 2.5, 'max_boost': 0.25},
    'D点价格振幅': {'threshold': 4.5, 'max_boost': 0.20},
    'A-B涨幅': {'threshold': 40.0, 'max_boost': 0.20},
    'E点实体涨跌幅': {'threshold': -1.0, 'max_boost': 0.15},
    'D点上影线/实体': {'threshold': 6.0, 'max_boost': 0.15}
}
```

### 技术指标确认
- **J值区间**：15-35为超卖反弹区间，最多提升10%
- **成交量确认**：温和放量(≥1.2倍)，最多提升8%

### 最终置信度
```python
total_confidence = base_confidence + feature_boost
total_confidence = max(0.0, min(1.0, total_confidence))
```

## 🏆 置信度等级

| 置信度范围 | 等级 | 投资建议 |
|-----------|------|----------|
| ≥80% | 超高置信度 | 🔥 强烈推荐 |
| 60-80% | 高置信度 | ⭐ 积极关注 |
| 40-60% | 中等置信度 | 📈 适度关注 |
| 20-40% | 低置信度 | ⚠️ 谨慎观察 |
| <20% | 极低置信度 | ❌ 不建议投资 |

## 📈 输出结果

### Excel文件列说明
- **涨超10%置信度**：股票5日涨超10%的概率
- **置信度等级**：基于置信度的分级
- **投资建议**：对应的投资建议
- **推荐等级**：Top3/Top5排名
- **预测准确性**：与实际结果的对比（如有实际数据）

### 排序规则
1. **Top3推荐**：置信度最高的前3只
2. **Top5推荐**：置信度最高的前5只
3. **其他高置信度股票**：置信度≥阈值的其他股票
4. **低置信度股票**：置信度<阈值的股票

## 🎯 使用示例

### 训练模型
```bash
python scripts/train_prediction_rules.py
```
**输出**：
- 优化后的选股规则
- 特征分析报告
- 更新的配置文件

### 预测新股票
```bash
python scripts/predict_new_stocks.py
```
**输入**：新股票数据文件路径
**输出**：
- Top5推荐股票（按置信度排序）
- 完整预测结果Excel文件

### 示例输出
```
🥇 最推荐的5只股票 (按置信度排序):
排名   股票代码     涨超10%置信度     置信度等级        投资建议
1    2735     68.2%        高置信度         ⭐ 积极关注
2    600793   59.3%        中等置信度        📈 适度关注
3    600475   56.7%        中等置信度        📈 适度关注
```

## 🔍 准确性评估

### 评估指标
- **预测准确性**：置信度>50%预测涨超10%，<50%预测不会涨超10%
- **Top3成功率**：前3只推荐股票的成功率
- **Top5成功率**：前5只推荐股票的成功率

### 实际vs预测对比
如果有实际涨幅数据，系统会自动计算：
- **实际涨超10%**：是/否
- **预测准确性**：正确/错误

## 🚀 系统优势

1. **科学性**：基于统计学和机器学习方法
2. **实用性**：置信度比具体涨幅更有指导意义
3. **可解释性**：每个特征的贡献度清晰可见
4. **自适应性**：参数自动优化，避免人工调参
5. **准确性**：避免了具体数值预测的巨大误差

## 📋 配置文件

系统参数保存在 `scripts/config.json`：
- **选股规则**：自动优化后的阈值
- **置信度阈值**：筛选高潜力股票的最小置信度
- **输出格式**：文件命名和列名配置

## 🎉 总结

新的置信度预测系统：
- ✅ **更准确**：避免具体涨幅预测的误差
- ✅ **更实用**：置信度直观易懂
- ✅ **更科学**：基于统计学方法
- ✅ **更智能**：参数自动优化
- ✅ **更可靠**：排序基于置信度而非误差较大的涨幅预测

这种方法更符合实际投资决策的需求，为用户提供更有价值的参考信息。
