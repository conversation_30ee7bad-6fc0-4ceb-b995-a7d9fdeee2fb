{"version": "1.0.0", "description": "深度学习股票预测配置", "models": {"PrecisionMLP": {"hidden_dims": [256, 128, 64, 32], "dropout_rate": 0.4, "activation": "relu", "batch_norm": true}, "ResidualNet": {"hidden_dims": [256, 128, 64, 32], "dropout_rate": 0.3, "activation": "relu", "batch_norm": true}, "AttentionNet": {"hidden_dims": [256, 128, 64, 32], "dropout_rate": 0.3, "activation": "relu", "num_heads": 8, "batch_norm": true}}, "training": {"optimizer": "adamw", "learning_rate": 0.0005, "batch_size": 64, "epochs": 1000, "early_stopping_patience": 100, "early_stopping_metric": "precision", "min_precision_improvement": 0.01, "validation_split": 0.2, "weight_decay": 0.0001, "loss_type": "precision_optimized", "precision_threshold": 0.8, "precision_weight": 10.0, "recall_penalty": 0.1, "lr_scheduler": {"type": "reduce_on_plateau", "factor": 0.8, "patience": 20, "min_lr": 1e-07, "metric": "precision"}, "gradient_clipping": 1.0, "device": "auto"}, "data": {"feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"], "target_column": "5日成功选股", "target_threshold": 0.5, "normalization": {"method": "standard_scaler", "feature_range": [-1, 1]}, "train_test_split": 0.8, "random_state": 42, "train_data_path": "选股分析结果/2025-01-01-2025-04-15.xlsx", "test_data_path": "选股分析结果/2025-05-01-2025-06-01.xlsx", "data_path": "选股分析结果/2025-01-01-2025-04-15.xlsx", "save_model": true, "model_path": "deeplearning/models/", "logs_path": "deeplearning/logs/", "checkpoints": "deeplearning/checkpoints/"}, "prediction": {"confidence_threshold": 0.9, "precision_threshold": 0.8, "output_probabilities": true, "batch_prediction": true, "optimize_for_precision": true}, "output": {"results_path": "deeplearning/output/", "model_summary": true, "feature_importance": true, "confusion_matrix": true, "classification_report": true}}