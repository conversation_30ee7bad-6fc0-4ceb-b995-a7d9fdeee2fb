#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据格式
"""

import pandas as pd
import numpy as np

def check_data():
    """检查数据格式"""
    excel_file = "选股分析结果/选股分析结果_20250804_222043.xlsx"
    
    print(f"📊 检查数据文件: {excel_file}")
    
    # 读取Excel文件
    df = pd.read_excel(excel_file)
    print(f"✅ 成功加载 {len(df)} 条记录")
    
    # 显示前几行
    print(f"\n📋 前5行数据:")
    print(df.head())
    
    # 检查5日最大涨幅列
    if '5日最大涨幅' in df.columns:
        print(f"\n📈 5日最大涨幅列样本:")
        print(df['5日最大涨幅'].head(10))
        print(f"数据类型: {df['5日最大涨幅'].dtype}")
        
        # 尝试转换为数值
        def parse_percentage(val):
            if pd.isna(val):
                return np.nan
            if isinstance(val, str):
                val = val.replace('%', '').replace(' ', '')
                try:
                    return float(val)
                except:
                    return np.nan
            return float(val) if not pd.isna(val) else np.nan
        
        df['5日最大涨幅_数值'] = df['5日最大涨幅'].apply(parse_percentage)
        
        print(f"\n📊 转换后的数值:")
        print(df['5日最大涨幅_数值'].head(10))
        print(f"最大值: {df['5日最大涨幅_数值'].max()}")
        print(f"最小值: {df['5日最大涨幅_数值'].min()}")
        print(f"平均值: {df['5日最大涨幅_数值'].mean():.2f}")
        
        # 检查超过10%的记录
        high_gain = df[df['5日最大涨幅_数值'] >= 10]
        print(f"\n🎯 5日涨幅≥10%的记录: {len(high_gain)} 条")
        if len(high_gain) > 0:
            print("样本:")
            print(high_gain[['股票', '买入日期', '5日最大涨幅', '5日最大涨幅_数值']].head())
    
    # 检查买入日期
    if '买入日期' in df.columns:
        print(f"\n📅 买入日期样本:")
        print(df['买入日期'].head(10))
        print(f"数据类型: {df['买入日期'].dtype}")

if __name__ == "__main__":
    check_data()
