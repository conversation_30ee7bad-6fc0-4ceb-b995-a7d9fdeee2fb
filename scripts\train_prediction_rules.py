#!/usr/bin/env python3
"""
脚本1: 训练预测规则并保存
基于训练数据分析特征，保存预测规则和参数
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import sys
import os
from scipy import stats
from sklearn.feature_selection import mutual_info_classif, SelectKBest, f_classif
from sklearn.model_selection import ParameterGrid
import warnings
warnings.filterwarnings('ignore')

# 添加scripts目录到路径
sys.path.append('scripts')
from high_gain_filter import quick_filter_high_gain_stocks

def convert_to_serializable(obj):
    """将numpy类型转换为可JSON序列化的类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_to_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    else:
        return obj

class PredictionRuleTrainer:
    def __init__(self, config_file='scripts/config.json'):
        """初始化训练器"""
        self.config_file = config_file
        self.config = self.load_config()

        # 从配置文件加载参数
        self.selection_rules = self.config['training']['selection_rules']
        self.feature_columns = self.config['training']['feature_columns']
        self.target_column = self.config['training']['target_column']
        self.training_data_path = self.config['paths']['training_data']
        self.models_dir = self.config['paths']['models_dir']
        self.output_dir = self.config['paths']['output_dir']

        # 确保目录存在
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        self.training_stats = {}

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {self.config_file}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            print(f"💡 请确保配置文件存在: {self.config_file}")
            raise
        
    def load_training_data(self, data_file=None):
        """加载训练数据"""
        print("📊 加载训练数据...")

        # 如果没有指定文件，使用配置文件中的路径
        if data_file is None:
            data_file = self.training_data_path

        print(f"   数据文件: {data_file}")

        if not os.path.exists(data_file):
            print(f"❌ 训练数据文件不存在: {data_file}")
            return None

        df = pd.read_excel(data_file)
        print(f"   原始数据: {len(df)} 行")

        # 检查必要列
        missing_features = [f for f in self.feature_columns if f not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features[:5]}...")
            return None

        if self.target_column not in df.columns:
            print(f"❌ 缺失目标列: {self.target_column}")
            return None

        print(f"✅ 数据验证通过")
        return df
    
    def preprocess_training_data(self, df):
        """预处理训练数据"""
        print("\n🔧 预处理训练数据...")
        
        # 创建副本
        processed_df = df.copy()
        
        # 处理百分比字符串和对象类型
        exclude_cols = ['股票', '股票代码', '股票名称', '日期', '买入日期', 'E点日期']
        
        for col in processed_df.columns:
            if processed_df[col].dtype == 'object' and col not in exclude_cols:
                # 处理百分比
                if processed_df[col].astype(str).str.contains('%').any():
                    processed_df[col] = processed_df[col].astype(str).str.replace('%', '')
                
                # 处理N/A和空值
                processed_df[col] = processed_df[col].replace(['N/A', 'nan', ''], '0')
                
                # 转换为数值
                processed_df[col] = pd.to_numeric(processed_df[col], errors='coerce')
        
        # 填充缺失值
        numeric_cols = processed_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            processed_df[col] = processed_df[col].fillna(processed_df[col].mean())
        
        print(f"   清洗后数据: {len(processed_df)} 行")
        return processed_df

    def feature_selection_analysis(self, df):
        """特征选择分析"""
        print("\n🔍 特征选择分析...")
        print("=" * 60)

        if self.target_column not in df.columns:
            print(f"❌ 目标列 {self.target_column} 不存在")
            return None

        # 准备特征和目标变量
        feature_data = df[self.feature_columns].copy()
        target_data = (df[self.target_column] > 0.10).astype(int)  # 转换为二分类：1=涨超10%, 0=未涨超10%

        # 移除缺失值过多的特征
        missing_ratios = feature_data.isnull().mean()
        valid_features = missing_ratios[missing_ratios < 0.3].index.tolist()
        feature_data = feature_data[valid_features]

        print(f"   有效特征数量: {len(valid_features)}/{len(self.feature_columns)}")

        # 填充缺失值
        for col in feature_data.columns:
            if feature_data[col].dtype in ['float64', 'int64']:
                feature_data[col] = feature_data[col].fillna(feature_data[col].median())

        feature_analysis = {}

        # 1. 统计显著性分析
        print("\n📊 统计显著性分析:")
        for feature in valid_features:
            try:
                success_group = feature_data[target_data == 1][feature].dropna()
                failure_group = feature_data[target_data == 0][feature].dropna()

                if len(success_group) > 5 and len(failure_group) > 5:
                    # t检验
                    t_stat, t_pvalue = stats.ttest_ind(success_group, failure_group)

                    # Mann-Whitney U检验
                    u_stat, u_pvalue = stats.mannwhitneyu(success_group, failure_group, alternative='two-sided')

                    # 效应大小 (Cohen's d)
                    pooled_std = np.sqrt(((len(success_group) - 1) * success_group.var() +
                                        (len(failure_group) - 1) * failure_group.var()) /
                                       (len(success_group) + len(failure_group) - 2))

                    cohens_d = (success_group.mean() - failure_group.mean()) / pooled_std if pooled_std > 0 else 0

                    feature_analysis[feature] = {
                        'success_mean': float(success_group.mean()),
                        'failure_mean': float(failure_group.mean()),
                        'success_std': float(success_group.std()),
                        'failure_std': float(failure_group.std()),
                        't_statistic': float(t_stat),
                        't_pvalue': float(t_pvalue),
                        'u_statistic': float(u_stat),
                        'u_pvalue': float(u_pvalue),
                        'cohens_d': float(cohens_d),
                        'effect_size': 'large' if abs(cohens_d) > 0.8 else 'medium' if abs(cohens_d) > 0.5 else 'small',
                        'is_significant': bool(min(t_pvalue, u_pvalue) < 0.05)
                    }

                    if feature_analysis[feature]['is_significant']:
                        print(f"   ✅ {feature}: p={min(t_pvalue, u_pvalue):.4f}, Cohen's d={cohens_d:.3f}")

            except Exception as e:
                print(f"   ⚠️ {feature}: 分析失败 - {e}")

        # 2. 互信息分析
        print(f"\n🔗 互信息分析:")
        try:
            mi_scores = mutual_info_classif(feature_data, target_data, random_state=42)
            mi_ranking = sorted(zip(valid_features, mi_scores), key=lambda x: x[1], reverse=True)

            for i, (feature, score) in enumerate(mi_ranking[:10]):
                print(f"   {i+1:2d}. {feature}: {score:.4f}")
                if feature in feature_analysis:
                    feature_analysis[feature]['mutual_info_score'] = float(score)
                    feature_analysis[feature]['mi_rank'] = i + 1
        except Exception as e:
            print(f"   ⚠️ 互信息分析失败: {e}")

        # 3. F统计量分析
        print(f"\n📈 F统计量分析:")
        try:
            f_scores, f_pvalues = f_classif(feature_data, target_data)
            f_ranking = sorted(zip(valid_features, f_scores, f_pvalues), key=lambda x: x[1], reverse=True)

            for i, (feature, f_score, f_pvalue) in enumerate(f_ranking[:10]):
                print(f"   {i+1:2d}. {feature}: F={f_score:.2f}, p={f_pvalue:.4f}")
                if feature in feature_analysis:
                    feature_analysis[feature]['f_score'] = float(f_score)
                    feature_analysis[feature]['f_pvalue'] = float(f_pvalue)
                    feature_analysis[feature]['f_rank'] = i + 1
        except Exception as e:
            print(f"   ⚠️ F统计量分析失败: {e}")

        return feature_analysis

    def parameter_search(self, df, feature_analysis):
        """参数搜索和优化"""
        print("\n🔍 参数搜索和优化...")
        print("=" * 60)

        if not feature_analysis:
            print("❌ 无特征分析结果，跳过参数搜索")
            return None

        # 选择显著特征
        significant_features = [f for f, analysis in feature_analysis.items()
                              if analysis.get('is_significant', False)]

        if len(significant_features) < 2:
            print(f"❌ 显著特征数量不足 ({len(significant_features)}), 使用默认规则")
            return None

        print(f"   显著特征数量: {len(significant_features)}")

        # 为每个显著特征搜索最优阈值
        optimized_rules = {}
        target_data = (df[self.target_column] > 0.10).astype(int)

        for feature in significant_features[:8]:  # 限制最多8个特征避免过拟合
            if feature not in df.columns:
                continue

            print(f"\n   🎯 优化特征: {feature}")

            feature_values = df[feature].dropna()
            if len(feature_values) < 10:
                continue

            analysis = feature_analysis[feature]
            success_mean = analysis['success_mean']
            failure_mean = analysis['failure_mean']

            # 确定搜索方向
            if success_mean > failure_mean:
                # 成功组均值更高，搜索下界阈值
                search_direction = '>='
                # 搜索范围：从失败组均值到成功组均值
                min_val = max(failure_mean, feature_values.quantile(0.1))
                max_val = min(success_mean, feature_values.quantile(0.9))
            else:
                # 成功组均值更低，搜索上界阈值
                search_direction = '<='
                # 搜索范围：从成功组均值到失败组均值
                min_val = max(success_mean, feature_values.quantile(0.1))
                max_val = min(failure_mean, feature_values.quantile(0.9))

            if min_val >= max_val:
                continue

            # 生成候选阈值
            thresholds = np.linspace(min_val, max_val, 20)

            best_score = 0
            best_threshold = None
            best_metrics = {}

            for threshold in thresholds:
                try:
                    # 应用阈值筛选
                    if search_direction == '>=':
                        mask = df[feature] >= threshold
                    else:
                        mask = df[feature] <= threshold

                    if mask.sum() < 5:  # 至少要有5个样本
                        continue

                    selected_targets = target_data[mask]

                    if len(selected_targets) == 0:
                        continue

                    # 计算性能指标
                    precision = selected_targets.mean()  # 选中股票中成功的比例
                    recall = selected_targets.sum() / target_data.sum()  # 成功股票中被选中的比例
                    coverage = mask.mean()  # 选中股票占总数的比例

                    # 改进的综合评分策略
                    if precision > 0 and recall > 0:
                        f1_score = 2 * precision * recall / (precision + recall)

                        # 覆盖率权重：鼓励合理的覆盖率范围
                        if coverage < 0.05:  # 覆盖率小于5%，严重惩罚
                            coverage_weight = coverage * 4  # 0-20%权重
                        elif coverage < 0.15:  # 5%-15%，较好范围
                            coverage_weight = 0.2 + (coverage - 0.05) * 6  # 20%-80%权重
                        elif coverage < 0.30:  # 15%-30%，最优范围
                            coverage_weight = 0.8 + (coverage - 0.15) * 1.33  # 80%-100%权重
                        else:  # 覆盖率过高，轻微惩罚
                            coverage_weight = max(0.7, 1.0 - (coverage - 0.30) * 0.5)

                        # 精确率权重：避免过低的精确率
                        if precision < 0.08:  # 精确率小于8%，惩罚
                            precision_weight = precision * 12.5  # 0-100%权重
                        else:  # 精确率>=8%，正常权重
                            precision_weight = 1.0

                        # 最终评分：平衡精确率、召回率和覆盖率
                        score = f1_score * coverage_weight * precision_weight

                        if score > best_score:
                            best_score = score
                            best_threshold = threshold
                            best_metrics = {
                                'precision': precision,
                                'recall': recall,
                                'coverage': coverage,
                                'f1_score': f1_score,
                                'score': score,
                                'selected_count': mask.sum(),
                                'success_count': selected_targets.sum()
                            }

                except Exception as e:
                    continue

            if best_threshold is not None:
                optimized_rules[feature] = {
                    'threshold': float(best_threshold),
                    'operator': search_direction,
                    'precision': float(best_metrics['precision']),
                    'recall': float(best_metrics['recall']),
                    'coverage': float(best_metrics['coverage']),
                    'f1_score': float(best_metrics['f1_score']),
                    'optimization_score': float(best_score),
                    'selected_count': int(best_metrics['selected_count']),
                    'success_count': int(best_metrics['success_count']),
                    'feature_analysis': analysis
                }

                print(f"      最优阈值: {best_threshold:.3f} ({search_direction})")
                print(f"      精确率: {best_metrics['precision']:.1%}")
                print(f"      召回率: {best_metrics['recall']:.1%}")
                print(f"      覆盖率: {best_metrics['coverage']:.1%}")

        # 按优化分数排序，选择最佳规则
        if optimized_rules:
            sorted_rules = sorted(optimized_rules.items(),
                                key=lambda x: x[1]['optimization_score'], reverse=True)

            print(f"\n🏆 最优规则排序:")
            for i, (feature, rule) in enumerate(sorted_rules[:6], 1):
                print(f"   {i}. {feature}: {rule['threshold']:.3f} {rule['operator']} "
                      f"(精确率: {rule['precision']:.1%}, 分数: {rule['optimization_score']:.3f})")

        return optimized_rules

    def optimize_rule_combinations(self, df, optimized_rules):
        """优化规则组合，寻找最佳的多规则组合"""
        if not optimized_rules or len(optimized_rules) < 2:
            return optimized_rules

        print(f"\n🔄 规则组合优化...")
        print("=" * 60)

        target_data = (df[self.target_column] > 0.10).astype(int)

        # 按单个规则性能排序
        sorted_rules = sorted(optimized_rules.items(),
                            key=lambda x: x[1]['optimization_score'], reverse=True)

        best_combination = None
        best_score = 0
        best_metrics = {}

        # 测试不同的规则组合
        for num_rules in range(2, min(6, len(sorted_rules) + 1)):  # 测试2-5个规则的组合
            for i in range(len(sorted_rules) - num_rules + 1):
                rule_subset = dict(sorted_rules[i:i+num_rules])

                # 应用规则组合
                combined_mask = pd.Series([True] * len(df), index=df.index)

                for feature, rule in rule_subset.items():
                    if feature in df.columns:
                        threshold = rule['threshold']
                        operator = rule['operator']

                        if operator == '>=':
                            feature_mask = df[feature] >= threshold
                        else:  # operator == '<='
                            feature_mask = df[feature] <= threshold

                        combined_mask = combined_mask & feature_mask

                if combined_mask.sum() < 5:  # 至少要有5个样本
                    continue

                selected_targets = target_data[combined_mask]

                if len(selected_targets) == 0:
                    continue

                # 计算组合性能
                precision = selected_targets.mean()
                recall = selected_targets.sum() / target_data.sum()
                coverage = combined_mask.mean()

                # 使用改进的评分策略
                if precision > 0 and recall > 0:
                    f1_score = 2 * precision * recall / (precision + recall)

                    # 覆盖率权重（同上）
                    if coverage < 0.05:
                        coverage_weight = coverage * 4
                    elif coverage < 0.15:
                        coverage_weight = 0.2 + (coverage - 0.05) * 6
                    elif coverage < 0.30:
                        coverage_weight = 0.8 + (coverage - 0.15) * 1.33
                    else:
                        coverage_weight = max(0.7, 1.0 - (coverage - 0.30) * 0.5)

                    # 精确率权重
                    if precision < 0.08:
                        precision_weight = precision * 12.5
                    else:
                        precision_weight = 1.0

                    score = f1_score * coverage_weight * precision_weight

                    if score > best_score:
                        best_score = score
                        best_combination = rule_subset
                        best_metrics = {
                            'precision': precision,
                            'recall': recall,
                            'coverage': coverage,
                            'f1_score': f1_score,
                            'score': score,
                            'selected_count': combined_mask.sum(),
                            'success_count': selected_targets.sum(),
                            'num_rules': num_rules
                        }

        if best_combination:
            print(f"🏆 最佳规则组合 ({best_metrics['num_rules']}个规则):")
            print(f"   精确率: {best_metrics['precision']:.1%}")
            print(f"   召回率: {best_metrics['recall']:.1%}")
            print(f"   覆盖率: {best_metrics['coverage']:.1%}")
            print(f"   选中股票: {best_metrics['selected_count']} 只")
            print(f"   成功股票: {best_metrics['success_count']} 只")
            print(f"   综合评分: {best_metrics['score']:.3f}")

            print(f"\n📋 最佳组合规则:")
            for i, (feature, rule) in enumerate(best_combination.items(), 1):
                print(f"   {i}. {feature}: {rule['threshold']:.3f} {rule['operator']}")

            return best_combination
        else:
            print(f"⚠️ 未找到更好的规则组合，保持原有规则")
            return optimized_rules

    def analyze_training_performance(self, df):
        """分析训练数据的性能"""
        print("\n📈 分析训练数据性能...")
        
        # 应用选股规则（使用当前优化后的规则）
        try:
            selected_df = quick_filter_high_gain_stocks(df.copy(), self.selection_rules)
            print(f"   选股规则筛选出: {len(selected_df)} 只股票")
        except Exception as e:
            print(f"   选股筛选出错: {e}")
            return False
        
        # 分析成功率
        if len(selected_df) > 0 and self.target_column in selected_df.columns:
            # 计算实际涨幅>10%的股票
            successful_stocks = selected_df[selected_df[self.target_column] > 0.10]
            success_rate = len(successful_stocks) / len(selected_df)
            
            print(f"   选中股票中涨幅>10%: {len(successful_stocks)} 只")
            print(f"   选股成功率: {success_rate:.1%}")
            
            # 计算各特征的统计信息
            feature_stats = {}
            for feature in self.feature_columns:
                if feature in selected_df.columns:
                    feature_stats[feature] = {
                        'mean': float(selected_df[feature].mean()),
                        'std': float(selected_df[feature].std()),
                        'min': float(selected_df[feature].min()),
                        'max': float(selected_df[feature].max()),
                        'median': float(selected_df[feature].median())
                    }
            
            # 保存训练统计信息
            self.training_stats = {
                'total_stocks': len(df),
                'selected_stocks': len(selected_df),
                'successful_stocks': len(successful_stocks),
                'success_rate': success_rate,
                'selection_rate': len(selected_df) / len(df),
                'feature_stats': feature_stats,
                'target_stats': {
                    'mean': float(df[self.target_column].mean()),
                    'std': float(df[self.target_column].std()),
                    'high_gain_ratio': float((df[self.target_column] > 0.10).mean())
                }
            }
            
            print(f"   整体高涨幅比例: {self.training_stats['target_stats']['high_gain_ratio']:.1%}")
            print(f"   选股提升倍数: {success_rate / self.training_stats['target_stats']['high_gain_ratio']:.1f}x")
            
        return True
    
    def save_prediction_rules(self, feature_analysis=None, optimized_rules=None):
        """保存预测规则和参数"""
        print(f"\n💾 保存预测规则...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 从配置文件获取文件命名规则
        rules_filename = self.config['output']['file_naming']['training_rules'].format(timestamp=timestamp)
        latest_filename = self.config['output']['file_naming']['latest_rules']

        # 使用优化后的规则（如果有）
        final_selection_rules = optimized_rules if optimized_rules else self.selection_rules

        # 准备保存的配置
        prediction_config = {
            'version': self.config['version'],
            'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'selection_rules': convert_to_serializable(final_selection_rules),
            'feature_columns': self.feature_columns,
            'target_column': self.target_column,
            'training_stats': convert_to_serializable(self.training_stats),
            'prediction_thresholds': self.config['prediction']['thresholds'],
            'recommendation': self.config['prediction']['recommendation'],
            'original_config': self.config,
            'feature_analysis': convert_to_serializable(feature_analysis),
            'optimization_info': {
                'method': 'grid_search_with_statistical_analysis',
                'optimization_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'rules_optimized': optimized_rules is not None,
                'features_analyzed': len(feature_analysis) if feature_analysis else 0
            }
        }

        # 保存配置文件
        config_path = os.path.join(self.models_dir, rules_filename)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(prediction_config, f, ensure_ascii=False, indent=2)
        print(f"   预测规则已保存: {config_path}")

        # 保存最新配置（用于预测脚本）
        latest_config_path = os.path.join(self.models_dir, latest_filename)
        with open(latest_config_path, 'w', encoding='utf-8') as f:
            json.dump(prediction_config, f, ensure_ascii=False, indent=2)
        print(f"   最新规则配置: {latest_config_path}")

        return config_path

    def update_config_with_optimized_rules(self, optimized_rules):
        """更新配置文件中的选股规则"""
        if not optimized_rules:
            return

        print(f"\n📝 更新配置文件中的选股规则...")

        try:
            # 更新内存中的配置
            self.config['training']['selection_rules'] = optimized_rules

            # 保存更新后的配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

            print(f"   ✅ 配置文件已更新: {self.config_file}")
            print(f"   📊 更新了 {len(optimized_rules)} 个选股规则")

        except Exception as e:
            print(f"   ⚠️ 配置文件更新失败: {e}")

    def generate_optimization_report(self, feature_analysis, optimized_rules):
        """生成优化报告"""
        if not feature_analysis and not optimized_rules:
            return

        print(f"\n📋 生成优化报告...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.output_dir, f"optimization_report_{timestamp}.txt")

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("股票预测系统 - 特征选择与参数优化报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"训练数据: {self.training_data_path}\n\n")

                if feature_analysis:
                    f.write("特征分析结果:\n")
                    f.write("-" * 40 + "\n")

                    # 按显著性排序
                    significant_features = [(name, analysis) for name, analysis in feature_analysis.items()
                                          if analysis.get('is_significant', False)]
                    significant_features.sort(key=lambda x: x[1].get('optimization_score', 0), reverse=True)

                    f.write(f"显著特征数量: {len(significant_features)}/{len(feature_analysis)}\n\n")

                    for name, analysis in significant_features:
                        f.write(f"特征: {name}\n")
                        f.write(f"  成功组均值: {analysis['success_mean']:.4f}\n")
                        f.write(f"  失败组均值: {analysis['failure_mean']:.4f}\n")
                        f.write(f"  Cohen's d: {analysis['cohens_d']:.4f} ({analysis['effect_size']})\n")
                        f.write(f"  p值: {min(analysis.get('t_pvalue', 1), analysis.get('u_pvalue', 1)):.6f}\n")
                        if 'mutual_info_score' in analysis:
                            f.write(f"  互信息分数: {analysis['mutual_info_score']:.4f}\n")
                        f.write("\n")

                if optimized_rules:
                    f.write("优化后的选股规则:\n")
                    f.write("-" * 40 + "\n")

                    for name, rule in optimized_rules.items():
                        f.write(f"规则: {name}\n")
                        f.write(f"  阈值: {rule['threshold']:.4f} {rule['operator']}\n")
                        f.write(f"  精确率: {rule['precision']:.1%}\n")
                        f.write(f"  召回率: {rule['recall']:.1%}\n")
                        f.write(f"  覆盖率: {rule['coverage']:.1%}\n")
                        f.write(f"  F1分数: {rule['f1_score']:.4f}\n")
                        f.write(f"  优化分数: {rule['optimization_score']:.4f}\n")
                        f.write(f"  选中股票: {rule['selected_count']} 只\n")
                        f.write(f"  成功股票: {rule['success_count']} 只\n")
                        f.write("\n")

            print(f"   ✅ 优化报告已保存: {report_file}")

        except Exception as e:
            print(f"   ⚠️ 优化报告生成失败: {e}")
    
    def display_training_summary(self):
        """显示训练总结"""
        print(f"\n📋 训练总结:")
        print("-" * 50)
        
        if self.training_stats:
            print(f"   总股票数: {self.training_stats['total_stocks']}")
            print(f"   选中股票数: {self.training_stats['selected_stocks']}")
            print(f"   成功股票数: {self.training_stats['successful_stocks']}")
            print(f"   选股成功率: {self.training_stats['success_rate']:.1%}")
            print(f"   选股覆盖率: {self.training_stats['selection_rate']:.1%}")
            
            print(f"\n🎯 核心选股规则:")
            for rule_name, rule_info in self.selection_rules.items():
                print(f"   {rule_name} {rule_info['operator']} {rule_info['threshold']} (精确率: {rule_info['precision']:.1%})")
            
            print(f"\n📊 目标变量统计:")
            target_stats = self.training_stats['target_stats']
            print(f"   平均涨幅: {target_stats['mean']*100:.2f}%")
            print(f"   高涨幅比例: {target_stats['high_gain_ratio']:.1%}")

def main():
    """主函数"""
    print("🎯 预测规则训练器 (基于配置文件)")
    print("=" * 60)

    try:
        # 创建训练器（自动加载配置）
        trainer = PredictionRuleTrainer()

        # 加载训练数据（使用配置文件中的路径）
        df = trainer.load_training_data()
        if df is None:
            return

        # 预处理数据
        processed_df = trainer.preprocess_training_data(df)
        if processed_df is None:
            return

        # 特征选择分析
        print("\n" + "="*80)
        print("🔬 开始特征选择和参数优化")
        print("="*80)

        feature_analysis = trainer.feature_selection_analysis(processed_df)

        # 参数搜索优化
        optimized_rules = None
        if feature_analysis:
            optimized_rules = trainer.parameter_search(processed_df, feature_analysis)

            if optimized_rules:
                print(f"\n✅ 单特征优化完成，优化了 {len(optimized_rules)} 个特征规则")

                # 规则组合优化
                final_rules = trainer.optimize_rule_combinations(processed_df, optimized_rules)

                if final_rules != optimized_rules:
                    print(f"\n✅ 规则组合优化完成，最终使用 {len(final_rules)} 个规则")
                    optimized_rules = final_rules

                # 更新选股规则为优化后的规则
                trainer.selection_rules = optimized_rules
            else:
                print(f"\n⚠️ 参数优化未找到更好的规则，使用默认配置")

        # 分析训练性能（使用最终规则）
        print("\n" + "="*80)
        print("📈 最终性能评估")
        print("="*80)

        if not trainer.analyze_training_performance(processed_df):
            return

        # 保存预测规则（包含优化信息）
        trainer.save_prediction_rules(feature_analysis, optimized_rules)

        # 更新配置文件（如果有优化规则）
        if optimized_rules:
            trainer.update_config_with_optimized_rules(optimized_rules)

        # 生成优化报告
        trainer.generate_optimization_report(feature_analysis, optimized_rules)

        # 显示训练总结
        trainer.display_training_summary()

        # 显示优化总结
        if optimized_rules:
            print(f"\n🎯 参数优化总结:")
            print(f"   优化特征数量: {len(optimized_rules)}")
            print(f"   特征分析数量: {len(feature_analysis) if feature_analysis else 0}")
            print(f"   优化方法: 网格搜索 + 统计分析")
            print(f"   评估指标: F1分数 × 覆盖率权重")
            print(f"   配置文件已更新: scripts/config.json")
        else:
            print(f"\n📋 使用默认配置:")
            print(f"   未找到显著改进的参数组合")
            print(f"   保持原有配置文件不变")

        print(f"\n🎉 预测规则训练完成！")
        print(f"📁 规则文件保存在 {trainer.models_dir} 目录")
        print(f"🚀 现在可以使用 predict_new_stocks.py 进行预测")

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        print(f"💡 请检查配置文件和数据文件是否正确")

if __name__ == "__main__":
    main()
