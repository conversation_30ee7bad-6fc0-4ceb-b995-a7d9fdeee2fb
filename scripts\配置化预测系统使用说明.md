# 配置化股票预测系统使用说明

## 系统概述

本系统是一个基于配置文件的股票预测系统，包含两个主要脚本：
- `train_prediction_rules.py`: 训练预测规则
- `predict_new_stocks.py`: 预测新股票

所有参数都从配置文件 `config.json` 中读取，产物保存到 `scripts` 目录下的子目录中。

## 目录结构

```
scripts/
├── config.json                    # 主配置文件
├── train_prediction_rules.py      # 训练脚本
├── predict_new_stocks.py          # 预测脚本
├── high_gain_filter.py            # 筛选模块
├── models/                        # 模型和规则存储目录
│   ├── latest_prediction_rules.json
│   └── prediction_rules_YYYYMMDD_HHMMSS.json
├── output/                        # 预测结果输出目录
│   └── 新股票预测结果_YYYYMMDD_HHMMSS.xlsx
├── temp/                          # 临时文件目录
└── logs/                          # 日志文件目录
```

## 配置文件说明

### 主要配置项

#### 1. 路径配置 (paths)
```json
"paths": {
  "training_data": "选股分析结果/选股分析结果_20250730_225530.xlsx",
  "models_dir": "scripts/models",
  "output_dir": "scripts/output",
  "temp_dir": "scripts/temp"
}
```

#### 2. 训练配置 (training)
```json
"training": {
  "selection_rules": {
    "E点价格振幅": {"threshold": 3.968, "operator": ">=", "precision": 0.193},
    "D点上影线/实体": {"threshold": 8.0, "operator": ">=", "precision": 0.172}
  },
  "feature_columns": [...],
  "target_column": "5日最大涨幅"
}
```

#### 3. 预测配置 (prediction)
```json
"prediction": {
  "thresholds": {
    "high_gain_threshold": 0.10,
    "min_confidence": 0.12,
    "min_predicted_gain": 0.10
  },
  "recommendation": {
    "top_n_stocks": 5,
    "top_priority_stocks": 3
  }
}
```

## 使用步骤

### 第一步：配置系统

1. **修改配置文件** `scripts/config.json`
   ```bash
   # 编辑配置文件
   notepad scripts/config.json
   ```

2. **主要配置项**：
   - `paths.training_data`: 训练数据文件路径
   - `training.selection_rules`: 选股规则和阈值
   - `prediction.thresholds`: 预测阈值设置

### 第二步：训练预测规则

```bash
# 运行训练脚本
python scripts/train_prediction_rules.py
```

**输出**：
- 控制台显示训练过程和统计信息
- 生成规则文件：`scripts/models/prediction_rules_YYYYMMDD_HHMMSS.json`
- 生成最新规则：`scripts/models/latest_prediction_rules.json`

### 第三步：预测新股票

```bash
# 运行预测脚本
python scripts/predict_new_stocks.py
```

**交互过程**：
1. 系统提示输入新数据文件路径
2. 输入完整路径，例如：`C:\Users\<USER>\新数据.xlsx`
3. 系统自动进行预测和分析

**输出**：
- 控制台显示预测结果和推荐股票
- 生成Excel文件：`scripts/output/新股票预测结果_YYYYMMDD_HHMMSS.xlsx`

## 配置文件详细说明

### 选股规则配置
```json
"selection_rules": {
  "特征名称": {
    "threshold": 数值阈值,
    "operator": "比较操作符(>=, <=, >, <, ==)",
    "precision": 精确率权重
  }
}
```

### 特征列配置
系统支持31个技术指标特征，包括：
- A-E点的价格和成交量数据
- 涨跌幅和振幅指标
- 技术分析指标（J值等）

### 预测阈值配置
- `high_gain_threshold`: 高涨幅阈值（默认10%）
- `min_confidence`: 最小置信度要求
- `min_predicted_gain`: 最小预测涨幅要求

### 输出文件命名
```json
"file_naming": {
  "training_rules": "prediction_rules_{timestamp}.json",
  "latest_rules": "latest_prediction_rules.json",
  "prediction_results": "新股票预测结果_{timestamp}.xlsx"
}
```

## 输出结果说明

### 训练输出
- **选股成功率**: 规则筛选的股票中实际涨幅>10%的比例
- **选股覆盖率**: 规则筛选出的股票占总数的比例
- **核心规则**: 各个特征的阈值和精确率

### 预测输出
- **Top3推荐**: 最优先推荐的3只股票
- **Top5推荐**: 前5只推荐股票
- **预测准确性**: 与实际涨幅的对比分析（如有实际数据）

### Excel输出列说明
- `最终排名`: 综合推荐排名
- `选股状态`: 高潜力股/低潜力股标识
- `预测5日涨幅`: 系统预测的涨幅
- `置信度分数`: 预测的可信度
- `投资建议`: 基于预测结果的建议
- `推荐等级`: 超高潜力/高潜力/中高潜力等

## 常见问题

### Q1: 如何修改选股规则？
A: 编辑 `config.json` 中的 `training.selection_rules` 部分，调整阈值和操作符。

### Q2: 如何更换训练数据？
A: 修改 `config.json` 中的 `paths.training_data` 路径。

### Q3: 预测结果保存在哪里？
A: 所有输出文件保存在 `scripts/output/` 目录中。

### Q4: 如何查看历史训练规则？
A: 查看 `scripts/models/` 目录中的时间戳命名文件。

### Q5: 系统支持哪些数据格式？
A: 目前支持Excel格式(.xlsx)，数据需包含配置文件中指定的特征列。

## 技术支持

如遇问题，请检查：
1. 配置文件格式是否正确（JSON格式）
2. 数据文件路径是否存在
3. 数据文件是否包含必需的特征列
4. 目录权限是否允许写入

## 版本信息

- 当前版本: 1.0
- 最后更新: 2025-08-02
- 支持的Python版本: 3.7+
