#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JZ Volume Shrink Selector - 极简版本
用最少的代码实现JZ极致缩量选股
"""

import json
import os
import pandas as pd
from datetime import datetime
from tqdm import tqdm
import glob

# 导入原有的选股器类
from jz_volume_shrink_runner import JZVolumeShrinkSelector


def load_config(config_file="jz_volume_config.json"):
    """加载配置文件"""
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_stock_data(data_dir):
    """加载股票数据"""
    print(f"📂 加载股票数据: {data_dir}")
    stock_data = {}
    
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    for file_path in tqdm(csv_files, desc="加载数据"):
        stock_code = os.path.basename(file_path).replace('.csv', '')
        try:
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            stock_data[stock_code] = df
        except Exception:
            continue
    
    print(f"✅ 成功加载 {len(stock_data)} 只股票数据")
    return stock_data


def create_selector(config):
    """创建选股器实例"""
    params = config.get('algorithm_params', {})
    return JZVolumeShrinkSelector(
        rise_pct=params.get('rise_pct', 0.15),
        fall_pct=params.get('fall_pct', 0.10),
        n1=params.get('n1', 20),
        a_consolidation_days=params.get('a_consolidation_days', 5),
        a_consolidation_range=params.get('a_consolidation_range', 0.05),
        a_downward_min_pct=params.get('a_downward_min_pct', 0.02),
        a_downward_max_pct=params.get('a_downward_max_pct', 0.08),
        a_downward_range_multiplier=params.get('a_downward_range_multiplier', 1.5),
        d_vol_ratio=params.get('d_vol_ratio', 1.2),
        d_vol_max_ratio=params.get('d_vol_max_ratio', 3.0),
        upper_shadow_ratio=params.get('upper_shadow_ratio', 0.3),
        e_vol_vs_cd_avg_ratio=params.get('e_vol_vs_cd_avg_ratio', 0.5),
        e_vs_d_vol_ratio=params.get('e_vs_d_vol_ratio', 0.8),
        de_max_days=params.get('de_max_days', 10),
        e_yang_threshold=params.get('e_yang_threshold', 0.01),
        shadow_ratio=params.get('shadow_ratio', 0.2),
        ce_rise_ratio=params.get('ce_rise_ratio', 0.3),
        ab_max_days=params.get('ab_max_days', 30),
        cd_max_distance_trade_days=params.get('cd_max_distance_trade_days', 15),
        d_lookback_trade_days=params.get('d_lookback_trade_days', 10),
        config=config
    )


def get_trading_dates(config):
    """获取交易日期列表"""
    date_range = config['data_settings']['date_range']
    start_date = pd.to_datetime(date_range['start_date'])
    end_date = pd.to_datetime(date_range['end_date'])
    
    # 生成日期范围（简化处理，过滤周末）
    dates = pd.date_range(start_date, end_date, freq='D')
    return [d for d in dates if d.weekday() < 5]


def select_stocks_for_date(selector, stock_data, date):
    """为指定日期选股"""
    selected_stocks = []
    
    for stock_code, df in stock_data.items():
        try:
            result = selector.select(df, date)
            if result:
                selected_stocks.append({
                    'stock_code': stock_code,
                    'date': date.strftime('%Y-%m-%d'),
                    'result': result
                })
        except Exception:
            continue
    
    return selected_stocks


def save_results(results):
    """保存结果"""
    if not results:
        print("⚠️ 没有选中任何股票")
        return
    
    # 创建结果目录
    os.makedirs("results", exist_ok=True)
    
    # 保存为JSON和CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_file = f"results/jz_simple_results_{timestamp}.json"
    csv_file = f"results/jz_simple_results_{timestamp}.csv"
    
    # 保存JSON
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    # 保存CSV
    df = pd.DataFrame(results)
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 结果已保存: {json_file}, {csv_file}")


def main():
    """主函数 - 极简版本"""
    try:
        print("🚀 开始JZ极致缩量选股分析")
        
        # 1. 加载配置
        config = load_config()
        
        # 2. 加载股票数据
        data_dir = config['data_settings']['stock_data_dir']
        stock_data = load_stock_data(data_dir)
        
        # 3. 创建选股器
        selector = create_selector(config)
        print("✅ 选股器创建成功")
        
        # 4. 获取交易日期
        dates = get_trading_dates(config)
        print(f"📅 分析日期: {len(dates)} 个交易日")
        
        # 5. 按日期选股
        all_results = []
        for date in tqdm(dates, desc="选股进度"):
            selected = select_stocks_for_date(selector, stock_data, date)
            all_results.extend(selected)
            if selected:
                print(f"📊 {date.strftime('%Y-%m-%d')}: 选中 {len(selected)} 只股票")
        
        # 6. 保存结果
        save_results(all_results)
        
        # 7. 统计摘要
        if all_results:
            dates_count = len(set(r['date'] for r in all_results))
            print(f"\n📊 统计摘要:")
            print(f"   处理日期数: {dates_count}")
            print(f"   选中股票数: {len(all_results)}")
            print(f"   平均每日: {len(all_results)/dates_count:.1f} 只")
        
        print(f"🎉 分析完成! 总共选中 {len(all_results)} 只股票")
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
