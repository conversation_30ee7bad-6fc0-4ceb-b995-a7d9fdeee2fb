#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析D-E点的移动平均线企稳情况
只统计D点到E点的价格变化和均线趋势
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def analyze_de_ma_stability():
    """分析D-E点的移动平均线企稳情况"""
    print("📊 分析D-E点的移动平均线企稳情况")
    print("=" * 80)
    
    # 加载数据
    data_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    print(f"📂 加载数据: {data_file}")
    
    df = pd.read_excel(data_file)
    print(f"   总数据: {len(df)} 条记录")
    
    # 提取成功案例
    target_col = "5日成功选股"
    successful_mask = df[target_col] == "成功"
    successful_cases = df[successful_mask].copy()
    
    print(f"✅ 成功案例: {len(successful_cases)} 个")
    
    if len(successful_cases) == 0:
        print("❌ 没有找到成功案例")
        return
    
    # 检查D点和E点的价格数据
    required_columns = ['D点收盘', 'E点收盘']
    missing_columns = [col for col in required_columns if col not in successful_cases.columns]
    
    if missing_columns:
        print(f"❌ 缺失关键列: {missing_columns}")
        return
    
    print(f"✅ 找到D点和E点收盘价数据")
    
    # 分析D-E点的价格变化
    analyze_de_price_change(successful_cases)
    
    # 分析D-E点的均线企稳情况
    analyze_de_ma_trend(successful_cases)
    
    # 分析企稳与成功率的关系
    analyze_stability_correlation(successful_cases)

def analyze_de_price_change(successful_cases):
    """分析D-E点的价格变化"""
    print(f"\n📈 D-E点价格变化分析:")
    
    # 提取D点和E点收盘价
    d_prices = pd.to_numeric(successful_cases['D点收盘'], errors='coerce')
    e_prices = pd.to_numeric(successful_cases['E点收盘'], errors='coerce')
    
    # 计算D-E价格变化
    valid_mask = pd.notna(d_prices) & pd.notna(e_prices) & (d_prices > 0)
    valid_cases = successful_cases[valid_mask].copy()
    d_prices_valid = d_prices[valid_mask]
    e_prices_valid = e_prices[valid_mask]
    
    price_change = (e_prices_valid - d_prices_valid) / d_prices_valid * 100
    valid_cases['de_price_change'] = price_change
    
    print(f"   有效样本: {len(valid_cases)} 个")
    print(f"   D-E价格变化统计:")
    print(f"     平均变化: {price_change.mean():.2f}%")
    print(f"     中位数变化: {price_change.median():.2f}%")
    print(f"     标准差: {price_change.std():.2f}%")
    print(f"     最大涨幅: {price_change.max():.2f}%")
    print(f"     最大跌幅: {price_change.min():.2f}%")
    
    # 统计上涨和下跌的比例
    up_count = (price_change > 0).sum()
    down_count = (price_change < 0).sum()
    flat_count = (price_change == 0).sum()
    
    print(f"\n   D-E价格变化分布:")
    print(f"     上涨: {up_count} 个 ({up_count/len(valid_cases):.1%})")
    print(f"     下跌: {down_count} 个 ({down_count/len(valid_cases):.1%})")
    print(f"     持平: {flat_count} 个 ({flat_count/len(valid_cases):.1%})")
    
    return valid_cases

def analyze_de_ma_trend(successful_cases):
    """分析D-E点的均线趋势"""
    print(f"\n📊 D-E点均线趋势分析:")
    
    # 获取有效的D-E价格数据
    valid_cases = analyze_de_price_change(successful_cases)
    
    if len(valid_cases) == 0:
        print("❌ 没有有效的D-E价格数据")
        return
    
    # 分析均线企稳情况
    ma_analysis = []
    
    for idx, row in valid_cases.iterrows():
        stock = row.get('股票', 'N/A')
        date = row.get('日期', 'N/A')
        d_price = pd.to_numeric(row['D点收盘'], errors='coerce')
        e_price = pd.to_numeric(row['E点收盘'], errors='coerce')
        actual_gain = row.get('5日最大涨幅', 'N/A')
        de_change = row['de_price_change']
        
        # 模拟均线企稳判断
        # 由于只有D-E两个点，我们用简化的方法判断
        
        # 5日线企稳：E点价格高于D点价格
        ma5_stable = e_price >= d_price
        
        # 10日线企稳：E点价格相对D点涨幅≥-1%（允许小幅下跌）
        ma10_stable = de_change >= -1.0
        
        # 20日线企稳：E点价格相对D点涨幅≥-2%（允许更大下跌）
        ma20_stable = de_change >= -2.0
        
        # 综合企稳：至少2条均线企稳
        overall_stable = sum([ma5_stable, ma10_stable, ma20_stable]) >= 2
        
        ma_analysis.append({
            'stock': stock,
            'date': date,
            'd_price': d_price,
            'e_price': e_price,
            'de_change': de_change,
            'actual_gain': actual_gain,
            'ma5_stable': ma5_stable,
            'ma10_stable': ma10_stable,
            'ma20_stable': ma20_stable,
            'overall_stable': overall_stable
        })
    
    # 统计企稳情况
    total_count = len(ma_analysis)
    ma5_stable_count = sum(1 for m in ma_analysis if m['ma5_stable'])
    ma10_stable_count = sum(1 for m in ma_analysis if m['ma10_stable'])
    ma20_stable_count = sum(1 for m in ma_analysis if m['ma20_stable'])
    overall_stable_count = sum(1 for m in ma_analysis if m['overall_stable'])
    
    print(f"\n📊 D-E均线企稳统计结果:")
    print(f"   分析样本: {total_count} 个成功案例")
    print(f"   5日线企稳 (E≥D): {ma5_stable_count} 个 ({ma5_stable_count/total_count:.1%})")
    print(f"   10日线企稳 (跌幅≤1%): {ma10_stable_count} 个 ({ma10_stable_count/total_count:.1%})")
    print(f"   20日线企稳 (跌幅≤2%): {ma20_stable_count} 个 ({ma20_stable_count/total_count:.1%})")
    print(f"   综合企稳 (≥2条线): {overall_stable_count} 个 ({overall_stable_count/total_count:.1%})")
    
    # 显示企稳案例详情
    print(f"\n🔍 企稳案例详情 (前10个):")
    stable_cases = [m for m in ma_analysis if m['overall_stable']]
    for i, case in enumerate(stable_cases[:10]):
        print(f"   {i+1}. {case['stock']} ({case['date']})")
        print(f"      D点: {case['d_price']:.2f}, E点: {case['e_price']:.2f}, 变化: {case['de_change']:.2f}%")
        print(f"      5日线: {'✅' if case['ma5_stable'] else '❌'}, "
              f"10日线: {'✅' if case['ma10_stable'] else '❌'}, "
              f"20日线: {'✅' if case['ma20_stable'] else '❌'}")
        print(f"      实际涨幅: {case['actual_gain']}")
    
    print(f"\n❌ 未企稳案例 (前5个):")
    unstable_cases = [m for m in ma_analysis if not m['overall_stable']]
    for i, case in enumerate(unstable_cases[:5]):
        print(f"   {i+1}. {case['stock']} ({case['date']})")
        print(f"      D点: {case['d_price']:.2f}, E点: {case['e_price']:.2f}, 变化: {case['de_change']:.2f}%")
        print(f"      5日线: {'✅' if case['ma5_stable'] else '❌'}, "
              f"10日线: {'✅' if case['ma10_stable'] else '❌'}, "
              f"20日线: {'✅' if case['ma20_stable'] else '❌'}")
        print(f"      实际涨幅: {case['actual_gain']}")
    
    return ma_analysis

def analyze_stability_correlation(successful_cases):
    """分析企稳与成功程度的关系"""
    print(f"\n📈 企稳与涨幅关系分析:")
    
    # 获取均线分析结果
    ma_analysis = analyze_de_ma_trend(successful_cases)
    
    if not ma_analysis:
        return
    
    # 分析企稳与涨幅的关系
    stable_cases = [m for m in ma_analysis if m['overall_stable']]
    unstable_cases = [m for m in ma_analysis if not m['overall_stable']]
    
    if stable_cases:
        stable_gains = []
        for case in stable_cases:
            try:
                gain = float(case['actual_gain'])
                stable_gains.append(gain)
            except:
                continue
        
        if stable_gains:
            print(f"   企稳案例 ({len(stable_gains)}个):")
            print(f"     平均涨幅: {np.mean(stable_gains):.1%}")
            print(f"     中位数涨幅: {np.median(stable_gains):.1%}")
            print(f"     最大涨幅: {max(stable_gains):.1%}")
            print(f"     最小涨幅: {min(stable_gains):.1%}")
    
    if unstable_cases:
        unstable_gains = []
        for case in unstable_cases:
            try:
                gain = float(case['actual_gain'])
                unstable_gains.append(gain)
            except:
                continue
        
        if unstable_gains:
            print(f"   未企稳案例 ({len(unstable_gains)}个):")
            print(f"     平均涨幅: {np.mean(unstable_gains):.1%}")
            print(f"     中位数涨幅: {np.median(unstable_gains):.1%}")
            print(f"     最大涨幅: {max(unstable_gains):.1%}")
            print(f"     最小涨幅: {min(unstable_gains):.1%}")
    
    # 按D-E变化幅度分析
    print(f"\n📊 按D-E价格变化分析:")
    
    # 分组分析
    ranges = [
        ("大幅上涨", lambda x: x >= 5),
        ("小幅上涨", lambda x: 0 < x < 5),
        ("持平", lambda x: x == 0),
        ("小幅下跌", lambda x: -5 < x < 0),
        ("大幅下跌", lambda x: x <= -5)
    ]
    
    for range_name, condition in ranges:
        range_cases = [m for m in ma_analysis if condition(m['de_change'])]
        if range_cases:
            range_gains = []
            for case in range_cases:
                try:
                    gain = float(case['actual_gain'])
                    range_gains.append(gain)
                except:
                    continue
            
            if range_gains:
                print(f"   {range_name} ({len(range_cases)}个):")
                print(f"     平均涨幅: {np.mean(range_gains):.1%}")
                print(f"     企稳比例: {sum(1 for c in range_cases if c['overall_stable'])/len(range_cases):.1%}")

def main():
    """主函数"""
    analyze_de_ma_stability()

if __name__ == "__main__":
    main()
