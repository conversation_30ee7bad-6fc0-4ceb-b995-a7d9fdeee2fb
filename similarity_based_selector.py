#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于历史成功案例的相似性选股系统
只从成功的历史案例中学习，然后找到最相似的新样本
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

class SimilarityBasedSelector:
    """基于相似性的选股器"""
    
    def __init__(self):
        self.feature_columns = [
            "A点实体涨跌幅", "A点价格振幅",
            "B点成交量", "B点实体涨跌幅", "B点价格振幅",
            "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量",
            "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"
        ]
        self.scaler = StandardScaler()
        self.successful_cases = None
        self.successful_features = None
        
    def load_and_analyze_historical_data(self, train_file):
        """加载并分析历史数据，提取成功案例"""
        print("📊 加载历史训练数据...")
        df = pd.read_excel(train_file)
        print(f"   历史数据: {len(df)} 条记录")

        # 检查目标列
        target_col = "5日成功选股"
        if target_col not in df.columns:
            print(f"❌ 未找到目标列: {target_col}")
            return False

        # 提取成功案例
        successful_mask = df[target_col] == "成功"
        all_successful_cases = df[successful_mask].copy()

        print(f"✅ 找到 {len(all_successful_cases)} 个成功案例")
        print(f"📊 成功率: {len(all_successful_cases)/len(df):.1%}")

        if len(all_successful_cases) == 0:
            print("❌ 没有找到成功案例")
            return False

        # 只选择涨幅最高的成功案例（前20%）
        gain_col = "5日最大涨幅"
        if gain_col in all_successful_cases.columns:
            # 转换涨幅为数值
            gains = pd.to_numeric(all_successful_cases[gain_col], errors='coerce')
            all_successful_cases = all_successful_cases.copy()
            all_successful_cases['gain_numeric'] = gains

            # 按涨幅排序，选择前20%
            all_successful_cases = all_successful_cases.dropna(subset=['gain_numeric'])
            all_successful_cases = all_successful_cases.sort_values('gain_numeric', ascending=False)

            top_20_percent = max(1, int(len(all_successful_cases) * 0.2))
            self.successful_cases = all_successful_cases.head(top_20_percent)

            print(f"🎯 选择涨幅最高的 {len(self.successful_cases)} 个案例作为模板")
            print(f"📊 涨幅范围: {self.successful_cases['gain_numeric'].min():.1%} - {self.successful_cases['gain_numeric'].max():.1%}")
        else:
            self.successful_cases = all_successful_cases

        # 预处理成功案例的特征
        self.successful_features = self._preprocess_features(self.successful_cases)

        if self.successful_features is None:
            return False

        print(f"✅ 成功案例特征预处理完成: {self.successful_features.shape}")

        # 分析成功案例的特征分布
        self._analyze_successful_patterns()

        return True
    
    def _preprocess_features(self, df):
        """预处理特征数据"""
        try:
            # 检查特征列是否存在
            missing_features = [col for col in self.feature_columns if col not in df.columns]
            if missing_features:
                print(f"❌ 缺失特征列: {missing_features[:5]}...")
                return None
            
            # 提取特征
            features = df[self.feature_columns].copy()
            
            # 转换百分比格式
            percentage_columns = [col for col in self.feature_columns if any(keyword in col for keyword in ['涨跌幅', '振幅', '涨幅', '跌幅'])]
            for col in percentage_columns:
                if col in features.columns:
                    features[col] = pd.to_numeric(features[col].astype(str).str.replace('%', ''), errors='coerce') / 100
            
            # 处理缺失值
            features = features.fillna(features.median())
            
            # 标准化
            features_scaled = self.scaler.fit_transform(features)
            
            return features_scaled
            
        except Exception as e:
            print(f"❌ 特征预处理失败: {e}")
            return None
    
    def _analyze_successful_patterns(self):
        """分析成功案例的模式"""
        print("\n🔍 分析成功案例模式...")
        
        # 显示成功案例的基本信息
        print(f"📈 成功案例统计:")
        for i, (idx, row) in enumerate(self.successful_cases.iterrows()):
            if i < 10:  # 只显示前10个
                stock_info = row.get('股票', f'股票_{i+1}')
                buy_date = row.get('买入日期', 'N/A')
                gain = row.get('5日最大涨幅', 'N/A')
                print(f"   {i+1}. {stock_info} ({buy_date}) - 涨幅: {gain}")
        
        if len(self.successful_cases) > 10:
            print(f"   ... 还有 {len(self.successful_cases)-10} 个成功案例")
    
    def find_similar_candidates(self, test_file, top_k=5, similarity_threshold=0.8):
        """在测试数据中找到与成功案例最相似的候选股票"""
        print(f"\n🔍 在测试数据中寻找相似案例...")
        
        # 加载测试数据
        test_df = pd.read_excel(test_file)
        print(f"   测试数据: {len(test_df)} 条记录")
        
        # 预处理测试数据特征
        test_features = self._preprocess_test_features(test_df)
        if test_features is None:
            return None
            
        # 计算相似度
        similarities = self._calculate_similarities(test_features)
        
        # 找到最相似的候选
        candidates = self._select_top_candidates(test_df, similarities, top_k, similarity_threshold)
        
        return candidates
    
    def _preprocess_test_features(self, df):
        """预处理测试数据特征"""
        try:
            # 检查特征列
            missing_features = [col for col in self.feature_columns if col not in df.columns]
            if missing_features:
                print(f"❌ 测试数据缺失特征列: {missing_features[:5]}...")
                return None
            
            # 提取特征
            features = df[self.feature_columns].copy()
            
            # 转换百分比格式
            percentage_columns = [col for col in self.feature_columns if any(keyword in col for keyword in ['涨跌幅', '振幅', '涨幅', '跌幅'])]
            for col in percentage_columns:
                if col in features.columns:
                    features[col] = pd.to_numeric(features[col].astype(str).str.replace('%', ''), errors='coerce') / 100
            
            # 处理缺失值
            features = features.fillna(features.median())
            
            # 使用训练时的标准化器
            features_scaled = self.scaler.transform(features)
            
            return features_scaled
            
        except Exception as e:
            print(f"❌ 测试数据预处理失败: {e}")
            return None
    
    def _calculate_similarities(self, test_features):
        """计算测试样本与成功案例的相似度"""
        print("🧮 计算相似度...")
        
        # 计算余弦相似度
        cosine_sim = cosine_similarity(test_features, self.successful_features)
        
        # 计算欧几里得距离（转换为相似度）
        euclidean_dist = euclidean_distances(test_features, self.successful_features)
        euclidean_sim = 1 / (1 + euclidean_dist)
        
        # 综合相似度（余弦相似度权重更高）
        combined_sim = 0.7 * cosine_sim + 0.3 * euclidean_sim
        
        # 对每个测试样本，取与所有成功案例的最大相似度
        max_similarities = np.max(combined_sim, axis=1)
        
        # 同时记录最相似的成功案例索引
        best_match_indices = np.argmax(combined_sim, axis=1)
        
        return max_similarities, best_match_indices
    
    def _select_top_candidates(self, test_df, similarities, top_k, similarity_threshold):
        """选择最佳候选股票"""
        max_similarities, best_match_indices = similarities
        
        # 创建候选列表
        candidates = []
        for i, (similarity, match_idx) in enumerate(zip(max_similarities, best_match_indices)):
            if similarity >= similarity_threshold:
                candidate = {
                    'index': i,
                    'similarity': similarity,
                    'best_match_idx': match_idx,
                    'stock_info': test_df.iloc[i].get('股票', f'股票_{i+1}'),
                    'buy_date': test_df.iloc[i].get('买入日期', 'N/A'),
                    'actual_gain': test_df.iloc[i].get('5日最大涨幅', 'N/A')
                }
                candidates.append(candidate)
        
        # 按相似度排序
        candidates.sort(key=lambda x: x['similarity'], reverse=True)
        
        # 取前top_k个
        top_candidates = candidates[:top_k]
        
        print(f"\n🎯 找到 {len(candidates)} 个相似度≥{similarity_threshold:.1%}的候选")
        print(f"📊 选择前 {len(top_candidates)} 个最相似的候选:")
        
        for i, candidate in enumerate(top_candidates):
            match_case = self.successful_cases.iloc[candidate['best_match_idx']]
            match_stock = match_case.get('股票', 'N/A')
            match_gain = match_case.get('5日最大涨幅', 'N/A')
            
            print(f"   {i+1}. {candidate['stock_info']} (相似度: {candidate['similarity']:.1%})")
            print(f"      最相似案例: {match_stock} (涨幅: {match_gain})")
            print(f"      实际涨幅: {candidate['actual_gain']}")
        
        return top_candidates

def main():
    """主函数"""
    print("🎯 基于历史成功案例的相似性选股系统")
    print("=" * 80)
    
    # 数据文件路径
    train_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    test_file = "选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    # 创建选股器
    selector = SimilarityBasedSelector()
    
    # 加载历史成功案例
    if not selector.load_and_analyze_historical_data(train_file):
        print("❌ 历史数据加载失败")
        return
    
    # 寻找相似候选 - 使用更严格的阈值
    candidates = selector.find_similar_candidates(
        test_file,
        top_k=2,  # 只选择2个最相似的
        similarity_threshold=0.85  # 相似度阈值85%
    )
    
    if candidates:
        print(f"\n🎉 成功找到 {len(candidates)} 个高相似度候选股票！")
        print("💡 这些股票与历史成功案例最相似，建议重点关注")
        
        # 计算精确率
        if all('actual_gain' in c and c['actual_gain'] != 'N/A' for c in candidates):
            successful_predictions = 0
            for candidate in candidates:
                try:
                    gain = float(str(candidate['actual_gain']).replace('%', ''))
                    if gain >= 10:  # 涨幅≥10%算成功
                        successful_predictions += 1
                except:
                    pass
            
            precision = successful_predictions / len(candidates) if candidates else 0
            print(f"\n📊 相似性选股精确率: {precision:.1%} ({successful_predictions}/{len(candidates)})")
    else:
        print("\n⚠️ 没有找到足够相似的候选股票")

if __name__ == "__main__":
    main()
