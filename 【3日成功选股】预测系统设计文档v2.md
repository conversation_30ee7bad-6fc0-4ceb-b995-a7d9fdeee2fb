# 🎯 3日成功选股预测系统设计文档 v2.0

## 📋 系统概述

### 目标
基于现有的成功选股预测系统，修改目标变量为"3日成功选股"，使用机器学习方法预测短期内表现优秀的投资机会。

### 核心变更
- **原目标**: "成功选股" (基于3日最大涨幅≥5%判定)
- **新目标**: "3日成功选股" (基于3日最大涨幅≥5% **且** 最大跌幅>-3%判定)
- **数据来源**: 直接使用数据集中已有的"3日成功选股"列，无需重新计算

### 数据集配置
- **训练集**: `选股分析结果/选股分析结果_20250728_230031.xlsx`
- **验证集**: `选股分析结果/选股分析结果_20250728_225651.xlsx`
- **目标列**: `3日成功选股` (已存在于数据集中)
- **涨跌幅数据**: 百分制格式，无需转换

## 🏗️ 系统架构

### 文件组织结构
```
StockAssistant/
├── config.json                      # 系统配置文件
├── train_3d_success.py             # 训练脚本
├── predict_3d_success.py           # 预测脚本
├── models/                          # 模型存储目录
│   ├── training_YYYYMMDD_HHMMSS/   # 每次训练生成的文件夹
│   │   ├── models/                  # 训练好的模型文件
│   │   ├── reports/                 # 训练报告和分析
│   │   ├── visualizations/         # 可视化图表
│   │   └── config.json             # 训练时的配置快照
│   └── predictions_YYYYMMDD_HHMMSS/ # 每次预测生成的文件夹
│       ├── results/                 # 预测结果文件
│       ├── visualizations/         # 预测可视化
│       └── summary.json            # 预测摘要
└── 选股分析结果/                    # 数据目录
    ├── 选股分析结果_20250728_230031.xlsx  # 训练数据
    └── 选股分析结果_20250728_225651.xlsx  # 验证数据
```

### 配置驱动设计
所有关键参数通过 `config.json` 配置：
- 数据文件路径
- 特征列表
- 模型参数
- 输出目录
- 可视化设置

## 📊 数据处理

### 目标变量处理
```python
# 直接使用数据集中的"3日成功选股"列
target_column = config["data_config"]["target_column"]  # "3日成功选股"

# 无需重新计算，直接提取
y = df[target_column].copy()
```

### 特征处理
- **输入特征**: 47个技术指标 (从config.json读取)
- **特征选择**: 互信息方法选择Top 20特征
- **数据清洗**: 处理缺失值、异常值
- **标准化**: StandardScaler标准化

### 涨跌幅数据
- **格式**: 百分制 (5.0表示5%)
- **列名**: "3日最大涨幅", "3日最大跌幅", "5日最大涨幅", "5日最大跌幅", "10日最大涨幅", "10日最大跌幅"
- **处理**: 直接使用，无需转换

## 🤖 机器学习流程

### 训练流程
1. **配置加载**: 从config.json读取所有配置
2. **数据加载**: 加载训练集和验证集
3. **特征工程**: 数据清洗、标准化、特征选择
4. **模型训练**: 训练4种机器学习模型
5. **模型评估**: 交叉验证、验证集评估
6. **结果保存**: 模型、报告、可视化保存到独立文件夹
7. **报告生成**: HTML训练报告

### 预测流程
1. **模型加载**: 从指定训练文件夹加载模型
2. **数据预处理**: 使用训练时的预处理器
3. **集成预测**: 多模型投票预测
4. **结果输出**: 预测结果、可视化保存到独立文件夹
5. **报告生成**: 预测摘要和可视化

## 📈 可视化设计

### 训练阶段可视化
1. **模型性能对比**: 准确率、AUC、F1-Score
2. **ROC曲线**: 各模型ROC曲线对比
3. **混淆矩阵**: 最佳模型分类详情
4. **特征重要性**: 随机森林特征重要性
5. **交叉验证**: 模型稳定性分析
6. **学习曲线**: 训练过程可视化

### 预测阶段可视化
1. **预测分布**: 成功/失败预测分布
2. **置信度分析**: 预测概率分布
3. **推荐股票**: 高置信度股票展示
4. **风险评估**: 不同概率区间分析
5. **模型一致性**: 各模型预测一致性
6. **特征贡献**: 重要特征贡献分析

## 🔧 技术实现要点

### 1. 配置管理
```python
import json

def load_config(config_path="config.json"):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

config = load_config()
```

### 2. 文件夹管理
```python
def create_training_folder(base_dir="models"):
    """创建训练输出文件夹"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    folder_name = f"{base_dir}/training_{timestamp}"
    
    for subdir in ['models', 'reports', 'visualizations']:
        os.makedirs(f"{folder_name}/{subdir}", exist_ok=True)
    
    return folder_name

def create_prediction_folder(base_dir="models"):
    """创建预测输出文件夹"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    folder_name = f"{base_dir}/predictions_{timestamp}"
    
    for subdir in ['results', 'visualizations']:
        os.makedirs(f"{folder_name}/{subdir}", exist_ok=True)
    
    return folder_name
```

### 3. 模型管理
```python
def save_models(models, folder_path):
    """保存所有模型"""
    for model_name, model in models.items():
        model_path = f"{folder_path}/models/{model_name}_model.pkl"
        joblib.dump(model, model_path)

def load_models(folder_path):
    """加载所有模型"""
    models = {}
    model_files = glob.glob(f"{folder_path}/models/*_model.pkl")
    
    for model_file in model_files:
        model_name = os.path.basename(model_file).replace('_model.pkl', '')
        models[model_name] = joblib.load(model_file)
    
    return models
```

## 📋 使用说明

### 训练模型
```bash
# 使用默认配置训练
python train_3d_success.py

# 使用自定义配置训练
python train_3d_success.py --config custom_config.json
```

### 预测新数据
```bash
# 使用最新训练的模型预测
python predict_3d_success.py --input_file new_data.xlsx

# 使用指定训练文件夹的模型预测
python predict_3d_success.py --input_file new_data.xlsx --training_folder models/training_20250728_143022

# 使用自定义配置预测
python predict_3d_success.py --input_file new_data.xlsx --config custom_config.json

python .\predict_3d_success.py  --input_file .\选股分析结果\选股分析结果_20250729_093200.xlsx --training_folder .\models\training_20250729_235516 --threshold 0.6
```

### 输出结果
- **训练结果**: `models/training_YYYYMMDD_HHMMSS/`
- **预测结果**: `models/predictions_YYYYMMDD_HHMMSS/`

## ⚠️ 重要说明

### 1. 目标变量使用
- **直接使用**: 数据集中已有的"3日成功选股"列
- **无需计算**: 不重新计算成功判断逻辑
- **数据格式**: 假设为"成功"/"失败"/"未知"格式

### 2. 涨跌幅数据
- **百分制**: 5.0表示5%，-3.0表示-3%
- **无需转换**: 直接使用原始数值
- **列名固定**: 按照数据集中的列名使用

### 3. 兼容性
- **基于现有脚本**: 在success_prediction_system.py和quick_predict.py基础上修改
- **保持接口**: 尽量保持原有接口和使用方式
- **增强功能**: 添加配置文件、独立文件夹等新功能

## 🚀 系统优势

1. **配置驱动**: 所有参数可通过配置文件调整
2. **版本管理**: 每次训练/预测生成独立文件夹
3. **完整记录**: 详细的训练报告和预测摘要
4. **可视化丰富**: 多维度图表展示结果
5. **易于使用**: 简单的命令行接口
6. **高度兼容**: 基于现有成熟脚本修改

---

**系统版本**: v2.0 (基于现有脚本修改版)  
**更新日期**: 2025-07-28  
**核心特点**: 配置驱动、版本管理、完整可视化  
**兼容性**: 基于success_prediction_system.py和quick_predict.py
