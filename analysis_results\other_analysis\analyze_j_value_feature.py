#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析E点J值相对D点J值涨幅与5日选股成功率的关系
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置字体 - 使用英文避免中文渲染问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def analyze_j_value_feature():
    """分析E点J值相对D点J值涨幅特征"""
    print("📊 分析E点J值相对D点J值涨幅与5日选股成功率的关系")
    print("=" * 80)
    
    # 加载训练数据
    train_file = "../../选股分析结果/2025-01-01-2025-04-15.xlsx"
    test_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    print("📂 加载数据...")
    train_df = pd.read_excel(train_file)
    test_df = pd.read_excel(test_file)
    
    print(f"   训练数据: {len(train_df)} 条记录")
    print(f"   测试数据: {len(test_df)} 条记录")
    
    # 合并数据进行整体分析
    all_df = pd.concat([train_df, test_df], ignore_index=True)
    print(f"   总数据: {len(all_df)} 条记录")
    
    # 检查关键列
    feature_col = "E点J值相对D点J值涨幅"
    target_col = "5日成功选股"
    
    if feature_col not in all_df.columns:
        print(f"❌ 未找到特征列: {feature_col}")
        return
    
    if target_col not in all_df.columns:
        print(f"❌ 未找到目标列: {target_col}")
        return
    
    # 数据预处理
    print("\n🔧 数据预处理...")
    
    # 转换特征值为数值
    feature_values = all_df[feature_col].copy()
    
    # 处理百分比格式
    if feature_values.dtype == 'object':
        feature_values = pd.to_numeric(feature_values.astype(str).str.replace('%', ''), errors='coerce')
    
    # 创建分析数据框
    analysis_df = pd.DataFrame({
        'j_value_change': feature_values,
        'success': all_df[target_col] == "成功"
    })
    
    # 移除缺失值
    analysis_df = analysis_df.dropna()
    print(f"   有效数据: {len(analysis_df)} 条")
    
    if len(analysis_df) == 0:
        print("❌ 没有有效数据")
        return
    
    # 基本统计
    print(f"\n📈 基本统计:")
    print(f"   总成功案例: {analysis_df['success'].sum()} 个")
    print(f"   总成功率: {analysis_df['success'].mean():.1%}")
    print(f"   J值涨幅范围: {analysis_df['j_value_change'].min():.1f}% ~ {analysis_df['j_value_change'].max():.1f}%")
    print(f"   J值涨幅均值: {analysis_df['j_value_change'].mean():.1f}%")
    print(f"   J值涨幅中位数: {analysis_df['j_value_change'].median():.1f}%")
    
    # 按区间分析
    print(f"\n📊 按J值涨幅区间分析:")
    print("-" * 60)
    
    # 定义区间
    bins = [-np.inf, -50, -20, -10, -5, 0, 5, 10, 20, 50, np.inf]
    labels = ['<-50%', '-50%~-20%', '-20%~-10%', '-10%~-5%', '-5%~0%', 
              '0%~5%', '5%~10%', '10%~20%', '20%~50%', '>50%']
    
    analysis_df['j_value_range'] = pd.cut(analysis_df['j_value_change'], bins=bins, labels=labels)
    
    # 计算每个区间的成功率
    range_stats = analysis_df.groupby('j_value_range').agg({
        'success': ['count', 'sum', 'mean']
    }).round(3)
    
    range_stats.columns = ['总数量', '成功数量', '成功率']
    range_stats['成功率%'] = (range_stats['成功率'] * 100).round(1)
    
    print(f"{'区间':<12} {'总数量':<8} {'成功数量':<8} {'成功率':<8}")
    print("-" * 60)
    for idx, row in range_stats.iterrows():
        if row['总数量'] > 0:  # 只显示有数据的区间
            print(f"{idx:<12} {int(row['总数量']):<8} {int(row['成功数量']):<8} {row['成功率%']:<8.1f}%")
    
    # 寻找最佳区间
    best_ranges = range_stats[range_stats['总数量'] >= 10].sort_values('成功率', ascending=False)
    
    if len(best_ranges) > 0:
        print(f"\n🏆 成功率最高的区间 (样本≥10):")
        for i, (idx, row) in enumerate(best_ranges.head(3).iterrows()):
            print(f"   {i+1}. {idx}: 成功率 {row['成功率%']:.1f}% ({int(row['成功数量'])}/{int(row['总数量'])})")
    
    # 详细分析高成功率区间
    print(f"\n🔍 详细分析:")
    
    # 成功案例的J值分布
    success_cases = analysis_df[analysis_df['success'] == True]
    fail_cases = analysis_df[analysis_df['success'] == False]
    
    print(f"   成功案例J值涨幅统计:")
    print(f"     均值: {success_cases['j_value_change'].mean():.1f}%")
    print(f"     中位数: {success_cases['j_value_change'].median():.1f}%")
    print(f"     标准差: {success_cases['j_value_change'].std():.1f}%")
    print(f"     25%分位数: {success_cases['j_value_change'].quantile(0.25):.1f}%")
    print(f"     75%分位数: {success_cases['j_value_change'].quantile(0.75):.1f}%")
    
    print(f"   失败案例J值涨幅统计:")
    print(f"     均值: {fail_cases['j_value_change'].mean():.1f}%")
    print(f"     中位数: {fail_cases['j_value_change'].median():.1f}%")
    print(f"     标准差: {fail_cases['j_value_change'].std():.1f}%")
    
    # 寻找最优阈值
    print(f"\n🎯 寻找最优阈值:")
    
    # 测试不同阈值的效果
    thresholds = np.percentile(analysis_df['j_value_change'], [10, 20, 30, 40, 50, 60, 70, 80, 90])
    
    print(f"{'阈值':<8} {'>=阈值数量':<12} {'>=阈值成功数':<12} {'>=阈值成功率':<12} {'<阈值成功率':<12}")
    print("-" * 70)
    
    best_threshold = None
    best_precision = 0
    
    for threshold in thresholds:
        above_threshold = analysis_df[analysis_df['j_value_change'] >= threshold]
        below_threshold = analysis_df[analysis_df['j_value_change'] < threshold]
        
        if len(above_threshold) >= 5:  # 至少5个样本
            above_success_rate = above_threshold['success'].mean()
            below_success_rate = below_threshold['success'].mean() if len(below_threshold) > 0 else 0
            
            print(f"{threshold:<8.1f} {len(above_threshold):<12} {above_threshold['success'].sum():<12} {above_success_rate:<12.1%} {below_success_rate:<12.1%}")
            
            if above_success_rate > best_precision and len(above_threshold) >= 10:
                best_precision = above_success_rate
                best_threshold = threshold
    
    if best_threshold is not None:
        print(f"\n✅ 最优阈值: {best_threshold:.1f}%")
        print(f"   在此阈值以上的成功率: {best_precision:.1%}")
        
        # 应用到测试数据
        print(f"\n🧪 应用到测试数据:")
        test_analysis = test_df.copy()
        test_feature_values = pd.to_numeric(test_analysis[feature_col].astype(str).str.replace('%', ''), errors='coerce')
        
        candidates = test_analysis[test_feature_values >= best_threshold]
        print(f"   测试数据中符合条件的股票: {len(candidates)} 只")
        
        if len(candidates) > 0:
            print(f"   推荐股票:")
            for i, (idx, row) in enumerate(candidates.head(5).iterrows()):
                stock = row.get('股票', 'N/A')
                date = row.get('买入日期', 'N/A')
                j_value = test_feature_values.iloc[idx] if idx < len(test_feature_values) else 'N/A'
                actual_gain = row.get('5日最大涨幅', 'N/A')
                print(f"     {i+1}. {stock} ({date}) - J值涨幅: {j_value:.1f}%, 实际涨幅: {actual_gain}")
    
    # 可视化分析
    create_visualizations(analysis_df, range_stats)

def create_visualizations(analysis_df, range_stats):
    """创建可视化图表"""
    print(f"\n🎨 生成可视化图表...")
    
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('E-Point J-Value Change vs 5-Day Success Rate Analysis', fontsize=16, fontweight='bold')
    
    # 1. 成功率按区间分布
    ax1 = axes[0, 0]
    valid_ranges = range_stats[range_stats['总数量'] > 0]
    bars = ax1.bar(range(len(valid_ranges)), valid_ranges['成功率%'], 
                   color=['red' if x < 15 else 'orange' if x < 25 else 'green' for x in valid_ranges['成功率%']])
    ax1.set_title('Success Rate by J-Value Change Range', fontweight='bold')
    ax1.set_xlabel('J-Value Change Range')
    ax1.set_ylabel('Success Rate (%)')
    ax1.set_xticks(range(len(valid_ranges)))
    ax1.set_xticklabels(valid_ranges.index, rotation=45)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 2. J值分布直方图
    ax2 = axes[0, 1]
    success_data = analysis_df[analysis_df['success'] == True]['j_value_change']
    fail_data = analysis_df[analysis_df['success'] == False]['j_value_change']
    
    ax2.hist([success_data, fail_data], bins=30, alpha=0.7, 
             label=['Success Cases', 'Failure Cases'], color=['green', 'red'])
    ax2.set_title('J-Value Change Distribution Comparison', fontweight='bold')
    ax2.set_xlabel('J-Value Change (%)')
    ax2.set_ylabel('Frequency')
    ax2.legend()
    
    # 3. 样本数量分布
    ax3 = axes[1, 0]
    bars = ax3.bar(range(len(valid_ranges)), valid_ranges['总数量'], alpha=0.7, color='skyblue')
    ax3.set_title('Sample Count Distribution by Range', fontweight='bold')
    ax3.set_xlabel('J-Value Change Range')
    ax3.set_ylabel('Sample Count')
    ax3.set_xticks(range(len(valid_ranges)))
    ax3.set_xticklabels(valid_ranges.index, rotation=45)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontsize=9)
    
    # 4. 成功数量分布
    ax4 = axes[1, 1]
    bars = ax4.bar(range(len(valid_ranges)), valid_ranges['成功数量'], alpha=0.7, color='lightgreen')
    ax4.set_title('Success Case Count by Range', fontweight='bold')
    ax4.set_xlabel('J-Value Change Range')
    ax4.set_ylabel('Success Case Count')
    ax4.set_xticks(range(len(valid_ranges)))
    ax4.set_xticklabels(valid_ranges.index, rotation=45)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{int(height)}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = "j_value_analysis.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {output_file}")
    
    plt.show()

if __name__ == "__main__":
    analyze_j_value_feature()
