#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选股结果分析主程序
整合数据过滤和HTML报告生成
"""

import os
import sys
import argparse
from datetime import datetime

# 导入自定义模块
from stock_result_filter import StockResultFilter
from html_report_generator import HTMLReportGenerator

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='选股结果分析系统')
    parser.add_argument('excel_file',
                       help='Excel源文件路径，如: 选股分析结果/选股分析结果_20250804_222043.xlsx')
    parser.add_argument('--max-days-gap', type=int, default=15,
                       help='时间合并窗口（天），默认15天')
    parser.add_argument('--success-threshold', type=float, default=10.0,
                       help='成功阈值（%%），默认10.0%%')
    parser.add_argument('--output-dir', default='.',
                       help='输出目录，默认当前目录')

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    print("🚀 选股结果分析系统")
    print("=" * 60)

    # 配置参数
    excel_file = args.excel_file

    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    json_file = os.path.join(args.output_dir, f"composite_stock_analysis_{timestamp}.json")
    html_file = os.path.join(args.output_dir, f"stock_analysis_report_{timestamp}.html")

    max_days_gap = args.max_days_gap      # 时间合并窗口（天）
    success_threshold = args.success_threshold  # 成功阈值（%）
    
    print(f"📋 分析参数:")
    print(f"   源文件: {excel_file}")
    print(f"   时间合并窗口: {max_days_gap} 天")
    print(f"   成功阈值: {success_threshold}%")
    print(f"   输出JSON: {json_file}")
    print(f"   输出HTML: {html_file}")
    print()
    
    try:
        # 第一步：数据过滤和分析
        print("🔍 第一步：数据过滤和分析")
        print("-" * 40)
        
        if not os.path.exists(excel_file):
            print(f"❌ 源文件不存在: {excel_file}")
            print("请确认文件路径是否正确")
            return False
        
        # 创建过滤器并运行分析
        filter_tool = StockResultFilter(excel_file)
        output_data = filter_tool.run_analysis(
            max_days_gap=max_days_gap,
            success_threshold=success_threshold
        )

        # 保存到指定的JSON文件（包含月度统计数据）
        import json
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 数据分析完成，结果保存到: {json_file}")
        
        # 第二步：生成HTML报告
        print("\n🎨 第二步：生成HTML可视化报告")
        print("-" * 40)
        
        # 创建HTML报告生成器
        generator = HTMLReportGenerator(json_file)
        generator.load_data()
        generated_html = generator.generate_html_report(html_file)
        
        print(f"✅ HTML报告生成完成: {generated_html}")
        
        # 第三步：显示结果摘要
        print("\n📊 第三步：分析结果摘要")
        print("-" * 40)
        
        summary = output_data.get('summary', {})
        analysis_info = output_data.get('analysis_info', {})
        
        print(f"📈 核心指标:")
        print(f"   总股票数: {summary.get('total_stocks', 0)}")
        print(f"   原始记录: {analysis_info.get('total_original_records', 0)} 条")
        print(f"   复合记录: {summary.get('total_composite_records', 0)} 条")
        print(f"   成功记录: {summary.get('successful_records', 0)} 条")
        print(f"   成功率: {summary.get('success_rate', 0):.2f}%")
        
        # 计算压缩率
        original_count = analysis_info.get('total_original_records', 0)
        composite_count = summary.get('total_composite_records', 0)
        if original_count > 0:
            compression_rate = (original_count - composite_count) / original_count * 100
            print(f"   数据压缩率: {compression_rate:.1f}%")
        
        # 第四步：尝试打开浏览器
        print(f"\n🌐 第四步：打开可视化报告")
        print("-" * 40)
        
        try:
            import webbrowser
            full_path = os.path.abspath(html_file)
            webbrowser.open(f"file://{full_path}")
            print(f"🚀 已自动在浏览器中打开报告")
            print(f"📄 报告路径: {full_path}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {str(e)}")
            print(f"💡 请手动在浏览器中打开: {os.path.abspath(html_file)}")
        
        print(f"\n🎉 分析完成!")
        print("=" * 60)
        
        # 显示文件清单
        print(f"📁 生成的文件:")
        if os.path.exists(json_file):
            print(f"   ✅ {json_file} ({os.path.getsize(json_file):,} 字节)")
        if os.path.exists(html_file):
            print(f"   ✅ {html_file} ({os.path.getsize(html_file):,} 字节)")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ 文件错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖"""
    required_modules = ['pandas', 'numpy', 'openpyxl']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要的Python模块: {', '.join(missing_modules)}")
        print(f"请运行: pip install {' '.join(missing_modules)}")
        return False
    
    return True

if __name__ == "__main__":
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查命令行参数
    if len(sys.argv) < 2:
        print("📋 使用方法:")
        print("python run_stock_analysis.py <Excel文件路径> [选项]")
        print()
        print("示例:")
        print("python run_stock_analysis.py 选股分析结果/选股分析结果_20250804_222043.xlsx")
        print("python run_stock_analysis.py data.xlsx --max-days-gap 20 --success-threshold 15.0")
        print()
        print("选项:")
        print("  --max-days-gap      时间合并窗口（天），默认15天")
        print("  --success-threshold 成功阈值（%），默认10.0%")
        print("  --output-dir        输出目录，默认当前目录")
        sys.exit(1)

    # 运行主程序
    success = main()

    if success:
        print(f"\n💡 使用说明:")
        print(f"1. JSON文件包含了详细的分析数据，可用于进一步处理")
        print(f"2. HTML文件提供了可视化的报告，支持交互式查看")
        print(f"3. 详情默认展开，点击'收起详情'按钮可折叠")
        print(f"4. 点击表头可对数据进行排序")
        print(f"5. 文件名包含时间戳，避免覆盖之前的分析结果")
        sys.exit(0)
    else:
        sys.exit(1)
