{"data": {"input_file": "选股分析结果/选股分析结果_20250730_225530.xlsx", "target_column": "3日最大涨幅", "stock_column": "股票", "feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"]}, "data_split": {"test_size": 0.2, "val_size": 0.2, "random_state": 42, "ensure_stock_separation": true}, "model": {"hidden_dim": 256, "dropout_rate": 0.3, "activation": "relu"}, "training": {"epochs": 200, "batch_size": 32, "learning_rate": 0.001, "weight_decay": 0.0001, "early_stopping_patience": 30, "lr_scheduler_patience": 10, "lr_scheduler_factor": 0.5}, "preprocessing": {"remove_outliers": true, "outlier_threshold": {"min_gain": -50, "max_gain": 100}, "handle_missing": "mean", "standardize_features": true}, "evaluation": {"metrics": ["mse", "mae", "r2", "mape"], "top_k_predictions": 20}, "output": {"save_model": true, "save_predictions": true, "generate_plots": true, "plot_format": "png", "plot_dpi": 300}}