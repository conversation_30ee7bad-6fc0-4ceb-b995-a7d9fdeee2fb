#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析被选出股票的移动平均线企稳情况
计算5日线、10日线和20日线在选股日期的企稳状态
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def analyze_ma_stability():
    """分析移动平均线企稳情况"""
    print("📊 分析被选出股票的移动平均线企稳情况")
    print("=" * 80)
    
    # 加载数据
    data_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    print(f"📂 加载数据: {data_file}")
    
    df = pd.read_excel(data_file)
    print(f"   总数据: {len(df)} 条记录")
    
    # 检查关键列
    required_columns = ['日期', '股票', '5日成功选股']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺失关键列: {missing_columns}")
        return
    
    # 查看数据结构
    print(f"\n📋 数据列信息:")
    print(f"   总列数: {len(df.columns)}")
    print(f"   前10列: {list(df.columns[:10])}")
    
    # 查找价格相关列
    price_columns = [col for col in df.columns if any(keyword in col.lower() for keyword in 
                    ['价格', 'price', '收盘', 'close', '开盘', 'open', '最高', 'high', '最低', 'low'])]
    print(f"   价格相关列: {price_columns}")
    
    # 查找可能的移动平均线列
    ma_columns = [col for col in df.columns if any(keyword in col for keyword in 
                 ['5日', '10日', '20日', 'MA5', 'MA10', 'MA20', '均线'])]
    print(f"   移动平均线相关列: {ma_columns}")
    
    # 分析成功案例
    target_col = "5日成功选股"
    successful_mask = df[target_col] == "成功"
    successful_cases = df[successful_mask].copy()
    
    print(f"\n✅ 成功案例: {len(successful_cases)} 个")
    
    if len(successful_cases) == 0:
        print("❌ 没有找到成功案例")
        return
    
    # 显示成功案例的基本信息
    print(f"\n📈 成功案例样本:")
    for i, (idx, row) in enumerate(successful_cases.head(10).iterrows()):
        stock = row.get('股票', 'N/A')
        date = row.get('日期', 'N/A')
        gain = row.get('5日最大涨幅', 'N/A')
        print(f"   {i+1}. {stock} ({date}) - 涨幅: {gain}")
    
    # 尝试从现有数据中分析移动平均线企稳
    analyze_existing_ma_data(successful_cases, df)
    
    # 如果没有现成的移动平均线数据，尝试计算
    if not ma_columns:
        print(f"\n🔧 未找到现成的移动平均线数据，尝试计算...")
        calculate_ma_stability(successful_cases)
    
    # 分析价格相关特征与企稳的关系
    analyze_price_features(successful_cases)

def analyze_existing_ma_data(successful_cases, all_df):
    """分析现有的移动平均线数据"""
    print(f"\n🔍 分析现有移动平均线相关数据...")
    
    # 查找可能包含移动平均线信息的列
    potential_ma_cols = []
    for col in all_df.columns:
        if any(keyword in col for keyword in ['均线', 'MA', '5日', '10日', '20日']):
            potential_ma_cols.append(col)
    
    if potential_ma_cols:
        print(f"   找到可能的移动平均线列: {potential_ma_cols}")
        
        for col in potential_ma_cols:
            print(f"\n   分析列: {col}")
            values = successful_cases[col].dropna()
            if len(values) > 0:
                print(f"     有效数据: {len(values)} 个")
                print(f"     数据类型: {values.dtype}")
                print(f"     样本值: {list(values.head())}")
    else:
        print("   未找到明确的移动平均线列")

def calculate_ma_stability(successful_cases):
    """计算移动平均线企稳情况"""
    print(f"\n🧮 计算移动平均线企稳情况...")
    
    # 查找价格列
    price_cols = []
    for col in successful_cases.columns:
        if any(keyword in col.lower() for keyword in ['收盘', 'close', '价格', 'price']):
            price_cols.append(col)
    
    if not price_cols:
        # 尝试从其他列推断价格
        print("   未找到明确的价格列，尝试从其他数据推断...")
        
        # 查看是否有点位相关的价格信息
        point_cols = [col for col in successful_cases.columns if any(point in col for point in ['A点', 'B点', 'C点', 'D点', 'E点'])]
        if point_cols:
            print(f"   找到点位相关列: {point_cols[:5]}...")
            
            # 尝试使用E点作为当前价格参考
            e_point_cols = [col for col in successful_cases.columns if 'E点' in col and any(keyword in col for keyword in ['收盘', '价格', '最低', '最高'])]
            if e_point_cols:
                print(f"   使用E点价格列: {e_point_cols}")
                analyze_point_based_stability(successful_cases, e_point_cols)
        
        return
    
    print(f"   找到价格列: {price_cols}")
    
    # 由于我们只有单个时间点的数据，无法计算真正的移动平均线
    # 但可以分析价格相对位置和趋势特征
    for price_col in price_cols[:1]:  # 只分析第一个价格列
        analyze_price_position(successful_cases, price_col)

def analyze_point_based_stability(successful_cases, e_point_cols):
    """基于点位数据分析企稳情况"""
    print(f"\n📊 基于点位数据分析企稳情况...")

    # 分析A-E点的价格走势
    points = ['A', 'B', 'C', 'D', 'E']
    point_data = {}

    for point in points:
        # 查找该点的收盘价列
        close_col = f'{point}点收盘'
        if close_col in successful_cases.columns:
            point_data[point] = close_col

    print(f"   找到点位价格数据: {list(point_data.keys())}")

    if len(point_data) >= 5:  # 需要A-E所有点来分析移动平均线
        analyze_point_trend(successful_cases, point_data)
    else:
        print(f"   ❌ 数据不足，需要A-E所有点的收盘价数据")

def analyze_point_trend(successful_cases, point_data):
    """分析点位趋势"""
    print(f"\n📈 分析点位价格趋势...")

    trend_analysis = []
    ma_analysis = []

    for idx, row in successful_cases.iterrows():
        stock = row.get('股票', 'N/A')
        date = row.get('日期', 'N/A')
        actual_gain = row.get('5日最大涨幅', 'N/A')

        # 提取各点收盘价
        point_prices = {}
        for point in ['A', 'B', 'C', 'D', 'E']:
            close_col = f'{point}点收盘'
            if close_col in row:
                try:
                    price = pd.to_numeric(row[close_col], errors='coerce')
                    if pd.notna(price):
                        point_prices[point] = price
                except:
                    continue

        if len(point_prices) >= 5:  # 需要A-E所有点
            # 按顺序排列价格
            prices = [point_prices[p] for p in ['A', 'B', 'C', 'D', 'E']]

            # 计算移动平均线企稳情况
            ma_stability = calculate_ma_stability_from_points(prices)

            # 计算趋势得分
            trend_score = analyze_trend_stability(prices)

            analysis_result = {
                'stock': stock,
                'date': date,
                'actual_gain': actual_gain,
                'prices': prices,
                'ma5_stable': ma_stability['ma5_stable'],
                'ma10_stable': ma_stability['ma10_stable'],
                'ma20_stable': ma_stability['ma20_stable'],
                'trend_score': trend_score,
                'is_stable': ma_stability['overall_stable']
            }

            trend_analysis.append(analysis_result)
            ma_analysis.append(ma_stability)

    # 统计企稳情况
    if trend_analysis:
        analyze_ma_stability_results(trend_analysis)

        # 分析企稳与成功率的关系
        analyze_stability_success_correlation(trend_analysis)

def calculate_ma_stability_from_points(prices):
    """基于5个点位计算移动平均线企稳情况"""
    if len(prices) != 5:
        return {'ma5_stable': False, 'ma10_stable': False, 'ma20_stable': False, 'overall_stable': False}

    # 模拟移动平均线计算
    # 由于只有5个点，我们用这5个点来模拟不同周期的移动平均线

    # 5日线：最近3个点的平均
    ma5_recent = np.mean(prices[-3:])  # C, D, E点平均
    ma5_earlier = np.mean(prices[-4:-1])  # B, C, D点平均

    # 10日线：最近4个点的平均
    ma10_recent = np.mean(prices[-4:])  # B, C, D, E点平均
    ma10_earlier = np.mean(prices[-5:-1])  # A, B, C, D点平均

    # 20日线：所有5个点的平均
    ma20_recent = np.mean(prices)  # 所有点平均
    ma20_earlier = np.mean(prices[:-1])  # 前4个点平均

    # 判断企稳：移动平均线向上或趋平
    ma5_stable = ma5_recent >= ma5_earlier * 0.995  # 允许小幅下跌
    ma10_stable = ma10_recent >= ma10_earlier * 0.995
    ma20_stable = ma20_recent >= ma20_earlier * 0.995

    # 额外判断：当前价格相对于移动平均线的位置
    current_price = prices[-1]  # E点价格

    # 价格在移动平均线之上加分
    price_above_ma5 = current_price >= ma5_recent
    price_above_ma10 = current_price >= ma10_recent
    price_above_ma20 = current_price >= ma20_recent

    # 综合判断企稳
    stability_score = sum([ma5_stable, ma10_stable, ma20_stable, price_above_ma5, price_above_ma10, price_above_ma20])
    overall_stable = stability_score >= 4  # 6项中至少4项满足

    return {
        'ma5_stable': ma5_stable,
        'ma10_stable': ma10_stable,
        'ma20_stable': ma20_stable,
        'price_above_ma5': price_above_ma5,
        'price_above_ma10': price_above_ma10,
        'price_above_ma20': price_above_ma20,
        'stability_score': stability_score,
        'overall_stable': overall_stable,
        'ma5_recent': ma5_recent,
        'ma10_recent': ma10_recent,
        'ma20_recent': ma20_recent,
        'current_price': current_price
    }

def analyze_ma_stability_results(trend_analysis):
    """分析移动平均线企稳结果"""
    total_count = len(trend_analysis)

    ma5_stable_count = sum(1 for t in trend_analysis if t['ma5_stable'])
    ma10_stable_count = sum(1 for t in trend_analysis if t['ma10_stable'])
    ma20_stable_count = sum(1 for t in trend_analysis if t['ma20_stable'])
    overall_stable_count = sum(1 for t in trend_analysis if t['is_stable'])

    print(f"\n📊 移动平均线企稳分析结果:")
    print(f"   分析样本: {total_count} 个成功案例")
    print(f"   5日线企稳: {ma5_stable_count} 个 ({ma5_stable_count/total_count:.1%})")
    print(f"   10日线企稳: {ma10_stable_count} 个 ({ma10_stable_count/total_count:.1%})")
    print(f"   20日线企稳: {ma20_stable_count} 个 ({ma20_stable_count/total_count:.1%})")
    print(f"   综合企稳: {overall_stable_count} 个 ({overall_stable_count/total_count:.1%})")

    print(f"\n🔍 企稳案例详情 (前10个):")
    stable_cases = [t for t in trend_analysis if t['is_stable']]
    for i, case in enumerate(stable_cases[:10]):
        print(f"   {i+1}. {case['stock']} ({case['date']}) - 实际涨幅: {case['actual_gain']:.1%}")
        print(f"      5日线: {'✅' if case['ma5_stable'] else '❌'}, "
              f"10日线: {'✅' if case['ma10_stable'] else '❌'}, "
              f"20日线: {'✅' if case['ma20_stable'] else '❌'}")

    print(f"\n❌ 未企稳案例 (前5个):")
    unstable_cases = [t for t in trend_analysis if not t['is_stable']]
    for i, case in enumerate(unstable_cases[:5]):
        print(f"   {i+1}. {case['stock']} ({case['date']}) - 实际涨幅: {case['actual_gain']:.1%}")
        print(f"      5日线: {'✅' if case['ma5_stable'] else '❌'}, "
              f"10日线: {'✅' if case['ma10_stable'] else '❌'}, "
              f"20日线: {'✅' if case['ma20_stable'] else '❌'}")

def analyze_stability_success_correlation(trend_analysis):
    """分析企稳与成功程度的关系"""
    print(f"\n📈 企稳与涨幅关系分析:")

    stable_cases = [t for t in trend_analysis if t['is_stable']]
    unstable_cases = [t for t in trend_analysis if not t['is_stable']]

    if stable_cases:
        stable_gains = [float(t['actual_gain']) for t in stable_cases if isinstance(t['actual_gain'], (int, float))]
        if stable_gains:
            print(f"   企稳案例平均涨幅: {np.mean(stable_gains):.1%}")
            print(f"   企稳案例中位数涨幅: {np.median(stable_gains):.1%}")
            print(f"   企稳案例最大涨幅: {max(stable_gains):.1%}")

    if unstable_cases:
        unstable_gains = [float(t['actual_gain']) for t in unstable_cases if isinstance(t['actual_gain'], (int, float))]
        if unstable_gains:
            print(f"   未企稳案例平均涨幅: {np.mean(unstable_gains):.1%}")
            print(f"   未企稳案例中位数涨幅: {np.median(unstable_gains):.1%}")
            print(f"   未企稳案例最大涨幅: {max(unstable_gains):.1%}")

    # 分析不同企稳程度的涨幅分布
    print(f"\n📊 按企稳程度分析:")

    # 按5日线、10日线、20日线分别统计
    for ma_type in ['ma5_stable', 'ma10_stable', 'ma20_stable']:
        stable_ma = [t for t in trend_analysis if t[ma_type]]
        unstable_ma = [t for t in trend_analysis if not t[ma_type]]

        if stable_ma and unstable_ma:
            stable_gains = [float(t['actual_gain']) for t in stable_ma if isinstance(t['actual_gain'], (int, float))]
            unstable_gains = [float(t['actual_gain']) for t in unstable_ma if isinstance(t['actual_gain'], (int, float))]

            if stable_gains and unstable_gains:
                ma_name = ma_type.replace('_stable', '').replace('ma', '').upper() + '日线'
                print(f"   {ma_name}企稳 vs 未企稳:")
                print(f"     企稳: {len(stable_gains)}个, 平均涨幅{np.mean(stable_gains):.1%}")
                print(f"     未企稳: {len(unstable_gains)}个, 平均涨幅{np.mean(unstable_gains):.1%}")

def analyze_trend_stability(prices):
    """分析价格序列的企稳程度"""
    if len(prices) < 3:
        return 0
    
    # 计算价格变化率
    changes = []
    for i in range(1, len(prices)):
        if prices[i-1] != 0:
            change = (prices[i] - prices[i-1]) / prices[i-1]
            changes.append(change)
    
    if not changes:
        return 0
    
    # 企稳判断逻辑：
    # 1. 最近的变化是否为正（上涨）
    # 2. 变化是否趋于稳定（波动减小）
    
    recent_change = changes[-1] if changes else 0
    
    # 如果只有一个变化，直接返回
    if len(changes) == 1:
        return recent_change
    
    # 计算变化的趋势
    trend_score = 0
    
    # 最近变化为正加分
    if recent_change > 0:
        trend_score += 1
    
    # 如果变化幅度在减小（趋于稳定）加分
    if len(changes) >= 2:
        recent_volatility = abs(changes[-1])
        earlier_volatility = abs(changes[-2])
        if recent_volatility < earlier_volatility:
            trend_score += 0.5
    
    # 整体趋势向上加分
    if len(changes) >= 2:
        overall_trend = sum(changes) / len(changes)
        if overall_trend > 0:
            trend_score += 0.5
    
    return trend_score

def analyze_price_features(successful_cases):
    """分析价格相关特征"""
    print(f"\n🔍 分析价格相关特征...")
    
    # 查找振幅、涨跌幅等特征
    amplitude_cols = [col for col in successful_cases.columns if '振幅' in col]
    change_cols = [col for col in successful_cases.columns if '涨跌幅' in col or '涨幅' in col]
    
    print(f"   振幅相关列: {amplitude_cols[:5]}")
    print(f"   涨跌幅相关列: {change_cols[:5]}")
    
    # 分析E点相关特征（最接近选股时点）
    e_point_features = [col for col in successful_cases.columns if 'E点' in col]
    print(f"   E点相关特征: {e_point_features}")
    
    if e_point_features:
        print(f"\n📊 E点特征统计:")
        for col in e_point_features[:5]:
            values = pd.to_numeric(successful_cases[col], errors='coerce').dropna()
            if len(values) > 0:
                print(f"   {col}:")
                print(f"     均值: {values.mean():.3f}")
                print(f"     中位数: {values.median():.3f}")
                print(f"     标准差: {values.std():.3f}")

def main():
    """主函数"""
    analyze_ma_stability()

if __name__ == "__main__":
    main()
