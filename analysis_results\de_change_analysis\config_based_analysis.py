#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于配置文件的多条件分析
从JSON配置文件加载预定义的筛选条件进行分析
"""

import json
import pandas as pd
from analyze_de_change_success import MultiConditionAnalyzer, analyze_multi_conditions

class ConfigBasedAnalyzer:
    """基于配置文件的分析器"""
    
    def __init__(self, config_file: str = "custom_conditions.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.analyzer = MultiConditionAnalyzer()
    
    def load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 成功加载配置文件: {self.config_file}")
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件不存在: {self.config_file}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return {}
    
    def list_available_strategies(self):
        """列出可用的策略"""
        if "condition_sets" not in self.config:
            print("❌ 配置文件中没有找到策略定义")
            return
        
        print("📋 可用的筛选策略:")
        print("-" * 80)
        
        for key, strategy in self.config["condition_sets"].items():
            print(f"🎯 {key}: {strategy['name']}")
            print(f"   描述: {strategy['description']}")
            print(f"   条件数量: {len(strategy['conditions'])}")
            print()
    
    def analyze_strategy(self, strategy_key: str, data_file: str):
        """分析指定策略"""
        if "condition_sets" not in self.config:
            print("❌ 配置文件中没有找到策略定义")
            return
        
        if strategy_key not in self.config["condition_sets"]:
            print(f"❌ 策略不存在: {strategy_key}")
            return
        
        strategy = self.config["condition_sets"][strategy_key]
        
        print(f"🎯 分析策略: {strategy['name']}")
        print(f"📝 描述: {strategy['description']}")
        
        analyze_multi_conditions(
            data_file=data_file,
            custom_conditions=strategy["conditions"],
            condition_name=strategy["name"]
        )
    
    def analyze_all_strategies(self, data_file: str):
        """分析所有策略"""
        if "condition_sets" not in self.config:
            print("❌ 配置文件中没有找到策略定义")
            return
        
        print("🔍 批量分析所有策略")
        print("=" * 80)
        
        df = self.analyzer.load_data(data_file)
        
        if '5日成功选股' not in df.columns:
            print("❌ 未找到目标列")
            return
        
        results = []
        
        for key, strategy in self.config["condition_sets"].items():
            print(f"\n🎯 分析策略: {strategy['name']}")
            
            # 应用筛选条件
            mask = self.analyzer.apply_conditions(df, strategy["conditions"])
            filtered_df = df[mask]
            
            if len(filtered_df) == 0:
                print("   ❌ 没有符合条件的数据")
                continue
            
            # 计算成功率
            success_mask = filtered_df['5日成功选股'] == "成功"
            success_count = success_mask.sum()
            total_count = len(filtered_df)
            success_rate = success_count / total_count if total_count > 0 else 0
            
            # 计算平均涨幅
            avg_gain = 0
            if success_count > 0 and '5日最大涨幅' in filtered_df.columns:
                success_cases = filtered_df[success_mask]
                actual_gains = pd.to_numeric(success_cases['5日最大涨幅'], errors='coerce').dropna()
                if len(actual_gains) > 0:
                    avg_gain = actual_gains.mean()
            
            print(f"   📊 结果: {total_count}只股票, {success_count}只成功, 成功率{success_rate:.1%}, 平均涨幅{avg_gain:.1%}")
            
            results.append({
                'strategy_key': key,
                'strategy_name': strategy['name'],
                'total_count': total_count,
                'success_count': success_count,
                'success_rate': success_rate,
                'avg_gain': avg_gain
            })
        
        # 汇总结果
        self.display_strategy_comparison(results)
    
    def display_strategy_comparison(self, results: list):
        """显示策略对比结果"""
        if not results:
            return
        
        print(f"\n📊 策略对比汇总:")
        print("=" * 100)
        print(f"{'策略名称':<20} {'符合数量':<10} {'成功数量':<10} {'成功率':<10} {'平均涨幅':<10} {'综合评分':<10}")
        print("=" * 100)
        
        # 计算综合评分并排序
        for result in results:
            # 综合评分 = 成功率 * 0.6 + 样本量权重 * 0.2 + 平均涨幅权重 * 0.2
            sample_weight = min(result['total_count'] / 50, 1.0)  # 样本量权重，50个样本为满分
            gain_weight = min(result['avg_gain'] / 0.2, 1.0)  # 涨幅权重，20%涨幅为满分
            
            result['composite_score'] = (
                result['success_rate'] * 0.6 + 
                sample_weight * 0.2 + 
                gain_weight * 0.2
            )
        
        # 按综合评分排序
        results.sort(key=lambda x: x['composite_score'], reverse=True)
        
        for result in results:
            print(f"{result['strategy_name']:<20} "
                  f"{result['total_count']:<10} "
                  f"{result['success_count']:<10} "
                  f"{result['success_rate']:<10.1%} "
                  f"{result['avg_gain']:<10.1%} "
                  f"{result['composite_score']:<10.3f}")
        
        # 推荐最佳策略
        if results:
            best_strategy = results[0]
            print(f"\n🏆 推荐策略: {best_strategy['strategy_name']}")
            print(f"   综合评分: {best_strategy['composite_score']:.3f}")
            print(f"   成功率: {best_strategy['success_rate']:.1%}")
            print(f"   样本数量: {best_strategy['total_count']}")
            print(f"   平均涨幅: {best_strategy['avg_gain']:.1%}")
    
    def analyze_quick_filters(self, data_file: str):
        """分析快速筛选条件"""
        if "quick_filters" not in self.config:
            print("❌ 配置文件中没有找到快速筛选定义")
            return
        
        print("⚡ 快速筛选分析")
        print("=" * 60)
        
        df = self.analyzer.load_data(data_file)
        
        if '5日成功选股' not in df.columns:
            print("❌ 未找到目标列")
            return
        
        print(f"{'筛选条件':<15} {'符合数量':<10} {'成功数量':<10} {'成功率':<10}")
        print("-" * 60)
        
        for key, filter_config in self.config["quick_filters"].items():
            mask = self.analyzer.apply_conditions(df, filter_config["conditions"])
            filtered_df = df[mask]
            
            if len(filtered_df) > 0:
                success_mask = filtered_df['5日成功选股'] == "成功"
                success_count = success_mask.sum()
                total_count = len(filtered_df)
                success_rate = success_count / total_count
                
                print(f"{filter_config['name']:<15} {total_count:<10} {success_count:<10} {success_rate:<10.1%}")

def interactive_config_analysis():
    """交互式配置分析"""
    config_analyzer = ConfigBasedAnalyzer()
    
    if not config_analyzer.config:
        print("❌ 无法加载配置文件，退出程序")
        return
    
    data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    while True:
        print("\n🎯 基于配置文件的多条件分析")
        print("=" * 50)
        print("1. 查看可用策略")
        print("2. 分析指定策略")
        print("3. 分析所有策略")
        print("4. 快速筛选分析")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            config_analyzer.list_available_strategies()
        
        elif choice == "2":
            config_analyzer.list_available_strategies()
            strategy_key = input("\n请输入策略键名: ").strip()
            config_analyzer.analyze_strategy(strategy_key, data_file)
        
        elif choice == "3":
            config_analyzer.analyze_all_strategies(data_file)
        
        elif choice == "4":
            config_analyzer.analyze_quick_filters(data_file)
        
        elif choice == "5":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        config_analyzer = ConfigBasedAnalyzer()
        data_file = "../../选股分析结果/2025-05-01-2025-06-01.xlsx"
        
        if mode == "list":
            config_analyzer.list_available_strategies()
        elif mode == "all":
            config_analyzer.analyze_all_strategies(data_file)
        elif mode == "quick":
            config_analyzer.analyze_quick_filters(data_file)
        elif mode.startswith("strategy:"):
            strategy_key = mode.split(":", 1)[1]
            config_analyzer.analyze_strategy(strategy_key, data_file)
        else:
            print("使用方法:")
            print("  python config_based_analysis.py list                    # 列出所有策略")
            print("  python config_based_analysis.py all                     # 分析所有策略")
            print("  python config_based_analysis.py quick                   # 快速筛选分析")
            print("  python config_based_analysis.py strategy:strategy_key   # 分析指定策略")
    else:
        interactive_config_analysis()

if __name__ == "__main__":
    main()
