
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习训练报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .header { text-align: center; color: #333; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metrics { display: flex; justify-content: space-around; }
        .metric { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: center; }
        th { background-color: #4CAF50; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e8f5e8; }
        .best-score { background-color: #d4edda; font-weight: bold; }
        .model-name { font-weight: bold; color: #2c3e50; }
        h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 深度学习训练报告</h1>
        <p>生成时间: 2025-08-03 01:27:55</p>
    </div>
    
    <div class="section">
        <h2>📊 模型性能对比</h2>
        <table>
            <tr>
                <th>模型</th>
                <th>准确率</th>
                <th>精确率</th>
                <th>召回率</th>
                <th>F1分数</th>
                <th>AUC</th>
            </tr>

            <tr>
                <td class="model-name">PrecisionMLP</td>
                <td>0.089</td>
                <td>0.089</td>
                <td class="best-score">1.000</td>
                <td>0.163</td>
                <td class="best-score">0.695</td>
            </tr>

            <tr>
                <td class="model-name">ResidualNet</td>
                <td class="best-score">0.726</td>
                <td class="best-score">0.176</td>
                <td>0.571</td>
                <td class="best-score">0.270</td>
                <td>0.631</td>
            </tr>

            <tr>
                <td class="model-name">AttentionNet</td>
                <td>0.641</td>
                <td>0.152</td>
                <td>0.667</td>
                <td>0.248</td>
                <td>0.662</td>
            </tr>

        </table>
    </div>

    <div class="section">
        <h2>🏆 最佳模型推荐</h2>
        <div class="metrics">

            <div class="metric">
                <h3>🎯 最佳AUC模型</h3>
                <p><strong>PrecisionMLP</strong></p>
                <p>AUC: 0.695</p>
            </div>
            <div class="metric">
                <h3>⚖️ 最佳F1模型</h3>
                <p><strong>ResidualNet</strong></p>
                <p>F1: 0.270</p>
            </div>

        </div>
    </div>

    <div class="section">
        <h2>📈 训练信息</h2>
        <p><strong>数据集信息:</strong></p>
        <ul>

            <li>训练样本: 945</li>
            <li>测试样本: 237</li>
            <li>特征数量: 31</li>
            <li>正样本率: 0.0%</li>
            <li>数据平衡性: 不平衡</li>

        </ul>

        <p><strong>训练配置:</strong></p>
        <ul>

            <li>学习率: 0.0005</li>
            <li>批次大小: 64</li>
            <li>最大轮数: 150</li>
            <li>早停耐心: 15</li>
            <li>优化器: ADAM</li>

        </ul>
    </div>
</body>
</html>
