#!/usr/bin/env python3
"""
深度学习数据预处理模块
处理股票数据，为深度学习模型准备训练数据
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import joblib
import os
import json
from datetime import datetime

class StockDataPreprocessor:
    """股票数据预处理器"""
    
    def __init__(self, config_path="deeplearning/config.json"):
        """初始化预处理器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.scaler = None
        if self.config:
            self.feature_columns = self.config['data']['feature_columns']
            self.target_column = self.config['data']['target_column']
            self.target_threshold = self.config['data']['target_threshold']
        else:
            self.feature_columns = []
            self.target_column = "5日最大涨幅"
            self.target_threshold = 0.10
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def load_data(self, data_path):
        """加载数据"""
        print(f"📊 加载数据: {data_path}")
        try:
            df = pd.read_excel(data_path)
            print(f"   原始数据: {len(df)} 行, {len(df.columns)} 列")
            return df
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
    
    def clean_data(self, df):
        """数据清洗"""
        print("🔧 数据清洗...")
        
        # 检查必要列是否存在
        missing_features = [col for col in self.feature_columns if col not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features}")
            return None
        
        if self.target_column not in df.columns:
            print(f"❌ 缺失目标列: {self.target_column}")
            return None
        
        # 选择需要的列
        selected_columns = self.feature_columns + [self.target_column]
        df_clean = df[selected_columns].copy()
        
        # 处理缺失值
        print(f"   处理前缺失值: {df_clean.isnull().sum().sum()}")

        # 处理百分比格式的数据
        for col in self.feature_columns:
            if col in df_clean.columns:
                # 检查是否有百分比格式的字符串
                if df_clean[col].dtype == 'object':
                    # 尝试转换百分比格式
                    try:
                        df_clean[col] = df_clean[col].astype(str).str.rstrip('%').astype(float)
                        print(f"   转换百分比格式: {col}")
                    except:
                        # 如果转换失败，尝试直接转换为数值
                        try:
                            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
                            print(f"   转换为数值: {col}")
                        except:
                            print(f"   ⚠️ 无法转换列: {col}")

                # 对数值列填充中位数
                if df_clean[col].dtype in ['float64', 'int64']:
                    median_val = df_clean[col].median()
                    df_clean[col] = df_clean[col].fillna(median_val)

        # 处理目标列 - 根据列名类型决定处理方式
        if self.target_column == "5日成功选股":
            # 对于"5日成功选股"列，保持文本格式，不需要转换
            print(f"   目标列为文本类型: {self.target_column}")
            # 删除目标列为空的行
            df_clean = df_clean.dropna(subset=[self.target_column])
        else:
            # 对于数值目标列，处理百分比格式
            if df_clean[self.target_column].dtype == 'object':
                try:
                    df_clean[self.target_column] = df_clean[self.target_column].astype(str).str.rstrip('%').astype(float) / 100
                    print(f"   转换目标列百分比格式: {self.target_column}")
                except:
                    try:
                        df_clean[self.target_column] = pd.to_numeric(df_clean[self.target_column], errors='coerce')
                    except:
                        print(f"   ⚠️ 无法转换目标列: {self.target_column}")

            # 删除目标列为空的行
            df_clean = df_clean.dropna(subset=[self.target_column])

        print(f"   处理后缺失值: {df_clean.isnull().sum().sum()}")
        print(f"   清洗后数据: {len(df_clean)} 行")
        
        return df_clean
    
    def create_target(self, df):
        """创建二分类目标变量"""
        print(f"🎯 创建目标变量: {self.target_column}")

        if self.target_column == "5日成功选股":
            # 对于"5日成功选股"列，直接转换为二分类
            target = (df[self.target_column] == "成功").astype(int)
        else:
            # 对于数值列，使用阈值判断
            print(f"   阈值: {self.target_threshold:.1%}")
            target = (df[self.target_column] >= self.target_threshold).astype(int)

        positive_count = target.sum()
        negative_count = len(target) - positive_count
        positive_rate = positive_count / len(target)

        print(f"   正样本 (成功): {positive_count} ({positive_rate:.1%})")
        print(f"   负样本 (失败): {negative_count} ({1-positive_rate:.1%})")

        return target
    
    def normalize_features(self, X_train, X_test=None, fit_scaler=True):
        """特征标准化"""
        print("📏 特征标准化...")
        
        normalization_method = self.config['data']['normalization']['method']
        
        if normalization_method == 'standard_scaler':
            if fit_scaler:
                self.scaler = StandardScaler()
                X_train_scaled = self.scaler.fit_transform(X_train)
            else:
                X_train_scaled = self.scaler.transform(X_train)
        elif normalization_method == 'min_max_scaler':
            feature_range = tuple(self.config['data']['normalization']['feature_range'])
            if fit_scaler:
                self.scaler = MinMaxScaler(feature_range=feature_range)
                X_train_scaled = self.scaler.fit_transform(X_train)
            else:
                X_train_scaled = self.scaler.transform(X_train)
        else:
            print(f"⚠️ 未知的标准化方法: {normalization_method}")
            return X_train, X_test
        
        X_test_scaled = None
        if X_test is not None:
            X_test_scaled = self.scaler.transform(X_test)
        
        print(f"   标准化方法: {normalization_method}")
        print(f"   训练集形状: {X_train_scaled.shape}")
        if X_test_scaled is not None:
            print(f"   测试集形状: {X_test_scaled.shape}")
        
        return X_train_scaled, X_test_scaled
    
    def split_data(self, df):
        """数据分割 - 按股票代码分组"""
        print("✂️ 数据分割...")

        # 准备特征和目标
        X = df[self.feature_columns].values
        y = self.create_target(df)

        # 分割训练集和测试集
        test_size = 1 - self.config['data']['train_test_split']
        random_state = self.config['data']['random_state']

        # 获取股票代码列
        stock_col = None
        for col in ['股票', '股票代码', '股票名称']:
            if col in df.columns:
                stock_col = col
                break

        if stock_col is not None:
            # 按股票代码分组分割
            print(f"   按{stock_col}分组分割数据...")
            unique_stocks = df[stock_col].unique()
            np.random.seed(random_state)
            np.random.shuffle(unique_stocks)

            train_size = int(len(unique_stocks) * (1 - test_size))
            train_stocks = unique_stocks[:train_size]
            test_stocks = unique_stocks[train_size:]

            train_mask = df[stock_col].isin(train_stocks)
            test_mask = df[stock_col].isin(test_stocks)

            X_train = X[train_mask]
            X_test = X[test_mask]
            y_train = y[train_mask]
            y_test = y[test_mask]

            print(f"   训练股票数: {len(train_stocks)}")
            print(f"   测试股票数: {len(test_stocks)}")
        else:
            # 如果没有股票代码列，使用传统分割方法
            print(f"   未找到股票代码列，使用随机分割...")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state, stratify=y
            )
        
        print(f"   训练集: {len(X_train)} 样本")
        print(f"   测试集: {len(X_test)} 样本")
        print(f"   训练集正样本率: {y_train.mean():.1%}")
        print(f"   测试集正样本率: {y_test.mean():.1%}")
        
        return X_train, X_test, y_train, y_test
    
    def save_scaler(self, save_path="deeplearning/models/scaler.pkl"):
        """保存标准化器"""
        if self.scaler is not None:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            joblib.dump(self.scaler, save_path)
            print(f"💾 标准化器已保存: {save_path}")
    
    def load_scaler(self, load_path="deeplearning/models/scaler.pkl"):
        """加载标准化器"""
        try:
            self.scaler = joblib.load(load_path)
            print(f"✅ 标准化器已加载: {load_path}")
            return True
        except Exception as e:
            print(f"❌ 标准化器加载失败: {e}")
            return False
    
    def preprocess_training_data(self, data_path):
        """预处理训练数据的完整流程"""
        print("🔄 开始训练数据预处理...")
        print("=" * 60)
        
        # 加载数据
        df = self.load_data(data_path)
        if df is None:
            return None
        
        # 清洗数据
        df_clean = self.clean_data(df)
        if df_clean is None:
            return None
        
        # 分割数据
        X_train, X_test, y_train, y_test = self.split_data(df_clean)
        
        # 标准化特征
        X_train_scaled, X_test_scaled = self.normalize_features(X_train, X_test, fit_scaler=True)
        
        # 保存标准化器
        self.save_scaler()
        
        print("✅ 训练数据预处理完成")
        
        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'feature_names': self.feature_columns
        }
    
    def preprocess_prediction_data(self, df):
        """预处理预测数据"""
        print("🔄 预处理预测数据...")
        
        # 检查特征列
        missing_features = [col for col in self.feature_columns if col not in df.columns]
        if missing_features:
            print(f"❌ 缺失特征列: {missing_features}")
            return None
        
        # 选择特征列
        X = df[self.feature_columns].copy()

        # 处理百分比格式的数据
        for col in self.feature_columns:
            if col in X.columns:
                # 检查是否有百分比格式的字符串
                if X[col].dtype == 'object':
                    # 尝试转换百分比格式
                    try:
                        X[col] = X[col].astype(str).str.rstrip('%').astype(float)
                        print(f"   转换百分比格式: {col}")
                    except:
                        # 如果转换失败，尝试直接转换为数值
                        try:
                            X[col] = pd.to_numeric(X[col], errors='coerce')
                            print(f"   转换为数值: {col}")
                        except:
                            print(f"   ⚠️ 无法转换列: {col}")

        # 处理缺失值
        for col in self.feature_columns:
            if X[col].dtype in ['float64', 'int64']:
                median_val = X[col].median()
                X[col] = X[col].fillna(median_val)
        
        # 标准化
        if self.scaler is None:
            print("❌ 标准化器未加载，请先加载或训练模型")
            return None
        
        X_scaled = self.scaler.transform(X.values)
        
        print(f"   预测数据形状: {X_scaled.shape}")
        
        return X_scaled

    def preprocess_separate_datasets(self, train_data_path, test_data_path):
        """预处理分离的训练和测试数据集"""
        print("🔄 开始分离数据集预处理...")
        print("=" * 60)

        # 加载训练数据
        print("📊 加载训练数据...")
        train_df = self.load_data(train_data_path)
        if train_df is None:
            return None

        # 加载测试数据
        print("📊 加载测试数据...")
        test_df = self.load_data(test_data_path)
        if test_df is None:
            return None

        # 清洗训练数据
        print("🔧 清洗训练数据...")
        train_df_clean = self.clean_data(train_df)
        if train_df_clean is None:
            return None

        # 清洗测试数据
        print("🔧 清洗测试数据...")
        test_df_clean = self.clean_data(test_df)
        if test_df_clean is None:
            return None

        # 准备训练集特征和目标
        print("🎯 准备训练集...")
        X_train = train_df_clean[self.feature_columns].values
        y_train = self.create_target(train_df_clean)

        # 准备测试集特征和目标
        print("🎯 准备测试集...")
        X_test = test_df_clean[self.feature_columns].values
        y_test = self.create_target(test_df_clean)

        # 标准化特征（在训练集上拟合，应用到测试集）
        print("📏 标准化特征...")
        X_train_scaled, X_test_scaled = self.normalize_features(X_train, X_test, fit_scaler=True)

        # 保存标准化器
        self.save_scaler()

        print("✅ 分离数据集预处理完成")
        print(f"   训练集: {X_train_scaled.shape[0]} 样本")
        print(f"   测试集: {X_test_scaled.shape[0]} 样本")

        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'feature_names': self.feature_columns
        }

if __name__ == "__main__":
    # 测试数据预处理器
    preprocessor = StockDataPreprocessor()
    
    # 预处理训练数据
    data_path = "选股分析结果/选股分析结果_20250730_225530.xlsx"
    processed_data = preprocessor.preprocess_training_data(data_path)
    
    if processed_data:
        print(f"\n📊 预处理结果:")
        print(f"   训练集形状: {processed_data['X_train'].shape}")
        print(f"   测试集形状: {processed_data['X_test'].shape}")
        print(f"   特征数量: {len(processed_data['feature_names'])}")
