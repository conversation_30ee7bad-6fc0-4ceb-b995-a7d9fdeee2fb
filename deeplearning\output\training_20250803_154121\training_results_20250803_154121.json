{"timestamp": "20250803_154121", "config": {"version": "1.0.0", "description": "深度学习股票预测配置", "models": {"PrecisionMLP": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.3, "activation": "relu"}, "ResidualNet": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.2, "activation": "relu"}, "AttentionNet": {"hidden_dims": [128, 64, 32, 16], "dropout_rate": 0.2, "activation": "relu", "num_heads": 4}}, "training": {"optimizer": "adam", "learning_rate": 0.0005, "batch_size": 64, "epochs": 300, "early_stopping_patience": 30, "validation_split": 0.2, "weight_decay": 0.01, "loss_type": "focal", "class_weight": 8.0, "focal_alpha": 0.25, "focal_gamma": 2.0, "precision_threshold": 0.8, "lr_scheduler": {"type": "reduce_on_plateau", "factor": 0.5, "patience": 15, "min_lr": 1e-07}, "device": "auto"}, "data": {"feature_columns": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅", "D点成交量", "D点实体涨跌幅", "D点价格振幅", "E点成交量", "E点实体涨跌幅", "E点价格振幅", "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数", "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量", "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"], "target_column": "5日成功选股", "target_threshold": 0.5, "normalization": {"method": "standard_scaler", "feature_range": [-1, 1]}, "train_test_split": 0.8, "random_state": 42, "train_data_path": "选股分析结果/2025-01-01-2025-04-15.xlsx", "test_data_path": "选股分析结果/2025-05-01-2025-06-01.xlsx", "data_path": "选股分析结果/2025-01-01-2025-04-15.xlsx", "save_model": true, "model_path": "deeplearning/models/", "logs_path": "deeplearning/logs/", "checkpoints": "deeplearning/checkpoints/"}, "prediction": {"confidence_threshold": 0.9, "precision_threshold": 0.8, "output_probabilities": true, "batch_prediction": true, "optimize_for_precision": true}, "output": {"results_path": "deeplearning/output/", "model_summary": true, "feature_importance": true, "confusion_matrix": true, "classification_report": true}}, "data_info": {"train_samples": 5126, "test_samples": 832, "feature_count": 31, "positive_rate_train": 0.12153726102223956, "positive_rate_test": 0.1201923076923077}, "evaluation_metrics": {"PrecisionMLP": {"test_accuracy": 0.6274038461538461, "test_precision": 0.125, "test_recall": 0.35, "f1_score": 0.18421052631578946, "auc_score": 0.5017622950819672, "confusion_matrix": [[487, 245], [65, 35]], "optimal_threshold": 0.5199999999999998, "predicted_positive": 280, "total_positive": 100, "precision_target_met": false}, "ResidualNet": {"test_accuracy": 0.8641826923076923, "test_precision": 0.15789473684210525, "test_recall": 0.03, "f1_score": 0.05042016806722689, "auc_score": 0.5184972677595628, "confusion_matrix": [[716, 16], [97, 3]], "optimal_threshold": 0.8599999999999995, "predicted_positive": 19, "total_positive": 100, "precision_target_met": false}, "AttentionNet": {"test_accuracy": 0.8798076923076923, "test_precision": 0.5, "test_recall": 0.01, "f1_score": 0.0196078431372549, "auc_score": 0.4721994535519126, "confusion_matrix": [[731, 1], [99, 1]], "optimal_threshold": 0.9699999999999995, "predicted_positive": 2, "total_positive": 100, "precision_target_met": false}}, "feature_importance": {"PrecisionMLP": [{"feature": "A点实体涨跌幅", "importance": 0.0}, {"feature": "A点价格振幅", "importance": 0.0}, {"feature": "B点成交量", "importance": 0.0}, {"feature": "B点实体涨跌幅", "importance": 0.0}, {"feature": "B点价格振幅", "importance": 0.0}, {"feature": "C点最低", "importance": 0.0}, {"feature": "C点成交量", "importance": 0.0}, {"feature": "C点实体涨跌幅", "importance": 0.0}, {"feature": "C点价格振幅", "importance": 0.0}, {"feature": "D点成交量", "importance": 0.0}, {"feature": "D点实体涨跌幅", "importance": 0.0}, {"feature": "D点价格振幅", "importance": 0.0}, {"feature": "E点成交量", "importance": 0.0}, {"feature": "E点实体涨跌幅", "importance": 0.0}, {"feature": "E点价格振幅", "importance": 0.0}, {"feature": "A-B涨幅", "importance": 0.0}, {"feature": "A-B天数", "importance": 0.0}, {"feature": "B-C跌幅", "importance": 0.0}, {"feature": "B-C天数", "importance": 0.0}, {"feature": "C-D涨幅", "importance": 0.0}, {"feature": "C-D天数", "importance": 0.0}, {"feature": "D-E涨幅", "importance": 0.0}, {"feature": "D-E天数", "importance": 0.0}, {"feature": "D点成交量/C-D均量", "importance": 0.0}, {"feature": "D点上影线涨幅", "importance": 0.0}, {"feature": "D点上影线/实体", "importance": 0.0}, {"feature": "E点成交量/C-D均量", "importance": 0.0}, {"feature": "E点成交量/D点成交量", "importance": 0.0}, {"feature": "E点J值", "importance": 0.0}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.0}, {"feature": "E点相对D点收盘价涨幅", "importance": 0.0}], "ResidualNet": [{"feature": "C点实体涨跌幅", "importance": 0.0336538461538462}, {"feature": "D点上影线/实体", "importance": 0.028846153846153855}, {"feature": "E点J值", "importance": 0.02644230769230771}, {"feature": "A点价格振幅", "importance": 0.02283653846153849}, {"feature": "B-C跌幅", "importance": 0.015625}, {"feature": "C-D涨幅", "importance": 0.014423076923076927}, {"feature": "E点成交量/C-D均量", "importance": 0.014423076923076927}, {"feature": "D点成交量/C-D均量", "importance": 0.013221153846153855}, {"feature": "B点成交量", "importance": 0.013221153846153855}, {"feature": "C-D天数", "importance": 0.012019230769230782}, {"feature": "B-C天数", "importance": 0.01081730769230771}, {"feature": "E点成交量", "importance": 0.007211538461538491}, {"feature": "B点价格振幅", "importance": 0.007211538461538491}, {"feature": "A-B天数", "importance": 0.007211538461538491}, {"feature": "D点上影线涨幅", "importance": 0.0036057692307692735}, {"feature": "E点成交量/D点成交量", "importance": 0.0036057692307692735}, {"feature": "B点实体涨跌幅", "importance": 0.002403846153846201}, {"feature": "E点实体涨跌幅", "importance": 0.002403846153846201}, {"feature": "D-E涨幅", "importance": 0.0012019230769230727}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.0012019230769230727}, {"feature": "C点最低", "importance": -0.0012019230769230727}, {"feature": "D点实体涨跌幅", "importance": -0.0024038461538461453}, {"feature": "A-B涨幅", "importance": -0.0024038461538461453}, {"feature": "E点相对D点收盘价涨幅", "importance": -0.004807692307692291}, {"feature": "C点成交量", "importance": -0.004807692307692291}, {"feature": "A点实体涨跌幅", "importance": -0.006009615384615363}, {"feature": "D-E天数", "importance": -0.006009615384615363}, {"feature": "E点价格振幅", "importance": -0.007211538461538436}, {"feature": "C点价格振幅", "importance": -0.008413461538461509}, {"feature": "D点成交量", "importance": -0.008413461538461509}, {"feature": "D点价格振幅", "importance": -0.027644230769230727}], "AttentionNet": [{"feature": "D点上影线涨幅", "importance": 0.0180288461538462}, {"feature": "D点上影线/实体", "importance": 0.01081730769230771}, {"feature": "E点成交量/D点成交量", "importance": 0.009615384615384637}, {"feature": "E点J值", "importance": 0.008413461538461564}, {"feature": "B点成交量", "importance": 0.008413461538461564}, {"feature": "A点价格振幅", "importance": 0.007211538461538491}, {"feature": "E点成交量", "importance": 0.007211538461538491}, {"feature": "D点成交量/C-D均量", "importance": 0.004807692307692346}, {"feature": "E点价格振幅", "importance": 0.0036057692307692735}, {"feature": "E点成交量/C-D均量", "importance": 0.0036057692307692735}, {"feature": "A-B涨幅", "importance": 0.002403846153846201}, {"feature": "E点J值相对D点J值涨幅", "importance": 0.002403846153846201}, {"feature": "D点成交量", "importance": 0.002403846153846201}, {"feature": "C点最低", "importance": 0.0}, {"feature": "A点实体涨跌幅", "importance": 0.0}, {"feature": "C点实体涨跌幅", "importance": 0.0}, {"feature": "A-B天数", "importance": -0.0012019230769230727}, {"feature": "B点实体涨跌幅", "importance": -0.0012019230769230727}, {"feature": "C点成交量", "importance": -0.0024038461538461453}, {"feature": "B-C跌幅", "importance": -0.003605769230769218}, {"feature": "B点价格振幅", "importance": -0.006009615384615363}, {"feature": "B-C天数", "importance": -0.006009615384615363}, {"feature": "D-E涨幅", "importance": -0.007211538461538436}, {"feature": "D-E天数", "importance": -0.008413461538461509}, {"feature": "E点相对D点收盘价涨幅", "importance": -0.008413461538461509}, {"feature": "E点实体涨跌幅", "importance": -0.009615384615384581}, {"feature": "D点价格振幅", "importance": -0.009615384615384581}, {"feature": "C-D天数", "importance": -0.010817307692307654}, {"feature": "D点实体涨跌幅", "importance": -0.0132211538461538}, {"feature": "C-D涨幅", "importance": -0.0132211538461538}, {"feature": "C点价格振幅", "importance": -0.0444711538461538}]}}