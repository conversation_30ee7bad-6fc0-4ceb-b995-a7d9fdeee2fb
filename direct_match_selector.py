#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接匹配选股系统
找到历史上表现最好的股票，看看它们在测试期间是否还有交易机会
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DirectMatchSelector:
    """直接匹配选股器"""
    
    def __init__(self):
        pass
    
    def analyze_top_performers(self, train_file, test_file):
        """分析历史最佳表现者并在测试数据中寻找"""
        print("🎯 直接匹配选股系统")
        print("=" * 80)
        
        # 加载历史数据
        print("📊 加载历史数据...")
        train_df = pd.read_excel(train_file)
        print(f"   历史数据: {len(train_df)} 条记录")
        
        # 加载测试数据
        print("📊 加载测试数据...")
        test_df = pd.read_excel(test_file)
        print(f"   测试数据: {len(test_df)} 条记录")
        
        # 分析历史成功案例
        target_col = "5日成功选股"
        if target_col not in train_df.columns:
            print(f"❌ 未找到目标列: {target_col}")
            return None
            
        successful_mask = train_df[target_col] == "成功"
        successful_cases = train_df[successful_mask].copy()
        
        print(f"✅ 历史成功案例: {len(successful_cases)} 个")
        
        if len(successful_cases) == 0:
            print("❌ 没有找到成功案例")
            return None
        
        # 按涨幅排序，找到最佳表现者
        gain_col = "5日最大涨幅"
        if gain_col in successful_cases.columns:
            gains = pd.to_numeric(successful_cases[gain_col], errors='coerce')
            successful_cases = successful_cases.copy()
            successful_cases['gain_numeric'] = gains
            successful_cases = successful_cases.dropna(subset=['gain_numeric'])
            successful_cases = successful_cases.sort_values('gain_numeric', ascending=False)
            
            print(f"\n🏆 历史最佳表现者 (前10名):")
            for i, (idx, row) in enumerate(successful_cases.head(10).iterrows()):
                stock = row.get('股票', 'N/A')
                date = row.get('买入日期', 'N/A')
                gain = row['gain_numeric']
                print(f"   {i+1}. {stock} ({date}) - 涨幅: {gain:.1%}")
        
        # 方法1: 寻找相同股票代码
        print(f"\n🔍 方法1: 寻找相同股票代码...")
        same_stock_matches = self._find_same_stock_matches(successful_cases, test_df)
        
        # 方法2: 寻找相似特征的股票
        print(f"\n🔍 方法2: 寻找特征最相似的股票...")
        feature_matches = self._find_feature_matches(successful_cases, test_df)
        
        # 方法3: 极端保守策略 - 只选择历史上多次成功的股票
        print(f"\n🔍 方法3: 寻找历史多次成功的股票...")
        repeat_winners = self._find_repeat_winners(successful_cases, test_df)
        
        # 综合所有方法的结果
        all_candidates = []
        
        if same_stock_matches:
            all_candidates.extend(same_stock_matches)
        if feature_matches:
            all_candidates.extend(feature_matches)
        if repeat_winners:
            all_candidates.extend(repeat_winners)
        
        # 去重并按优先级排序
        final_candidates = self._rank_candidates(all_candidates)
        
        return final_candidates
    
    def _find_same_stock_matches(self, successful_cases, test_df):
        """寻找相同股票代码的匹配"""
        matches = []
        
        # 获取历史成功股票的代码
        successful_stocks = successful_cases['股票'].value_counts()
        print(f"   历史成功股票种类: {len(successful_stocks)}")
        
        # 在测试数据中寻找相同股票
        for stock_code, count in successful_stocks.head(20).items():  # 只看前20个最成功的股票
            test_matches = test_df[test_df['股票'] == stock_code]
            
            if len(test_matches) > 0:
                avg_historical_gain = successful_cases[successful_cases['股票'] == stock_code]['gain_numeric'].mean()
                
                for idx, row in test_matches.iterrows():
                    match = {
                        'method': '相同股票',
                        'stock': stock_code,
                        'buy_date': row.get('买入日期', 'N/A'),
                        'actual_gain': row.get('5日最大涨幅', 'N/A'),
                        'historical_success_count': count,
                        'historical_avg_gain': avg_historical_gain,
                        'priority': count * avg_historical_gain,  # 成功次数 × 平均涨幅
                        'test_index': idx
                    }
                    matches.append(match)
                
                print(f"   ✅ {stock_code}: 历史成功{count}次, 测试期出现{len(test_matches)}次")
        
        return matches
    
    def _find_feature_matches(self, successful_cases, test_df):
        """寻找特征最相似的股票"""
        matches = []
        
        # 选择历史上涨幅最高的前5个案例作为模板
        top_cases = successful_cases.head(5)
        
        feature_columns = [
            "A点实体涨跌幅", "A点价格振幅",
            "B点成交量", "B点实体涨跌幅", "B点价格振幅",
            "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量",
            "E点J值", "E点J值相对D点J值涨幅", "E点相对D点收盘价涨幅"
        ]
        
        # 检查特征列是否存在
        available_features = [col for col in feature_columns if col in top_cases.columns and col in test_df.columns]
        
        if len(available_features) < 10:
            print(f"   ⚠️ 可用特征太少: {len(available_features)}")
            return matches
        
        print(f"   使用 {len(available_features)} 个特征进行匹配")
        
        # 简单的特征匹配：找到关键特征值最接近的股票
        for idx, template in top_cases.iterrows():
            template_gain = template['gain_numeric']
            
            # 选择几个关键特征进行匹配
            key_features = ["E点实体涨跌幅", "D点实体涨跌幅", "E点J值", "D-E涨幅"]
            key_features = [f for f in key_features if f in available_features]
            
            if len(key_features) == 0:
                continue
            
            # 计算测试数据中每个样本与模板的距离
            best_match_idx = None
            best_distance = float('inf')
            
            for test_idx, test_row in test_df.iterrows():
                distance = 0
                valid_features = 0
                
                for feature in key_features:
                    try:
                        template_val = pd.to_numeric(str(template[feature]).replace('%', ''), errors='coerce')
                        test_val = pd.to_numeric(str(test_row[feature]).replace('%', ''), errors='coerce')
                        
                        if pd.notna(template_val) and pd.notna(test_val):
                            distance += abs(template_val - test_val)
                            valid_features += 1
                    except:
                        continue
                
                if valid_features > 0:
                    avg_distance = distance / valid_features
                    if avg_distance < best_distance:
                        best_distance = avg_distance
                        best_match_idx = test_idx
            
            if best_match_idx is not None and best_distance < 0.1:  # 距离阈值
                test_row = test_df.iloc[best_match_idx]
                match = {
                    'method': '特征匹配',
                    'stock': test_row.get('股票', 'N/A'),
                    'buy_date': test_row.get('买入日期', 'N/A'),
                    'actual_gain': test_row.get('5日最大涨幅', 'N/A'),
                    'template_gain': template_gain,
                    'feature_distance': best_distance,
                    'priority': template_gain * (1 - best_distance),  # 模板涨幅 × (1-距离)
                    'test_index': best_match_idx
                }
                matches.append(match)
        
        print(f"   找到 {len(matches)} 个特征匹配")
        return matches
    
    def _find_repeat_winners(self, successful_cases, test_df):
        """寻找历史上多次成功的股票"""
        matches = []
        
        # 找到历史上成功次数≥3的股票
        stock_success_counts = successful_cases['股票'].value_counts()
        repeat_winners = stock_success_counts[stock_success_counts >= 3]
        
        print(f"   历史多次成功股票: {len(repeat_winners)} 个")
        
        for stock_code, success_count in repeat_winners.items():
            # 计算该股票的历史平均涨幅
            stock_gains = successful_cases[successful_cases['股票'] == stock_code]['gain_numeric']
            avg_gain = stock_gains.mean()
            min_gain = stock_gains.min()
            
            # 在测试数据中寻找该股票
            test_matches = test_df[test_df['股票'] == stock_code]
            
            for idx, row in test_matches.iterrows():
                match = {
                    'method': '多次成功',
                    'stock': stock_code,
                    'buy_date': row.get('买入日期', 'N/A'),
                    'actual_gain': row.get('5日最大涨幅', 'N/A'),
                    'success_count': success_count,
                    'historical_avg_gain': avg_gain,
                    'historical_min_gain': min_gain,
                    'priority': success_count * avg_gain * 2,  # 给多次成功更高权重
                    'test_index': idx
                }
                matches.append(match)
        
        return matches
    
    def _rank_candidates(self, all_candidates):
        """对所有候选进行排序和去重"""
        if not all_candidates:
            return []
        
        # 按test_index去重，保留优先级最高的
        unique_candidates = {}
        for candidate in all_candidates:
            test_idx = candidate['test_index']
            if test_idx not in unique_candidates or candidate['priority'] > unique_candidates[test_idx]['priority']:
                unique_candidates[test_idx] = candidate
        
        # 按优先级排序
        final_candidates = list(unique_candidates.values())
        final_candidates.sort(key=lambda x: x['priority'], reverse=True)
        
        return final_candidates

def main():
    """主函数"""
    # 数据文件路径
    train_file = "选股分析结果/2025-01-01-2025-04-15.xlsx"
    test_file = "选股分析结果/2025-05-01-2025-06-01.xlsx"
    
    # 创建选股器
    selector = DirectMatchSelector()
    
    # 分析并寻找匹配
    candidates = selector.analyze_top_performers(train_file, test_file)
    
    if candidates:
        print(f"\n🎯 最终推荐 (前3名):")
        print("=" * 80)
        
        top_3 = candidates[:3]
        successful_predictions = 0
        
        for i, candidate in enumerate(top_3):
            print(f"{i+1}. 股票: {candidate['stock']}")
            print(f"   方法: {candidate['method']}")
            print(f"   买入日期: {candidate['buy_date']}")
            print(f"   实际涨幅: {candidate['actual_gain']}")
            print(f"   优先级得分: {candidate['priority']:.3f}")
            
            # 计算是否成功
            try:
                gain = float(str(candidate['actual_gain']).replace('%', ''))
                if gain >= 10:
                    successful_predictions += 1
                    print(f"   ✅ 成功 (涨幅≥10%)")
                else:
                    print(f"   ❌ 失败 (涨幅<10%)")
            except:
                print(f"   ❓ 无法判断")
            print()
        
        precision = successful_predictions / len(top_3) if top_3 else 0
        print(f"📊 直接匹配选股精确率: {precision:.1%} ({successful_predictions}/{len(top_3)})")
        
        if precision > 0:
            print("🎉 找到了有效的选股策略！")
        else:
            print("⚠️ 当前策略效果不佳，可能需要调整")
    else:
        print("\n⚠️ 没有找到合适的候选股票")

if __name__ == "__main__":
    main()
