{"data_config": {"train_file": "选股分析结果/选股分析结果_20250730_225530.xlsx", "validation_file": "选股分析结果/选股分析结果_20250730_225041.xlsx", "target_column": "5日成功选股", "success_criteria": {"description": "3日最大涨幅≥5%且最大跌幅>-3%", "min_rise": 5.0, "max_fall": -3.0}}, "feature_config": {"input_features": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅"], "feature_selection": {"method": "mutual_info_classif", "k_best": 35}}, "model_config": {"models": {"XGBoost": {"n_estimators": 1000, "max_depth": 4, "learning_rate": 0.02, "subsample": 0.8, "colsample_bytree": 0.8, "scale_pos_weight": 2, "min_child_weight": 5, "gamma": 0.2, "reg_alpha": 0.5, "reg_lambda": 2, "random_state": 42, "n_jobs": -1, "eval_metric": "logloss"}, "LightGBM": {"n_estimators": 1000, "max_depth": 4, "learning_rate": 0.02, "subsample": 0.8, "colsample_bytree": 0.8, "scale_pos_weight": 3, "min_child_weight": 10, "reg_alpha": 0.3, "reg_lambda": 1.5, "random_state": 42, "n_jobs": -1, "verbose": -1, "objective": "binary", "metric": "binary_logloss"}}, "cross_validation": {"cv_folds": 5, "shuffle": true, "random_state": 42}}, "output_config": {"models_dir": "models", "results_dir": "results", "visualizations_dir": "visualizations", "reports_dir": "reports"}, "visualization_config": {"figure_size": [18, 12], "dpi": 300, "font_family": ["SimHei", "Microsoft YaHei"], "colors": {"primary": ["blue", "red", "green", "orange"], "success": "#28a745", "failure": "#dc3545", "warning": "#ffc107", "info": "#17a2b8"}}, "training_info": {"timestamp": "20250730_233637", "train_file": "选股分析结果/选股分析结果_20250730_225530.xlsx", "validation_file": "选股分析结果/选股分析结果_20250730_225041.xlsx", "selected_features": ["A点实体涨跌幅", "A点价格振幅", "B点成交量", "B点实体涨跌幅", "B点价格振幅", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅"], "feature_count": 9}}