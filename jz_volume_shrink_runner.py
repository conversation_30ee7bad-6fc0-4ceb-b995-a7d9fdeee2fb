#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JZVolumeShrinkSelector 极致缩量算法运行器
使用真实的股票数据和算法进行选股分析
"""

import json
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List
import logging
from logging.handlers import RotatingFileHandler
from tqdm import tqdm
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import signal
import copy


class ConfigurableLogger:
    """可配置的日志类，根据配置决定是否输出日志"""

    def __init__(self, config=None):
        self.config = config or {}
        self.enabled = self._should_enable_logging()
        self.logger = logging.getLogger(__name__) if self.enabled else None

    def _should_enable_logging(self):
        """根据配置决定是否启用日志"""
        logging_settings = self.config.get('logging_settings', {})
        return logging_settings.get('enable_detailed_logging', False)

    def log(self, message, level='INFO'):
        """输出日志消息"""
        if not self.enabled or not self.logger:
            return

        level_map = {
            'DEBUG': self.logger.debug,
            'INFO': self.logger.info,
            'WARNING': self.logger.warning,
            'ERROR': self.logger.error,
            'CRITICAL': self.logger.critical
        }

        log_func = level_map.get(level.upper(), self.logger.info)
        log_func(message)

    def debug(self, message):
        self.log(message, 'DEBUG')

    def info(self, message):
        self.log(message, 'INFO')

    def warning(self, message):
        self.log(message, 'WARNING')

    def error(self, message):
        self.log(message, 'ERROR')


# 全局日志实例
app_logger = ConfigurableLogger()

# 导入选股算法

def compute_kdj(df, n=9, m1=3, m2=3):
    """计算KDJ指标"""
    low_list = df['low'].rolling(n, min_periods=1).min()
    high_list = df['high'].rolling(n, min_periods=1).max()
    rsv = (df['close'] - low_list) / (high_list - low_list) * 100

    kdj = pd.DataFrame()
    kdj['K'] = rsv.ewm(com=m1-1).mean()
    kdj['D'] = kdj['K'].ewm(com=m2-1).mean()
    kdj['J'] = 3 * kdj['K'] - 2 * kdj['D']

    return kdj

# --------------------------- 极致缩量策略 --------------------------- #
class JZVolumeShrinkSelector:
    """
    极致缩量策略：
    1. A点-B点：N1日内最低点到最高点涨幅≥rise_pct
    2. B点-C点：B点后N2日内最高点到最低点跌幅≥fall_pct
    3. D点：C点后出现小放量上影线K线
    4. E点：D点后出现极致缩量阴线，成交量低于均量和D点，收盘价<开盘价
    所有参数均可配置。
    """
    
    def __init__(
        self,
        rise_pct, # A点-B点：N1日内最低点到最高点涨幅≥rise_pct
        fall_pct, # B点-C点：B点-E后最高点到最低点跌幅≥fall_pct
        n1,
        a_consolidation_days,
        a_consolidation_range,
        a_downward_min_pct,
        a_downward_max_pct,
        a_downward_range_multiplier,
        d_vol_ratio,
        d_vol_max_ratio,
        upper_shadow_ratio,
        e_vol_vs_cd_avg_ratio,
        e_vs_d_vol_ratio,
        de_max_days,
        e_yang_threshold,
        shadow_ratio,
        ce_rise_ratio,
        ab_max_days,
        cd_max_distance_trade_days: int,  # C-D点最大距离天数
        d_lookback_trade_days: int,  # D点放量比较的向前查找天数
    ) -> None:
        # 参数有效性检查
        params = locals()
        for k, v in params.items():
            if k != 'self' and v is None:
                raise ValueError(f"参数 {k} 不能为空")
        self.rise_pct = rise_pct
        self.fall_pct = fall_pct
        self.n1 = n1
        self.a_consolidation_days = a_consolidation_days
        self.a_consolidation_range = a_consolidation_range
        self.a_downward_min_pct = a_downward_min_pct
        self.a_downward_max_pct = a_downward_max_pct
        self.a_downward_range_multiplier = a_downward_range_multiplier
        self.d_vol_ratio = d_vol_ratio
        self.d_vol_max_ratio = d_vol_max_ratio
        self.upper_shadow_ratio = upper_shadow_ratio
        self.e_vol_vs_cd_avg_ratio = e_vol_vs_cd_avg_ratio
        self.e_vs_d_vol_ratio = e_vs_d_vol_ratio
        self.de_max_days = de_max_days
        self.e_yang_threshold = e_yang_threshold
        self.shadow_ratio = shadow_ratio
        self.ce_rise_ratio = ce_rise_ratio
        self.ab_max_days = ab_max_days
        self.cd_max_distance_trade_days = cd_max_distance_trade_days
        self.d_lookback_trade_days = d_lookback_trade_days

    def _check_ce_max_rise(self, hist: pd.DataFrame, b_idx: int, c_idx: int, e_idx: int) -> bool:
        """
        检查C-E区间价格最大值不能超过B点最大值减去(B点最大值- C点最小值)*ce_rise_ratio + C点最小值
        """
        c_price = hist.loc[c_idx, 'low']  # C点最小值
        b_price = hist.loc[b_idx, 'high']  # B点最大值
        ce_data = hist.loc[c_idx+1:e_idx]
        if ce_data.empty or c_price <= 0 or b_price <= 0:
            return True

        # C-E区间实体最大值
        peak_price = max(ce_data['close'].max(), ce_data['open'].max())

        # 计算阈值
        ce_max_allowed_price = c_price + (b_price - c_price) * self.ce_rise_ratio
        self.log(f'C-E区间检查: B点最大值={b_price:.4f}, C点最小值={c_price:.4f}, 区间最高点={peak_price:.4f}, 允许阈值={ce_max_allowed_price:.4f}, 涨幅比例={self.ce_rise_ratio:.4f}')
        return peak_price <= ce_max_allowed_price

    def _find_d_point(self, hist: pd.DataFrame, c_idx: int, e_idx: int) -> tuple[int, int]:
        """
        在C点和E点之间查找轻微放量试盘上影线D点
        如果D点距离C点太远，则使用D点前N天的均值作为比较基准
        返回: (d_idx, new_c_idx)
        """
        d_idx = None
        new_c_idx = c_idx  # 默认使用原始C点
        # D点和E点间隔不能超过de_max_days天
        d_search_start = e_idx - 1
        d_search_end = max(c_idx + 1, d_search_start - self.de_max_days + 1)
        
        for i in range(d_search_start, d_search_end - 1, -1):
            if i < c_idx + 1:
                break

            d_idx = i
            row = hist.loc[d_idx]
            d_date = row['date']
            entity = abs(row['close'] - row['open'])
            # 当天K线内部比例
            upper_shadow = (row['high'] - max(row['close'], row['open'])) / row['open'] if row['open'] != 0 else 0
            lower_shadow = (min(row['close'], row['open']) - row['low']) / row['open'] if row['open'] != 0 else 0
            entity_pct = entity / row['open'] if row['open'] != 0 else 0

            # 计算D点距离C点的天数
            cd_distance = d_idx - c_idx
            
            # 根据距离决定使用哪个区间的均值
            if cd_distance > self.cd_max_distance_trade_days:
                # 距离太远，使用D点前d_lookback_days天的均值
                lookback_start = max(0, d_idx - self.d_lookback_trade_days)
                vol_data = hist.loc[lookback_start:d_idx-1]
                vol_mean = vol_data['volume'].mean()
                # 更新C点为D点前d_lookback_days天的位置
                new_c_idx = lookback_start
                self.log(f'D点候选idx: {d_idx}, 日期: {d_date}, 距离C点{cd_distance}天 > {self.cd_max_distance_trade_days}天，使用前{self.d_lookback_trade_days}天均值，更新C点idx为{new_c_idx},新C点为{hist.loc[new_c_idx, "date"]}')
                self.log(f'  D成交量: {row["volume"]:.0f}, [{vol_data.iloc[0]["date"]} - {vol_data.iloc[-1]["date"]}]均量: {vol_mean:.0f}')
            else:
                # 距离合适，使用C-D区间的均值
                between_c_d = hist.loc[c_idx:d_idx-1]
                vol_mean = between_c_d['volume'].mean()
                self.log(f'D点候选idx: {d_idx}, 日期: {d_date}, 距离C点{cd_distance}天 <= {self.cd_max_distance_trade_days}天，使用C-D区间均值')
                self.log(f'  D成交量: {row["volume"]:.0f}, [{between_c_d.iloc[0]["date"]} - {between_c_d.iloc[-1]["date"]}]均量: {vol_mean:.0f}')

            # 判断各个条件
            vol_condition1 = row['volume'] > vol_mean * self.d_vol_ratio
            vol_condition2 = row['volume'] < vol_mean * self.d_vol_max_ratio
            shadow_condition1 = upper_shadow >= entity_pct * self.upper_shadow_ratio
            shadow_condition2 = upper_shadow >= lower_shadow * self.shadow_ratio  # 上影线比例是下影线比例的N倍
            
            self.log(f'  成交量条件1(>均量* {self.d_vol_ratio:.0f}): {vol_condition1}, 条件2(<均量* {self.d_vol_max_ratio:.0f}): {vol_condition2}')
            self.log(f'  实体比例: {entity_pct:.8f}, 上影线比例: {upper_shadow:.8f}, 下影线比例: {lower_shadow:.8f}')
            self.log(f'  上影线条件(>={entity_pct * self.upper_shadow_ratio:.8f}): {shadow_condition1}, 上影线比例>下影线比例*{self.shadow_ratio:.2f}: {shadow_condition2}')
            
            if vol_condition1 and vol_condition2 and shadow_condition1 and shadow_condition2:
                self.log(f'  ✓ 找到D点: {d_idx}, 日期: {d_date}')
                return d_idx, new_c_idx
            else:
                self.log(f'  ✗ 不符合D点条件')
                if not vol_condition1:
                    self.log(f'    原因: 成交量不足 (需要>{vol_mean * self.d_vol_ratio:.0f})')
                elif not vol_condition2:
                    self.log(f'    原因: 成交量过大 (需要<{vol_mean * self.d_vol_max_ratio:.0f})')
                elif not shadow_condition1:
                    self.log(f'    原因: 上影线比例不足 (需要>={entity_pct * self.upper_shadow_ratio:.4f})')
                elif not shadow_condition2:
                    self.log(f'    原因: 上影线比例不足下影线比例的{self.shadow_ratio:.2f}倍 (上影线比例={upper_shadow:.8f}, 下影线比例*{self.shadow_ratio:.2f}={lower_shadow*self.shadow_ratio:.8f})')
        
        return None, None

    def _check_e_price_condition(self, hist: pd.DataFrame, e_idx: int) -> bool:
        """
        检查E点的价格条件
        1. 阴线或小阳线（收盘价<开盘价 或 涨幅<e_yang_threshold）
        2. 今日收盘价比昨日收盘价的涨幅不超过e_yang_threshold
        """
        e_row = hist.loc[e_idx]
        
        # 条件1：阴线或小阳线
        price_condition1 = e_row['close'] < e_row['open'] or (e_row['close'] - e_row['open']) / e_row['open'] < self.e_yang_threshold
        
        # 条件2：今日收盘价比昨日收盘价的涨幅不超过e_yang_threshold
        price_condition2 = (e_row['close'] - hist.loc[e_idx-1, 'close']) / hist.loc[e_idx-1, 'close'] < self.e_yang_threshold
        
        price_condition = price_condition1 or price_condition2
        
        if not price_condition:
            self.log(f'E点价格条件不满足: 日期={e_row["date"]}, 开盘={e_row["open"]:.4f}, 收盘={e_row["close"]:.4f}, 昨日收盘={hist.loc[e_idx-1, "close"]:.4f}')
            self.log(f'  条件1(阴线或小阳): {price_condition1}, 条件2(涨幅限制): {price_condition2}')
        
        return price_condition

    def _find_e_point(self, hist: pd.DataFrame, c_idx: int, d_idx: int, e_idx: int) -> bool:
        """
        判断E点是否是缩量洗盘线
        使用更新后的C点计算C-D区间均值
        """
        e_row = hist.loc[e_idx]
        e_date = hist.loc[e_idx, 'date']
        
        # 重新计算C-D区间的均量（用于E点判断）
        c2d_data = hist.loc[c_idx+1:d_idx]
        c2d_vol_mean = c2d_data['volume'].mean()
        d_vol = hist.loc[d_idx, 'volume']
        
        self.log(f'E点检查: idx={e_idx}, 日期={e_date}, 成交量={e_row["volume"]:.0f}, C-D均量={c2d_vol_mean:.0f}, D点量={d_vol:.0f}, 收盘={e_row["close"]:.4f}, 开盘={e_row["open"]:.4f}')

        # E点缩量条件改为与C-D均量比较
        # 判断E点条件
        vol_condition1 = e_row['volume'] < c2d_vol_mean * self.e_vol_vs_cd_avg_ratio
        vol_condition2 = e_row['volume'] < d_vol * self.e_vs_d_vol_ratio
        price_condition = self._check_e_price_condition(hist, e_idx)
        
        self.log(f'E点条件检查: 缩量1(C-D成交量均值*{self.e_vol_vs_cd_avg_ratio:.2f}：{c2d_vol_mean * self.e_vol_vs_cd_avg_ratio:.0f})={vol_condition1}, 缩量2(D点成交量*{self.e_vs_d_vol_ratio:.2f}：{d_vol * self.e_vs_d_vol_ratio:.0f})={vol_condition2}, 价格条件={price_condition}')
    
        # 判断E点为缩量涨停线（最高价和最低价涨跌比例小于0.1%）
        if e_row['low'] > 0:
            limit_line = (e_row['high'] - e_row['low']) / e_row['low']
            if limit_line < 0.001:
                self.log(f'E点为缩量涨停线（高低价涨跌比例仅{limit_line:.4%}），跳过。')
                return False

        if vol_condition1 and vol_condition2 and price_condition:
            self.log(f'✓ E点符合条件: idx={e_idx}, 日期={e_date}')
            return True
        else:
            self.log(f'✗ E点不符合条件')
            if not vol_condition1:
                self.log(f'  原因: 成交量过大 (需要<{c2d_vol_mean * self.e_vol_vs_cd_avg_ratio:.0f})')
            elif not vol_condition2:
                self.log(f'  原因: 成交量过大 (需要<{d_vol * self.e_vs_d_vol_ratio:.0f})')
            elif not price_condition:
                self.log(f'  原因: 不是阴线或小阳线')
            return False

    def _is_consolidation(self, data: pd.DataFrame) -> bool:
        """
        判断一段数据是否为区间震荡
        条件：最高价/最低价 - 1 <= a_consolidation_range
        """
        if len(data) < 3:  # 至少需要3天数据
            return False
        high = data['high'].max()
        low = data['low'].min()
        if low <= 0:
            return False
        range_pct = (high / low - 1)
        self.log(f'区间震荡检查: 日期[{data.iloc[0]["date"]} - {data.iloc[-1]["date"]}], 最高价={high:.4f}, 最低价={low:.4f}, 震荡幅度={range_pct:.4f}, 阈值={self.a_consolidation_range}')
        return range_pct <= self.a_consolidation_range

    def _is_consolidation_downward(self, data: pd.DataFrame) -> bool:
        """
        判断一段数据是否为震荡下行区间
        条件：
        1. 整体趋势向下（收盘价趋势）
        2. 但波动幅度仍然可控（最高价/最低价 - 1 <= a_consolidation_range * 1.5）
        3. 下跌幅度不超过阈值（首日收盘价/末日收盘价 - 1 <= 0.2）
        """
        if len(data) < 3:  # 至少需要3天数据
            return False
            
        # 计算整体趋势（首日到末日的收盘价变化）
        first_close = data.iloc[0]['close']
        last_close = data.iloc[-1]['close']
        if first_close <= 0:
            return False
            
        trend_pct = (first_close - last_close) / first_close  # 下跌幅度
        
        # 计算波动幅度
        high = data['high'].max()
        low = data['low'].min()
        if low <= 0:
            return False
        range_pct = (high / low - 1)
        
        # 判断条件：
        # 1. 整体趋势向下（下跌幅度在配置范围内）
        # 2. 波动幅度在可接受范围内
        is_downward = trend_pct > self.a_downward_min_pct and trend_pct <= self.a_downward_max_pct
        is_controlled_range = range_pct <= self.a_consolidation_range * self.a_downward_range_multiplier
        
        self.log(f'震荡下行检查: 趋势跌幅={trend_pct:.4f}, 波动幅度={range_pct:.4f}, 阈值={self.a_consolidation_range * self.a_downward_range_multiplier:.4f}, 是否下行={is_downward}, 是否可控={is_controlled_range}')
        
        return is_downward and is_controlled_range

    def _find_uptrend_start(self, hist: pd.DataFrame, b_idx: int) -> int:
        """
        从B点往前遍历，找到主升浪的起点A点
        A点的特征：A点前N天是震荡，A点后开始上涨，且A-B涨幅满足要求
        """
        self.log(f'开始寻找A点: 从B点({b_idx})往前搜索...')
        
        b_price = hist.loc[b_idx, 'high']  # B点价格
        self.log(f'B点价格: {b_price:.4f}')
       
        # 从B点往前遍历，寻找A点
        # 搜索范围：从B点往前最多搜索n1天，确保能找到n1区间内的A点
        max_lookback = min(b_idx, self.n1)  # 最多往前搜索n1天，但不能超过数据边界
        
        self.log(f'A点搜索区间: [{hist.loc[max(0, b_idx - max_lookback), "date"]} - {hist.loc[b_idx, "date"]}]')
        
        for i in range(b_idx - 1, max(0, b_idx - max_lookback), -1):
            # 检查i点前a_consolidation_days天是否为震荡
            if i < self.a_consolidation_days:
                continue
            
            # 如果 A 点当日涨幅大于 9%，认为A点涨幅过大可能是第一个涨停板不能作为主升浪起点，则跳过
            # 如果 A 点最高涨幅和昨日收盘比涨幅大于 9%，认为A点涨幅过大可能是第一个涨停板不能作为主升浪起点，则跳过
            if hist.loc[i, 'high'] > hist.loc[i, 'low'] * 1.09 or (hist.loc[i, 'high'] - hist.loc[i-1, 'close']) / hist.loc[i-1, 'close'] > 0.09:
                self.log(f'候选点 A点 {hist.loc[i, "date"]} 涨幅过大，可能是第一个涨停板不能作为主升浪起点，跳过')
                continue
            
            
            consolidation_data = hist.loc[i-self.a_consolidation_days:i]
            self.log(f'  检查候选点{i}: 区间长度={len(consolidation_data)}, 日期[{hist.loc[i-self.a_consolidation_days, "date"]} - {hist.loc[i, "date"]}]')

            is_consolidation = self._is_consolidation(consolidation_data)
            is_consolidation_downward = self._is_consolidation_downward(consolidation_data)
            
            self.log(f'  检查结果: 区间震荡={is_consolidation}, 震荡下行={is_consolidation_downward}')

            if is_consolidation or is_consolidation_downward:
                # 找到满足震荡条件的候选A点，现在检查A-B涨幅
                a_price = hist.loc[i, 'low']  # 使用当日最低价作为A点价格
                ab_rise = (b_price - a_price) / a_price
                
                self.log(f'  候选A点涨幅检查: A点价格={a_price:.4f}, B点价格={b_price:.4f}, 涨幅={ab_rise:.4f}, 阈值={self.rise_pct}')
                
                if ab_rise >= self.rise_pct:
                    # 检查A-B点之间的间隔天数
                    a_date = hist.loc[i, 'date']
                    b_date = hist.loc[b_idx, 'date']
                    ab_days = (b_date - a_date).days
                    
                    self.log(f'  候选A点间隔检查: A点日期={a_date}, B点日期={b_date}, 间隔天数={ab_days}, 最大允许天数={self.ab_max_days}')
                    
                    if ab_days <= self.ab_max_days:
                        pattern_type = "区间震荡" if is_consolidation else "震荡下行"
                        self.log(f'找到满足条件的A点: idx={i}, 日期={a_date}, 前区间类型={pattern_type}, A-B涨幅={ab_rise:.4f}, 间隔天数={ab_days}')
                        return i
                    else:
                        self.log(f'  候选A点间隔过长: {ab_days}天 > {self.ab_max_days}天')
                else:
                    self.log(f'  候选A点涨幅不达标: {ab_rise:.4f} < {self.rise_pct}')

        self.log('未找到满足条件的A点')
        return -1

    def _passes_filters(self, hist: pd.DataFrame, code: str = '', date: str = '') -> bool:
        # 添加调用次数统计，避免重复处理
        if not hasattr(self, '_call_count'):
            self._call_count = 0
        self._call_count += 1
        
        self.log(f'\n=== 第{self._call_count}次调用 ===')
        self.log(f'股票: {code} 日期: {date} 总K线数: {len(hist)}')
        
        if len(hist) < self.n1 + 10:
            self.log(f'K线数量不足: {len(hist)} < {self.n1 + 10}')
            return False
        
        hist = hist.copy().reset_index(drop=True)
        
        # E点就是传入的日期（今天）
        e_idx = len(hist) - 1  # 最后一天
        e_date = hist.loc[e_idx, 'date']
        self.log(f'E点idx: {e_idx}, 日期: {e_date} (传入的日期)')
        if str(date) != str(e_date):
            self.log(f'传入日期不存在，传入日期 {date} 与 E点日期 {e_date} 不匹配')
            return False
        
        # 提前检查E点价格条件
        if not self._check_e_price_condition(hist, e_idx):
            self.log(f'E点价格条件不满足，跳过')
            return False
        
        # 1. 获取E点前60个交易日的数据
        search_window = hist.tail(self.n1 + 1)  # 包含E点在内的n1+1天
        search_window = search_window.iloc[:-1]  # 排除E点，只取前n1天
        
        self.log(f'搜索窗口: 从{search_window.iloc[0]["date"]}到{search_window.iloc[-1]["date"]}, 共{len(search_window)}天')
        
        # 2. 在搜索窗口内找B点（最高点）
        b_idx = search_window['high'].idxmax()
        b_date = search_window.loc[b_idx, 'date']
        b_price = search_window.loc[b_idx, 'high']
        self.log(f'B点idx: {b_idx}, 日期: {b_date}, 价格: {b_price:.4f} (最高点)')
        
        # 3. 从B点往前寻找主升浪起点A点（包含A-B涨幅检查）
        a_idx = self._find_uptrend_start(hist, b_idx)
        if a_idx == -1:
            return False
            
        a_date = hist.loc[a_idx, 'date']
        a_price = hist.loc[a_idx, 'low']  # 使用当日最低价作为A点价格
        self.log(f'A点idx: {a_idx}, 日期: {a_date}, 价格: {a_price:.4f} (主升浪起点)')
            
        # 4. 在B点和E点之间寻找最低点C点
        between_b_e = hist.loc[b_idx+1:e_idx-1]  # B点后到E点前
        if between_b_e.empty:
            self.log('B点到E点间无数据')
            return False
            
        c_idx = between_b_e['low'].idxmin()
        c_date = between_b_e.loc[c_idx, 'date']
        c_price = between_b_e.loc[c_idx, 'low']
        self.log(f'C点idx: {c_idx}, 日期: {c_date}, 价格: {c_price:.4f} (B到E间最低点)')
        
        # # 如果C点最低价小于A点最高价，认为没有主力维护股价，下跌趋势没有企稳，直接跳过
        # a_high = max(hist.loc[a_idx, 'open'], hist.loc[a_idx, 'close'])
        # if c_price < a_high:
        #     self.log(f'C点最低价({c_price:.4f}) < A点最高价({a_high:.4f})，无主力维护，趋势未企稳，跳过')
        #     return False

        # 5. 判断C点和B点之间是否有一定的跌幅
        bc_fall = (b_price - c_price) / b_price
        self.log(f'B-C跌幅检查: B点价格={b_price:.4f}, C点价格={c_price:.4f}, 跌幅={bc_fall:.4f}, 阈值={self.fall_pct}')
        
        if bc_fall < self.fall_pct:
            self.log('B-C跌幅不达标')
            return False
        
        # 6. 检查C-E区间最高点涨幅，避免第二次主升浪
        if not self._check_ce_max_rise(hist, b_idx, c_idx, e_idx):
            self.log('C-E区间最高点涨幅过大，可能形成第二次主升浪')
            return False
        else:
            self.log('C-E区间最高点涨幅在合理范围内')        

        # 7. 在C点和E点之间查找轻微放量试盘上影线D点
        d_idx, new_c_idx = self._find_d_point(hist, c_idx, e_idx)
        # 检查D点是否是阳线
        if d_idx is None or d_idx is not None and hist.loc[d_idx, 'close'] > hist.loc[d_idx, 'open']:
            return False
        
        # # 检查 C点-D点之间是否有涨停
        # for i in range(c_idx, d_idx):
        #     if hist.loc[i, 'high'] > hist.loc[i, 'low'] * 1.09 or (hist.loc[i, 'high'] - hist.loc[i-1, 'close']) / hist.loc[i-1, 'close'] > 0.09:
        #         self.log(f'C点-D点之间有涨停，不考虑: {hist.loc[i, "date"]}')
        #         return False

        # 如果C点被更新，记录日志
        if new_c_idx != c_idx:
            self.log(f'C点已更新: 原始C点idx={c_idx}, 新C点idx={new_c_idx}, 日期={hist.loc[new_c_idx, "date"]}')
            c_idx = new_c_idx
            c_date = hist.loc[c_idx, 'date']
            
        # 8. 判断E点是否是缩量洗盘线
        if not self._find_e_point(hist, c_idx, d_idx, e_idx):
            self.log('E点条件不满足')
            return False
            
        self.log(f'选出E点: idx={e_idx}, 日期={e_date}, 股票={code}')
        self.log(f'完整路径: A点({a_date}) -> B点({b_date}) -> C点({c_date}) -> D点({hist.loc[d_idx, "date"]}) -> E点({e_date})')
        
        # 添加详细的参数信息用于进一步筛选
        self.log(f'=== 详细参数信息 ===')
        
        self.log(f'股票代码: {code}')

        # 各点基本信息
        a_row = hist.loc[a_idx]
        b_row = hist.loc[b_idx]
        c_row = hist.loc[c_idx]
        d_row = hist.loc[d_idx]
        e_row = hist.loc[e_idx]
        
        # A点信息
        a_entity_pct = (a_row['close'] - a_row['open']) / a_row['open'] if a_row['open'] != 0 else 0
        a_amplitude = (a_row['high'] - a_row['low']) / a_row['low'] if a_row['low'] != 0 else 0
        self.log(f'A点: 日期={a_date}, 开盘={a_row["open"]:.4f}, 收盘={a_row["close"]:.4f}, 最高={a_row["high"]:.4f}, 最低={a_row["low"]:.4f}, 成交量={a_row["volume"]:.0f}, 实体涨跌幅={a_entity_pct:.4f}, 价格振幅={a_amplitude:.4f}')
        
        # B点信息
        b_entity_pct = (b_row['close'] - b_row['open']) / b_row['open'] if b_row['open'] != 0 else 0
        b_amplitude = (b_row['high'] - b_row['low']) / b_row['low'] if b_row['low'] != 0 else 0
        self.log(f'B点: 日期={b_date}, 开盘={b_row["open"]:.4f}, 收盘={b_row["close"]:.4f}, 最高={b_row["high"]:.4f}, 最低={b_row["low"]:.4f}, 成交量={b_row["volume"]:.0f}, 实体涨跌幅={b_entity_pct:.4f}, 价格振幅={b_amplitude:.4f}')
        
        # C点信息
        c_entity_pct = (c_row['close'] - c_row['open']) / c_row['open'] if c_row['open'] != 0 else 0
        c_amplitude = (c_row['high'] - c_row['low']) / c_row['low'] if c_row['low'] != 0 else 0
        self.log(f'C点: 日期={c_date}, 开盘={c_row["open"]:.4f}, 收盘={c_row["close"]:.4f}, 最高={c_row["high"]:.4f}, 最低={c_row["low"]:.4f}, 成交量={c_row["volume"]:.0f}, 实体涨跌幅={c_entity_pct:.4f}, 价格振幅={c_amplitude:.4f}')
        
        # D点信息
        d_date = hist.loc[d_idx, 'date']
        d_entity_pct = (d_row['close'] - d_row['open']) / d_row['open'] if d_row['open'] != 0 else 0
        d_amplitude = (d_row['high'] - d_row['low']) / d_row['low'] if d_row['low'] != 0 else 0
        self.log(f'D点: 日期={d_date}, 开盘={d_row["open"]:.4f}, 收盘={d_row["close"]:.4f}, 最高={d_row["high"]:.4f}, 最低={d_row["low"]:.4f}, 成交量={d_row["volume"]:.0f}, 实体涨跌幅={d_entity_pct:.4f}, 价格振幅={d_amplitude:.4f}')
        
        # E点信息
        e_entity_pct = (e_row['close'] - e_row['open']) / e_row['open'] if e_row['open'] != 0 else 0
        e_amplitude = (e_row['high'] - e_row['low']) / e_row['low'] if e_row['low'] != 0 else 0
        self.log(f'E点: 日期={e_date}, 开盘={e_row["open"]:.4f}, 收盘={e_row["close"]:.4f}, 最高={e_row["high"]:.4f}, 最低={e_row["low"]:.4f}, 成交量={e_row["volume"]:.0f}, 实体涨跌幅={e_entity_pct:.4f}, 价格振幅={e_amplitude:.4f}')
        
        # 区间信息
        # A-B区间
        ab_rise = (b_price - a_price) / a_price
        ab_days = (b_date - a_date).days
        self.log(f'A-B区间: 涨幅={ab_rise:.4f}, 时间间隔={ab_days}天')
        
        # B-C区间
        bc_fall = (b_price - c_price) / b_price
        bc_days = (c_date - b_date).days
        self.log(f'B-C区间: 跌幅={bc_fall:.4f}, 时间间隔={bc_days}天')
        
        # C-D区间
        cd_rise = (d_row['close'] - c_row['close']) / c_row['close'] if c_row['close'] != 0 else 0
        cd_days = (d_date - c_date).days
        self.log(f'C-D区间: 涨幅={cd_rise:.4f}, 时间间隔={cd_days}天')
        
        # D-E区间
        de_rise = (e_row['close'] - d_row['close']) / d_row['close'] if d_row['close'] != 0 else 0
        de_days = (e_date - d_date).days
        self.log(f'D-E区间: 涨幅={de_rise:.4f}, 时间间隔={de_days}天')
        
        # 成交量比例信息
        # 重新计算C-D区间成交量均值
        c2d_data = hist.loc[c_idx+1:d_idx]
        c2d_vol_mean = c2d_data['volume'].mean()
        
        # D点成交量比例
        d_vol_ratio_vs_cd = d_row['volume'] / c2d_vol_mean if c2d_vol_mean != 0 else 0
        self.log(f'D点成交量/C-D均量={d_vol_ratio_vs_cd:.2f}')
              
        # D点上影线比例
        d_upper_shadow = (d_row['high'] - max(d_row['close'], d_row['open'])) / d_row['open'] if d_row['open'] != 0 else 0
        self.log(f'D点上影线涨幅={d_upper_shadow:.2f}')

        # D点上影线比例
        d_upper_shadow_vs_entity = d_upper_shadow / d_entity_pct if d_entity_pct != 0 else 0
        self.log(f'D点上影线和实体比例上影线/实体={d_upper_shadow_vs_entity:.2f}')

        # E点成交量比例
        e_vol_ratio_vs_cd = e_row['volume'] / c2d_vol_mean if c2d_vol_mean != 0 else 0
        e_vol_ratio_vs_d = e_row['volume'] / d_row['volume'] if d_row['volume'] != 0 else 0
        self.log(f'E点成交量/C-D均量={e_vol_ratio_vs_cd:.2f}, E点成交量/D点成交量={e_vol_ratio_vs_d:.2f}')
        
        # E点的 KDJ的J值
        kdj = compute_kdj(hist)
        self.log(f'E点的J值={kdj.loc[e_idx, "J"]:.2f}')
        
        # E点J值相对D点J值的涨幅
        e_vs_d_j_pct = (kdj.loc[e_idx, "J"] - kdj.loc[d_idx, "J"]) / kdj.loc[d_idx, "J"] if kdj.loc[d_idx, "J"] != 0 else 0
        self.log(f'E点J值相对D点J值的涨幅={e_vs_d_j_pct:.2f}')
        
        # E点相对D点收盘价的涨幅
        e_vs_d_pct = (e_row['close'] - d_row['close']) / d_row['close'] if d_row['close'] != 0 else 0
        self.log(f'E点相对D点收盘价的涨幅={e_vs_d_pct:.2f}')
        
        self.log(f'=== 参数信息结束 ===')
        
        return True

    def select(self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]) -> List[str]:
        picks: List[str] = []

        # # 第一次调用时打印全部参数
        # self.log(f'\n=== JZVolumeShrinkSelector 参数配置 ===')
        # self.log(f'rise_pct: {self.rise_pct}')
        # self.log(f'fall_pct: {self.fall_pct}')
        # self.log(f'n1: {self.n1}')
        # self.log(f'a_consolidation_days: {self.a_consolidation_days}')
        # self.log(f'a_consolidation_range: {self.a_consolidation_range}')
        # self.log(f'a_downward_min_pct: {self.a_downward_min_pct}')
        # self.log(f'a_downward_max_pct: {self.a_downward_max_pct}')
        # self.log(f'a_downward_range_multiplier: {self.a_downward_range_multiplier}')
        # self.log(f'd_vol_ratio: {self.d_vol_ratio}')
        # self.log(f'd_vol_max_ratio: {self.d_vol_max_ratio}')
        # self.log(f'upper_shadow_ratio: {self.upper_shadow_ratio}')
        # self.log(f'e_vol_vs_cd_avg_ratio: {self.e_vol_vs_cd_avg_ratio}')
        # self.log(f'e_vs_d_vol_ratio: {self.e_vs_d_vol_ratio}')
        # self.log(f'de_max_days: {self.de_max_days}')
        # self.log(f'e_yang_threshold: {self.e_yang_threshold}')
        # self.log(f'shadow_ratio: {self.shadow_ratio}')
        # self.log(f'ce_rise_ratio: {self.ce_rise_ratio}')
        # self.log(f'ab_max_days: {self.ab_max_days}')
        # self.log(f'cd_max_distance_trade_days: {self.cd_max_distance_trade_days}')
        # self.log(f'd_lookback_trade_days: {self.d_lookback_trade_days}')
        # self.log(f'=== 参数配置结束 ===\n')

        global _interrupted

        for code, df in tqdm(data.items(), desc=f"选股分析 {date.date()}", leave=False):
            if _interrupted:
                break
            hist = df[df['date'] <= date]
            if hist.empty:
                continue
            res = self._passes_filters(hist, code=code, date=str(date))
            if res:
                self.log(f'最终选出: {code} {date}')
                picks.append(code)
            else:
                self.log(f'未选出: {code} {date}')
        return picks


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局中断标志
_interrupted = False

def signal_handler(signum, frame):
    """信号处理函数"""
    global _interrupted
    _interrupted = True
    logger.info("\n⚠️ 接收到中断信号，正在优雅退出...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

class JZVolumeShrinkRunner:
    """JZVolumeShrinkSelector 运行器"""
    
    def __init__(self, config_file: str = "jz_volume_config.json"):
        self.config_file = config_file
        self.config = {}
        self.stock_data = {}
        self.results = []
        self.statistics = {
            'total_processed': 0,
            'total_selected': 0,
            'success_3d_5pct': 0,  # 3日涨幅>5%
            'success_5d_10pct': 0, # 5日涨幅>10%
            'errors': 0
        }
        self.selector = None
        self.log = ConfigurableLogger()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)

            # 重新初始化日志类
            self.log = ConfigurableLogger(self.config)

            logger.info(f"✅ 配置文件加载成功: {self.config_file}")
            return True
        except FileNotFoundError:
            logger.error(f"❌ 配置文件不存在: {self.config_file}")
            self.create_default_config()
            return False
        except json.JSONDecodeError as e:
            logger.error(f"❌ 配置文件格式错误: {e}")
            return False
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "algorithm_params": {
                "rise_pct": 0.15,
                "fall_pct": 0.10,
                "n1": 20,
                "a_consolidation_days": 5,
                "a_consolidation_range": 0.05,
                "a_downward_min_pct": 0.02,
                "a_downward_max_pct": 0.08,
                "a_downward_range_multiplier": 1.5,
                "d_vol_ratio": 1.2,
                "d_vol_max_ratio": 3.0,
                "upper_shadow_ratio": 0.3,
                "e_vol_vs_cd_avg_ratio": 0.5,
                "e_vs_d_vol_ratio": 0.8,
                "de_max_days": 10,
                "e_yang_threshold": 0.01,
                "shadow_ratio": 0.2,
                "ce_rise_ratio": 0.3,
                "ab_max_days": 30,
                "cd_max_distance_trade_days": 15,
                "d_lookback_trade_days": 10
            },
            "data_settings": {
                "stock_data_dir": "stock_data",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                }
            },
            "analysis_settings": {
                "success_threshold_3d": 5.0,
                "success_threshold_5d": 10.0,
                "batch_size": 10,
                "calculate_future_gains": True
            },
            "threading_settings": {
                "max_workers": 4,
                "enable_threading": True,
                "thread_timeout": 300
            },
            "logging_settings": {
                "enable_file_logging": False,
                "enable_detailed_logging": False,
                "log_level": "INFO",
                "log_file": "jz_volume_analysis.log",
                "log_format": "%(asctime)s - %(levelname)s - %(message)s",
                "max_log_size_mb": 10,
                "backup_count": 3
            },
            "output_settings": {
                "save_intermediate": True,
                "output_dir": "./results",
                "include_timestamp": True
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 已创建默认配置文件: {self.config_file}")
        logger.info("请修改配置文件后重新运行")

    def setup_logging(self):
        """设置日志配置"""
        logging_settings = self.config.get('logging_settings', {})

        # 获取配置参数
        enable_file_logging = logging_settings.get('enable_file_logging', True)
        log_level = logging_settings.get('log_level', 'INFO')
        log_file = logging_settings.get('log_file', 'jz_volume_analysis.log')
        log_format = logging_settings.get('log_format', '%(asctime)s - %(levelname)s - %(message)s')
        max_log_size_mb = logging_settings.get('max_log_size_mb', 10)
        backup_count = logging_settings.get('backup_count', 3)

        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 设置日志级别
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        root_logger.setLevel(numeric_level)

        # 创建格式器
        formatter = logging.Formatter(log_format)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 文件处理器（如果启用）
        if enable_file_logging:
            try:
                # 创建日志目录
                log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else '.'
                os.makedirs(log_dir, exist_ok=True)

                # 创建旋转文件处理器
                file_handler = RotatingFileHandler(
                    log_file,
                    maxBytes=max_log_size_mb * 1024 * 1024,  # 转换为字节
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                file_handler.setLevel(numeric_level)
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)

                logger.info(f"✅ 日志文件配置成功: {log_file}")

            except Exception as e:
                logger.warning(f"⚠️ 无法设置文件日志: {e}")

        logger.info(f"📝 日志级别设置为: {log_level}")
        return True
    
    def load_stock_data(self):
        """加载股票数据"""
        data_dir = self.config.get('data_settings', {}).get('stock_data_dir', 'stock_data')
        
        if not os.path.exists(data_dir):
            logger.error(f"❌ 股票数据目录不存在: {data_dir}")
            return False
        
        logger.info(f"📂 开始加载股票数据: {data_dir}")
        self.log.info(f"开始加载股票数据目录: {data_dir}")

        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

        for csv_file in csv_files:
            try:
                stock_code = csv_file.replace('.csv', '')
                file_path = os.path.join(data_dir, csv_file)

                # 读取CSV文件
                df = pd.read_csv(file_path)

                # 转换日期格式
                df['date'] = pd.to_datetime(df['date'])

                # 按日期排序
                df = df.sort_values('date').reset_index(drop=True)

                self.stock_data[stock_code] = df
                self.log.debug(f"成功加载股票数据: {stock_code}, 数据行数: {len(df)}")

            except Exception as e:
                logger.error(f"❌ 加载股票数据失败 {csv_file}: {e}")
                self.log.error(f"加载股票数据失败 {csv_file}: {e}")
                continue

        logger.info(f"✅ 成功加载 {len(self.stock_data)} 只股票数据")
        self.log.info(f"股票数据加载完成，共 {len(self.stock_data)} 只股票")
        return len(self.stock_data) > 0
    
    def initialize_selector(self):
        """初始化选股器"""
        try:
            params = self.config.get('algorithm_params', {})
            
            self.selector = JZVolumeShrinkSelector(
                rise_pct=params.get('rise_pct', 0.15),
                fall_pct=params.get('fall_pct', 0.10),
                n1=params.get('n1', 20),
                a_consolidation_days=params.get('a_consolidation_days', 5),
                a_consolidation_range=params.get('a_consolidation_range', 0.05),
                a_downward_min_pct=params.get('a_downward_min_pct', 0.02),
                a_downward_max_pct=params.get('a_downward_max_pct', 0.08),
                a_downward_range_multiplier=params.get('a_downward_range_multiplier', 1.5),
                d_vol_ratio=params.get('d_vol_ratio', 1.2),
                d_vol_max_ratio=params.get('d_vol_max_ratio', 3.0),
                upper_shadow_ratio=params.get('upper_shadow_ratio', 0.3),
                e_vol_vs_cd_avg_ratio=params.get('e_vol_vs_cd_avg_ratio', 0.5),
                e_vs_d_vol_ratio=params.get('e_vs_d_vol_ratio', 0.8),
                de_max_days=params.get('de_max_days', 10),
                e_yang_threshold=params.get('e_yang_threshold', 0.01),
                shadow_ratio=params.get('shadow_ratio', 0.2),
                ce_rise_ratio=params.get('ce_rise_ratio', 0.3),
                ab_max_days=params.get('ab_max_days', 30),
                cd_max_distance_trade_days=params.get('cd_max_distance_trade_days', 15),
                d_lookback_trade_days=params.get('d_lookback_trade_days', 10)
            )
            
            logger.info("✅ 选股器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 选股器初始化失败: {e}")
            return False
    
    def calculate_future_gains(self, stock_code: str, select_date: pd.Timestamp):
        """计算未来收益"""
        try:
            df = self.stock_data[stock_code]
            
            # 找到选股日期的索引
            select_idx = df[df['date'] == select_date].index
            if len(select_idx) == 0:
                return None, None
            
            select_idx = select_idx[0]
            select_price = df.loc[select_idx, 'close']
            
            # 计算3日和5日后的最高价
            gain_3d = 0.0
            gain_5d = 0.0
            
            # 3日最高涨幅
            for i in range(1, 4):
                if select_idx + i < len(df):
                    future_high = df.loc[select_idx + i, 'high']
                    gain = (future_high - select_price) / select_price * 100
                    gain_3d = max(gain_3d, gain)
            
            # 5日最高涨幅
            for i in range(1, 6):
                if select_idx + i < len(df):
                    future_high = df.loc[select_idx + i, 'high']
                    gain = (future_high - select_price) / select_price * 100
                    gain_5d = max(gain_5d, gain)
            
            return round(gain_3d, 2), round(gain_5d, 2)
            
        except Exception as e:
            logger.error(f"❌ 计算未来收益失败 {stock_code}-{select_date}: {e}")
            return None, None
    
    def run_selection_for_date(self, date: pd.Timestamp):
        """运行指定日期的选股"""
        try:
            # 调用选股算法
            selected_stocks = self.selector.select(date, self.stock_data)

            # 处理选股结果
            date_results = []
            date_stats = {
                'total_selected': 0,
                'success_3d_5pct': 0,
                'success_5d_10pct': 0,
                'errors': 0
            }

            for stock_code in selected_stocks:
                date_stats['total_selected'] += 1

                # 计算未来收益
                gain_3d, gain_5d = None, None
                if self.config.get('analysis_settings', {}).get('calculate_future_gains', True):
                    gain_3d, gain_5d = self.calculate_future_gains(stock_code, date)

                # 判断成功
                success_3d = gain_3d is not None and gain_3d >= self.config.get('analysis_settings', {}).get('success_threshold_3d', 5.0)
                success_5d = gain_5d is not None and gain_5d >= self.config.get('analysis_settings', {}).get('success_threshold_5d', 10.0)

                if success_3d:
                    date_stats['success_3d_5pct'] += 1
                if success_5d:
                    date_stats['success_5d_10pct'] += 1

                # 记录结果
                result = {
                    'date': date.strftime('%Y-%m-%d'),
                    'stock_code': stock_code,
                    'gain_3d': gain_3d,
                    'gain_5d': gain_5d,
                    'success_3d_5pct': success_3d,
                    'success_5d_10pct': success_5d,
                    'timestamp': datetime.now().isoformat()
                }

                date_results.append(result)

            return {
                'date': date,
                'results': date_results,
                'stats': date_stats,
                'selected_count': len(selected_stocks)
            }

        except Exception as e:
            logger.error(f"❌ 选股失败 {date}: {e}")
            return {
                'date': date,
                'results': [],
                'stats': {'total_selected': 0, 'success_3d_5pct': 0, 'success_5d_10pct': 0, 'errors': 1},
                'selected_count': 0
            }
    
    def run_selection_for_date_thread_safe(self, date: pd.Timestamp):
        """线程安全的单日选股方法"""
        try:
            # 为每个线程创建独立的选股器实例
            thread_selector = JZVolumeShrinkSelector(
                **self.config.get('algorithm_params', {})
            )

            # 调用选股算法
            selected_stocks = thread_selector.select(date, self.stock_data)

            # 处理选股结果
            date_results = []
            date_stats = {
                'total_selected': 0,
                'success_3d_5pct': 0,
                'success_5d_10pct': 0,
                'errors': 0
            }

            for stock_code in selected_stocks:
                date_stats['total_selected'] += 1

                # 计算未来收益
                gain_3d, gain_5d = None, None
                if self.config.get('analysis_settings', {}).get('calculate_future_gains', True):
                    gain_3d, gain_5d = self.calculate_future_gains(stock_code, date)

                # 判断成功
                success_3d = gain_3d is not None and gain_3d >= self.config.get('analysis_settings', {}).get('success_threshold_3d', 5.0)
                success_5d = gain_5d is not None and gain_5d >= self.config.get('analysis_settings', {}).get('success_threshold_5d', 10.0)

                if success_3d:
                    date_stats['success_3d_5pct'] += 1
                if success_5d:
                    date_stats['success_5d_10pct'] += 1

                # 记录结果
                result = {
                    'date': date.strftime('%Y-%m-%d'),
                    'stock_code': stock_code,
                    'gain_3d': gain_3d,
                    'gain_5d': gain_5d,
                    'success_3d_5pct': success_3d,
                    'success_5d_10pct': success_5d,
                    'timestamp': datetime.now().isoformat()
                }

                date_results.append(result)

            return {
                'date': date,
                'results': date_results,
                'stats': date_stats,
                'selected_count': len(selected_stocks),
                'success': True
            }

        except Exception as e:
            logger.error(f"❌ 线程选股失败 {date}: {e}")
            return {
                'date': date,
                'results': [],
                'stats': {'total_selected': 0, 'success_3d_5pct': 0, 'success_5d_10pct': 0, 'errors': 1},
                'selected_count': 0,
                'success': False
            }

    def run_multithreaded_selection(self, dates: List[pd.Timestamp]):
        """多线程运行选股"""
        global _interrupted

        threading_settings = self.config.get('threading_settings', {})
        max_workers = threading_settings.get('max_workers', 4)
        timeout = threading_settings.get('thread_timeout', 300)

        logger.info(f"🔄 启动多线程选股，线程数: {max_workers}")
        self.log.info(f"启动多线程选股，最大工作线程数: {max_workers}")

        # 线程安全的结果收集
        thread_results = {}  # 每个线程的私有结果
        results_lock = threading.Lock()

        # 统计信息
        thread_stats = {
            'total_selected': 0,
            'success_3d_5pct': 0,
            'success_5d_10pct': 0,
            'errors': 0,
            'completed_dates': 0
        }
        stats_lock = threading.Lock()

        # 设置中断处理
        signal.signal(signal.SIGINT, signal_handler)

        try:
            # 使用线程池执行
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_date = {
                    executor.submit(self.run_selection_for_date_thread_safe, date): date
                    for date in dates
                }

                # 使用tqdm显示进度
                with tqdm(total=len(dates), desc="多线程选股进度", unit="日") as pbar:
                    for future in as_completed(future_to_date, timeout=timeout):
                        # 检查中断标志
                        if _interrupted:
                            logger.info("🛑 检测到中断信号，正在取消剩余任务...")
                            # 取消所有未完成的任务
                            for f in future_to_date:
                                if not f.done():
                                    f.cancel()
                            break

                        date = future_to_date[future]
                        thread_id = threading.current_thread().ident

                        try:
                            result = future.result()

                            # 线程安全地保存结果
                            with results_lock:
                                if thread_id not in thread_results:
                                    thread_results[thread_id] = []
                                thread_results[thread_id].append(result)

                            # 线程安全地更新统计
                            with stats_lock:
                                if result['success']:
                                    thread_stats['total_selected'] += result['stats']['total_selected']
                                    thread_stats['success_3d_5pct'] += result['stats']['success_3d_5pct']
                                    thread_stats['success_5d_10pct'] += result['stats']['success_5d_10pct']
                                else:
                                    thread_stats['errors'] += 1

                                thread_stats['completed_dates'] += 1

                                # 更新进度条
                                pbar.update(1)
                                pbar.set_postfix({
                                    '已选股票': thread_stats['total_selected'],
                                    '3日成功': thread_stats['success_3d_5pct'],
                                    '5日成功': thread_stats['success_5d_10pct']
                                })

                            # 不再每批次打印统计，只在完成后打印

                        except Exception as e:
                            logger.error(f"❌ 处理日期 {date} 时出错: {e}")
                            with stats_lock:
                                thread_stats['errors'] += 1
                                thread_stats['completed_dates'] += 1
                                pbar.update(1)
                                pbar.set_postfix({
                                    '已选股票': thread_stats['total_selected'],
                                    '3日成功': thread_stats['success_3d_5pct'],
                                    '5日成功': thread_stats['success_5d_10pct']
                                })

        except KeyboardInterrupt:
            logger.info("🛑 接收到键盘中断信号")
            _interrupted = True

        # 合并所有线程的结果
        logger.info("📊 合并线程结果...")
        all_results = []
        for thread_id, results in thread_results.items():
            logger.info(f"线程 {thread_id}: 处理了 {len(results)} 个日期")
            all_results.extend(results)

            # 合并到主结果列表
            for result in results:
                if result['success']:
                    self.results.extend(result['results'])

        # 更新主统计
        self.statistics['total_selected'] = thread_stats['total_selected']
        self.statistics['success_3d_5pct'] = thread_stats['success_3d_5pct']
        self.statistics['success_5d_10pct'] = thread_stats['success_5d_10pct']
        self.statistics['errors'] = thread_stats['errors']
        self.statistics['total_processed'] = thread_stats['completed_dates']

        if _interrupted:
            logger.warning(f"⚠️ 选股被中断，已完成 {thread_stats['completed_dates']}/{len(dates)} 个日期")
        else:
            logger.info(f"✅ 多线程选股完成，处理了 {thread_stats['completed_dates']} 个日期")

        return all_results

    def update_statistics(self, processed_dates: int):
        """更新统计数据"""
        self.statistics['total_processed'] = processed_dates
    
    def print_statistics(self, batch_num: int = None):
        """打印统计结果"""
        total_processed = self.statistics['total_processed']
        total_selected = self.statistics['total_selected']
        success_3d = self.statistics['success_3d_5pct']
        success_5d = self.statistics['success_5d_10pct']
        errors = self.statistics['errors']
        
        if batch_num:
            print(f"\n📊 第 {batch_num} 批统计结果 (处理了 {total_processed} 个日期)")
        else:
            print(f"\n📊 最终统计结果 (总计 {total_processed} 个日期)")
        
        print("-" * 70)
        print(f"处理日期数: {total_processed}")
        print(f"选中股票数: {total_selected}")
        print(f"平均每日选股: {total_selected/total_processed:.1f} 只" if total_processed > 0 else "平均每日选股: 0 只")
        print(f"3日涨幅>5%: {success_3d} 个 ({success_3d/total_selected*100:.1f}%)" if total_selected > 0 else "3日涨幅>5%: 0 个")
        print(f"5日涨幅>10%: {success_5d} 个 ({success_5d/total_selected*100:.1f}%)" if total_selected > 0 else "5日涨幅>10%: 0 个")
        print(f"错误数量: {errors}")
        print("-" * 70)
    
    def save_results(self):
        """保存结果到文件"""
        try:
            # 创建输出目录
            output_dir = self.config.get('output_settings', {}).get('output_dir', './results')
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存详细结果
            results_file = os.path.join(output_dir, f"jz_volume_shrink_results_{timestamp}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'algorithm': 'JZVolumeShrinkSelector',
                    'config': self.config,
                    'statistics': self.statistics,
                    'results': self.results,
                    'generated_at': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
            
            # 保存CSV格式的结果
            if self.results:
                csv_file = os.path.join(output_dir, f"jz_volume_shrink_results_{timestamp}.csv")
                df = pd.DataFrame(self.results)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                logger.info(f"✅ CSV结果已保存: {csv_file}")
            
            # 保存统计摘要
            summary_file = os.path.join(output_dir, f"jz_volume_shrink_summary_{timestamp}.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("JZVolumeShrinkSelector 极致缩量算法统计摘要\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("算法参数:\n")
                params = self.config.get('algorithm_params', {})
                for key, value in params.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")
                
                f.write("统计结果:\n")
                total_processed = self.statistics['total_processed']
                total_selected = self.statistics['total_selected']
                f.write(f"  处理日期数: {total_processed}\n")
                f.write(f"  选中股票数: {total_selected}\n")
                f.write(f"  平均每日选股: {total_selected/total_processed:.1f} 只\n" if total_processed > 0 else "  平均每日选股: 0 只\n")
                f.write(f"  3日涨幅>5%: {self.statistics['success_3d_5pct']} 个 ({self.statistics['success_3d_5pct']/total_selected*100:.1f}%)\n" if total_selected > 0 else "  3日涨幅>5%: 0 个\n")
                f.write(f"  5日涨幅>10%: {self.statistics['success_5d_10pct']} 个 ({self.statistics['success_5d_10pct']/total_selected*100:.1f}%)\n" if total_selected > 0 else "  5日涨幅>10%: 0 个\n")
                f.write(f"  错误数量: {self.statistics['errors']}\n")
            
            logger.info(f"✅ 结果已保存:")
            logger.info(f"   详细结果: {results_file}")
            logger.info(f"   统计摘要: {summary_file}")
            
            return results_file
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            return None
    
    def run_analysis(self):
        """运行分析"""
        global _interrupted
        logger.info("🚀 开始 JZVolumeShrinkSelector 极致缩量算法分析")
        
        # 1. 加载配置
        if not self.load_config():
            return False

        # 2. 设置日志
        self.setup_logging()
        
        # 2. 加载股票数据
        if not self.load_stock_data():
            return False
        
        # 3. 初始化选股器
        if not self.initialize_selector():
            return False
        
        try:
            # 4. 获取日期范围
            date_range = self.config.get('data_settings', {}).get('date_range', {})
            start_date = pd.to_datetime(date_range.get('start_date', '2025-01-01'))
            end_date = pd.to_datetime(date_range.get('end_date', '2025-01-31'))
            
            # 5. 生成工作日列表
            dates = []
            current_date = start_date
            while current_date <= end_date:
                # 只选择工作日
                if current_date.weekday() < 5:  # 0-4 是周一到周五
                    dates.append(current_date)
                current_date += timedelta(days=1)
            
            logger.info(f"📅 分析日期范围: {start_date.date()} 到 {end_date.date()}")
            logger.info(f"📊 总计需要处理 {len(dates)} 个交易日")

            # 6. 选择执行方式：多线程或单线程
            threading_settings = self.config.get('threading_settings', {})
            enable_threading = threading_settings.get('enable_threading', True)

            if enable_threading and len(dates) > 1:
                logger.info("🚀 使用多线程模式执行选股")
                all_results = self.run_multithreaded_selection(dates)
            else:
                logger.info("🔄 使用单线程模式执行选股")

                try:
                    # 单线程逐日运行选股
                    for i, date in enumerate(tqdm(dates, desc="单线程选股进度", unit="日")):
                        # 检查中断标志
                        if _interrupted:
                            logger.info(f"🛑 检测到中断信号，已完成 {i}/{len(dates)} 个日期的处理")
                            break

                        result = self.run_selection_for_date(date)

                        # 处理结果
                        if result and 'results' in result:
                            self.results.extend(result['results'])
                            self.statistics['total_selected'] += result['stats']['total_selected']
                            self.statistics['success_3d_5pct'] += result['stats']['success_3d_5pct']
                            self.statistics['success_5d_10pct'] += result['stats']['success_5d_10pct']
                            self.statistics['errors'] += result['stats']['errors']

                        # 更新统计
                        self.update_statistics(i + 1)

                        # 不再每批次打印统计，只在完成后打印

                except KeyboardInterrupt:
                    logger.info("🛑 接收到键盘中断信号")
                    _interrupted = True
            
            # 7. 打印最终统计
            self.print_statistics()

            # 8. 保存最终结果
            result_file = self.save_results()

            if _interrupted:
                logger.info("⚠️ JZVolumeShrinkSelector 分析被中断，但已保存部分结果!")
            else:
                logger.info("🎉 JZVolumeShrinkSelector 分析完成!")
            return result_file
            
        except Exception as e:
            logger.error(f"❌ 分析过程中出现错误: {e}")
            return False

def main():
    """主函数"""
    runner = JZVolumeShrinkRunner()
    
    try:
        result_file = runner.run_analysis()
        
        if result_file:
            print(f"\n🎉 JZVolumeShrinkSelector 分析完成! 结果已保存到: {result_file}")
            print(f"\n💡 算法说明:")
            print(f"1. 使用真实的 JZVolumeShrinkSelector 极致缩量算法")
            print(f"2. 基于真实股票数据进行选股分析")
            print(f"3. 统计3日涨幅>5%和5日涨幅>10%的成功率")
            print(f"4. 每1000个数据点打印统计结果")
        else:
            print(f"\n❌ 分析失败，请检查日志")
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
