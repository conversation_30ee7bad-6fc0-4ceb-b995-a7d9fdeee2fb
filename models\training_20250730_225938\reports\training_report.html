
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>3日成功选股预测模型训练报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
                .section { margin: 20px 0; }
                .metric { background-color: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .best { background-color: #d4edda; color: #155724; font-weight: bold; }
                .good { background-color: #d1ecf1; color: #0c5460; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎯 3日成功选股预测模型训练报告</h1>
                <p><strong>训练时间:</strong> 2025-07-30 22:59:42</p>
                <p><strong>训练数据:</strong> 选股分析结果_20250730_225530.xlsx</p>
                <p><strong>验证数据:</strong> 选股分析结果_20250730_225041.xlsx</p>
                <p><strong>目标变量:</strong> 3日成功选股</p>
            </div>

            <div class="section">
                <h2>📊 成功判断标准</h2>
                <div class="metric">
                    <strong>3日成功选股标准:</strong><br>
                    3日最大涨幅≥5%且最大跌幅>-3%<br>
                    <em>注: 直接使用数据集中已有的"3日成功选股"列</em>
                </div>
            </div>

            <div class="section">
                <h2>🏆 最佳模型表现</h2>
                <div class="metric best">
                    <strong>最佳模型:</strong> XGBoost<br>
                    <strong>验证准确率:</strong> 0.9352<br>
                    <strong>AUC得分:</strong> 0.8402<br>
                    <strong>F1得分:</strong> 0.8494
                </div>
            </div>

            <div class="section">
                <h2>📈 所有模型性能对比</h2>
                <table>
                    <tr>
                        <th>模型</th>
                        <th>训练准确率</th>
                        <th>验证准确率</th>
                        <th>AUC得分</th>
                        <th>F1得分</th>
                        <th>交叉验证</th>
                    </tr>
        
                    <tr class="best">
                        <td>XGBoost</td>
                        <td>0.9971</td>
                        <td>0.9352</td>
                        <td>0.8402</td>
                        <td>0.8494</td>
                        <td>0.8530 ± 0.0129</td>
                    </tr>
            
                    <tr class="good">
                        <td>LightGBM</td>
                        <td>0.9949</td>
                        <td>0.9292</td>
                        <td>0.8435</td>
                        <td>0.8697</td>
                        <td>0.8436 ± 0.0178</td>
                    </tr>
            
                </table>
            </div>

            <div class="section">
                <h2>🎯 重要特征</h2>
                <p>系统选择了以下重要特征进行预测:</p>
                <ul>
        <li>A点实体涨跌幅</li><li>A点价格振幅</li><li>B点成交量</li><li>B点实体涨跌幅</li><li>B点价格振幅</li><li>C点最低</li><li>C点成交量</li><li>C点实体涨跌幅</li><li>C点价格振幅</li><li>D点成交量</li><li>D点实体涨跌幅</li><li>D点价格振幅</li><li>E点成交量</li><li>E点实体涨跌幅</li><li>E点价格振幅</li><li>A-B涨幅</li><li>A-B天数</li><li>B-C跌幅</li><li>B-C天数</li><li>C-D涨幅</li><li>C-D天数</li><li>D-E涨幅</li><li>D-E天数</li><li>D点成交量/C-D均量</li><li>D点上影线涨幅</li><li>D点上影线/实体</li><li>E点成交量/C-D均量</li><li>E点成交量/D点成交量</li><li>E点J值</li><li>E点J值相对D点J值涨幅</li><li>E点相对D点收盘价涨幅</li>
                </ul>
            </div>

            <div class="section">
                <h2>📁 输出文件</h2>
                <ul>
                    <li><strong>模型文件:</strong> models/ 目录下的 .pkl 文件</li>
                    <li><strong>可视化图表:</strong> visualizations/training_results.png</li>
                    <li><strong>性能数据:</strong> reports/model_performance.json</li>
                    <li><strong>训练配置:</strong> config.json</li>
                </ul>
            </div>

            <div class="section">
                <h2>💡 使用建议</h2>
                <ul>
                    <li>使用最佳模型 <strong>XGBoost</strong> 进行预测</li>
                    <li>关注验证准确率 <strong>93.5%</strong> 的表现</li>
                    <li>重点关注预测概率 > 0.7 的股票</li>
                    <li>结合其他分析方法进行综合判断</li>
                </ul>
            </div>

            <div class="section">
                <h2>🚀 下一步操作</h2>
                <p>使用以下命令进行预测:</p>
                <code>python predict_3d_success.py --input_file 新数据.xlsx --training_folder models/training_20250730_225938</code>
            </div>
        </body>
        </html>
        