
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双损失函数深度学习股票预测报告</title>

    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        .header h1 {
            color: #2E7D32;
            margin-bottom: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .model-comparison {
            margin: 30px 0;
        }
        .model-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .model-card.best {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
        }
        .model-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 15px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .metric {
            text-align: center;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .best-model {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .training-info {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #2196F3;
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 双损失函数深度学习股票预测报告</h1>
            <p>训练时间: 2025年08月03日 02:05:26</p>
        </div>

        <div class="training-info">
            <h3>📋 训练信息</h3>
            <p><strong>基础成功率:</strong> 13.11%</p>
            <p><strong>目标预测比例:</strong> 8.0%</p>
            <p><strong>数据分割:</strong> 按股票分离，确保无数据泄露</p>
            <p><strong>损失函数:</strong> 精确率损失 + 比例损失</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>🏆 最佳模型</h3>
                <div class="value">PrecisionMLP</div>
                <p>综合评分: 0.250</p>
            </div>
            <div class="summary-card">
                <h3>📈 最高精确率</h3>
                <div class="value">26.1%</div>
                <p>vs 基础成功率 13.1%</p>
            </div>
            <div class="summary-card">
                <h3>🎯 预测比例</h3>
                <div class="value">9.1%</div>
                <p>目标: 8.0%</p>
            </div>
            <div class="summary-card">
                <h3>📊 预测数量</h3>
                <div class="value">23</div>
                <p>个股票</p>
            </div>
        </div>

        <div class="model-comparison">
            <h2>🤖 模型详细对比</h2>

            <div class="model-card best">
                <div class="model-name">
                    PrecisionMLP 🏆
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-label">精确率</div>
                        <div class="metric-value">26.09%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">召回率</div>
                        <div class="metric-value">22.22%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">F1分数</div>
                        <div class="metric-value">0.240</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">AUC</div>
                        <div class="metric-value">0.664</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测数量</div>
                        <div class="metric-value">23</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测比例</div>
                        <div class="metric-value">9.06%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">比例偏差</div>
                        <div class="metric-value">0.011</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">综合评分</div>
                        <div class="metric-value">0.250</div>
                    </div>
                </div>
            </div>

            <div class="model-card">
                <div class="model-name">
                    ResidualNet 
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-label">精确率</div>
                        <div class="metric-value">22.58%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">召回率</div>
                        <div class="metric-value">25.93%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">F1分数</div>
                        <div class="metric-value">0.241</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">AUC</div>
                        <div class="metric-value">0.629</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测数量</div>
                        <div class="metric-value">31</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测比例</div>
                        <div class="metric-value">12.20%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">比例偏差</div>
                        <div class="metric-value">0.042</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">综合评分</div>
                        <div class="metric-value">0.184</div>
                    </div>
                </div>
            </div>

            <div class="model-card">
                <div class="model-name">
                    AttentionNet 
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-label">精确率</div>
                        <div class="metric-value">19.05%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">召回率</div>
                        <div class="metric-value">14.81%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">F1分数</div>
                        <div class="metric-value">0.167</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">AUC</div>
                        <div class="metric-value">0.621</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测数量</div>
                        <div class="metric-value">21</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测比例</div>
                        <div class="metric-value">8.27%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">比例偏差</div>
                        <div class="metric-value">0.003</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">综合评分</div>
                        <div class="metric-value">0.188</div>
                    </div>
                </div>
            </div>

            <div class="model-card">
                <div class="model-name">
                    EnsembleModel 
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-label">精确率</div>
                        <div class="metric-value">20.69%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">召回率</div>
                        <div class="metric-value">22.22%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">F1分数</div>
                        <div class="metric-value">0.214</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">AUC</div>
                        <div class="metric-value">0.622</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测数量</div>
                        <div class="metric-value">29</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">预测比例</div>
                        <div class="metric-value">11.42%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">比例偏差</div>
                        <div class="metric-value">0.034</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">综合评分</div>
                        <div class="metric-value">0.173</div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</body>
</html>
