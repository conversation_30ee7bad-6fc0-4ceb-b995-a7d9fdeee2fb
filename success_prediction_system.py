#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
成功选股预测系统
基于47个技术指标特征预测选股成功率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.feature_selection import SelectKBest, chi2, mutual_info_classif
import joblib
import warnings
from datetime import datetime
import os

warnings.filterwarnings('ignore')

class SuccessPredictionSystem:
    def __init__(self):
        """初始化成功选股预测系统"""
        self.models = {}
        self.scaler = None
        self.feature_selector = None
        self.label_encoder = None
        self.feature_names = None
        self.results = {}
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 定义输入特征
        self.input_features = [
            "A点开盘", "A点收盘", "A点最高", "A点最低", "A点成交量", "A点实体涨跌幅", "A点价格振幅",
            "B点开盘", "B点收盘", "B点最高", "B点最低", "B点成交量", "B点实体涨跌幅", "B点价格振幅", 
            "C点开盘", "C点收盘", "C点最高", "C点最低", "C点成交量", "C点实体涨跌幅", "C点价格振幅",
            "D点开盘", "D点收盘", "D点最高", "D点最低", "D点成交量", "D点实体涨跌幅", "D点价格振幅",
            "E点开盘", "E点收盘", "E点最高", "E点最低", "E点成交量", "E点实体涨跌幅", "E点价格振幅",
            "A-B涨幅", "A-B天数", "B-C跌幅", "B-C天数", "C-D涨幅", "C-D天数", "D-E涨幅", "D-E天数",
            "D点成交量/C-D均量", "D点上影线涨幅", "D点上影线/实体", "E点成交量/C-D均量", "E点成交量/D点成交量"
        ]
        
        self.target_feature = "3日成功选股"
        # self.target_feature = "5日成功选股"
        
        # 创建输出目录
        os.makedirs('success_prediction_results', exist_ok=True)
        os.makedirs('success_prediction_models', exist_ok=True)
    
    def load_and_preprocess_data(self, file_path, is_training=True):
        """加载和预处理数据"""
        print(f"📁 加载数据: {os.path.basename(file_path)}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        print(f"   📊 原始数据形状: {df.shape}")
        
        # 提取特征
        missing_features = [f for f in self.input_features if f not in df.columns]
        if missing_features:
            print(f"   ⚠️  缺失特征: {missing_features}")
            # 用0填充缺失特征
            for feature in missing_features:
                df[feature] = 0
        
        X = df[self.input_features].copy()
        
        # 数据清洗
        X = self._clean_data(X)
        
        if is_training:
            if self.target_feature not in df.columns:
                raise ValueError(f"目标变量 '{self.target_feature}' 不存在于数据中")
            
            y = df[self.target_feature].copy()
            
            # 移除包含缺失值的行
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X = X[mask]
            y = y[mask]
            df_clean = df[mask]
            
            print(f"   ✅ 清洗后数据形状: X={X.shape}, y={y.shape}")
            print(f"   📊 目标变量分布: {y.value_counts().to_dict()}")
            
            return X, y, df_clean
        else:
            # 移除包含缺失值的行
            mask = ~X.isnull().any(axis=1)
            X = X[mask]
            df_clean = df[mask]
            
            print(f"   ✅ 清洗后数据形状: X={X.shape}")
            return X, df_clean
    
    def _clean_data(self, X):
        """数据清洗"""
        # 处理无穷大值
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # 数值类型转换
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    # 处理百分比格式
                    if X[col].astype(str).str.contains('%').any():
                        X[col] = X[col].astype(str).str.replace('%', '').astype(float) / 100
                    else:
                        X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    X[col] = 0
        
        # 填充缺失值
        X = X.fillna(X.median())
        
        return X
    
    def statistical_analysis(self, X, y):
        """统计学分析"""
        print("\n📊 统计学分析")
        print("=" * 60)
        
        # 编码目标变量
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 1. 相关性分析
        print("1. 特征与目标变量的相关性分析:")
        correlations = []
        
        for feature in X.columns:
            try:
                corr = np.corrcoef(X[feature], y_encoded)[0, 1]
                if not np.isnan(corr):
                    correlations.append((feature, abs(corr)))
            except:
                correlations.append((feature, 0))
        
        # 排序并显示前10个最相关的特征
        correlations.sort(key=lambda x: x[1], reverse=True)
        print("   Top 10 最相关特征:")
        for i, (feature, corr) in enumerate(correlations[:10], 1):
            print(f"   {i:2d}. {feature:<25} 相关系数: {corr:.4f}")
        
        # 2. 特征重要性分析（互信息）
        print("\n2. 互信息特征重要性分析:")
        mi_scores = mutual_info_classif(X, y_encoded, random_state=42)
        mi_ranking = [(X.columns[i], score) for i, score in enumerate(mi_scores)]
        mi_ranking.sort(key=lambda x: x[1], reverse=True)
        
        print("   Top 10 互信息重要特征:")
        for i, (feature, score) in enumerate(mi_ranking[:10], 1):
            print(f"   {i:2d}. {feature:<25} 互信息得分: {score:.4f}")
        
        # 3. 成功/失败组的特征统计对比
        print("\n3. 成功/失败组特征对比:")
        success_mask = y == '成功'
        failure_mask = y == '失败'
        
        print("   关键特征统计对比:")
        key_features = [feat[0] for feat in correlations[:5]]
        
        for feature in key_features:
            success_mean = X.loc[success_mask, feature].mean()
            failure_mean = X.loc[failure_mask, feature].mean()
            diff_pct = ((success_mean - failure_mean) / failure_mean * 100) if failure_mean != 0 else 0
            
            print(f"   {feature:<25}")
            print(f"     成功组均值: {success_mean:8.4f}")
            print(f"     失败组均值: {failure_mean:8.4f}")
            print(f"     差异百分比: {diff_pct:+7.1f}%")
            print()
        
        return correlations, mi_ranking
    
    def train_models(self, X, y):
        """训练多个机器学习模型"""
        print("\n🚀 训练机器学习模型")
        print("=" * 60)
        
        # 编码目标变量
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 数据标准化
        if self.scaler is None:
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
        else:
            X_scaled = self.scaler.transform(X)
        
        # 特征选择
        if self.feature_selector is None:
            self.feature_selector = SelectKBest(score_func=mutual_info_classif, k=20)
            X_selected = self.feature_selector.fit_transform(X_scaled, y_encoded)
            selected_features = X.columns[self.feature_selector.get_support()]
            self.feature_names = selected_features.tolist()
            print(f"   🎯 选择了 {len(self.feature_names)} 个重要特征")
        else:
            X_selected = self.feature_selector.transform(X_scaled)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_selected, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        print(f"   📊 训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
        # 定义模型
        models_config = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000),
            'SVM': SVC(random_state=42, class_weight='balanced', probability=True)
        }
        
        # 训练和评估模型
        for name, model in models_config.items():
            print(f"\n   🔧 训练 {name}...")
            
            # 训练模型
            model.fit(X_train, y_train)
            self.models[name] = model
            
            # 预测
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            # 评估
            train_acc = (y_pred_train == y_train).mean()
            test_acc = (y_pred_test == y_test).mean()
            auc_score = roc_auc_score(y_test, y_pred_proba)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_selected, y_encoded, cv=5, scoring='accuracy')
            
            self.results[name] = {
                'train_accuracy': train_acc,
                'test_accuracy': test_acc,
                'auc_score': auc_score,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'y_test': y_test,
                'y_pred': y_pred_test,
                'y_pred_proba': y_pred_proba
            }
            
            print(f"      训练准确率: {train_acc:.4f}")
            print(f"      测试准确率: {test_acc:.4f}")
            print(f"      AUC得分: {auc_score:.4f}")
            print(f"      交叉验证: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        return X_train, X_test, y_train, y_test

    def visualize_results(self):
        """可视化训练和预测结果"""
        print("\n📈 生成可视化图表")
        print("=" * 60)

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('成功选股预测模型分析结果', fontsize=16, fontweight='bold')

        # 1. 模型准确率对比
        ax1 = axes[0, 0]
        models = list(self.results.keys())
        train_accs = [self.results[m]['train_accuracy'] for m in models]
        test_accs = [self.results[m]['test_accuracy'] for m in models]

        x = np.arange(len(models))
        width = 0.35

        bars1 = ax1.bar(x - width/2, train_accs, width, label='训练准确率', alpha=0.8, color='skyblue')
        bars2 = ax1.bar(x + width/2, test_accs, width, label='测试准确率', alpha=0.8, color='lightcoral')

        ax1.set_xlabel('模型')
        ax1.set_ylabel('准确率')
        ax1.set_title('模型准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)

        # 2. AUC得分对比
        ax2 = axes[0, 1]
        auc_scores = [self.results[m]['auc_score'] for m in models]
        bars = ax2.bar(models, auc_scores, alpha=0.8, color='lightgreen')
        ax2.set_xlabel('模型')
        ax2.set_ylabel('AUC得分')
        ax2.set_title('AUC得分对比')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)

        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # 3. 交叉验证结果
        ax3 = axes[0, 2]
        cv_means = [self.results[m]['cv_mean'] for m in models]
        cv_stds = [self.results[m]['cv_std'] for m in models]

        bars = ax3.bar(models, cv_means, yerr=cv_stds, alpha=0.8, color='gold', capsize=5)
        ax3.set_xlabel('模型')
        ax3.set_ylabel('交叉验证准确率')
        ax3.set_title('交叉验证结果')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)

        for bar, mean in zip(bars, cv_means):
            ax3.text(bar.get_x() + bar.get_width()/2., mean,
                    f'{mean:.3f}', ha='center', va='bottom', fontsize=9)

        # 4. ROC曲线
        ax4 = axes[1, 0]
        colors = ['blue', 'red', 'green', 'orange']

        for i, model_name in enumerate(models):
            y_test = self.results[model_name]['y_test']
            y_pred_proba = self.results[model_name]['y_pred_proba']

            fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
            auc = self.results[model_name]['auc_score']

            ax4.plot(fpr, tpr, color=colors[i], lw=2,
                    label=f'{model_name} (AUC = {auc:.3f})')

        ax4.plot([0, 1], [0, 1], color='gray', lw=2, linestyle='--', alpha=0.5)
        ax4.set_xlim([0.0, 1.0])
        ax4.set_ylim([0.0, 1.05])
        ax4.set_xlabel('假正率')
        ax4.set_ylabel('真正率')
        ax4.set_title('ROC曲线')
        ax4.legend(loc="lower right")
        ax4.grid(True, alpha=0.3)

        # 5. 混淆矩阵（最佳模型）
        best_model = max(models, key=lambda m: self.results[m]['test_accuracy'])
        ax5 = axes[1, 1]

        y_test = self.results[best_model]['y_test']
        y_pred = self.results[best_model]['y_pred']
        cm = confusion_matrix(y_test, y_pred)

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax5,
                   xticklabels=['失败', '成功'], yticklabels=['失败', '成功'])
        ax5.set_xlabel('预测标签')
        ax5.set_ylabel('真实标签')
        ax5.set_title(f'混淆矩阵 ({best_model})')

        # 6. 特征重要性（随机森林）
        ax6 = axes[1, 2]
        if 'RandomForest' in self.models:
            rf_model = self.models['RandomForest']
            importances = rf_model.feature_importances_

            # 获取前10个重要特征
            indices = np.argsort(importances)[::-1][:10]

            ax6.barh(range(10), importances[indices], alpha=0.8, color='purple')
            ax6.set_yticks(range(10))
            ax6.set_yticklabels([self.feature_names[i] for i in indices])
            ax6.set_xlabel('重要性')
            ax6.set_title('特征重要性 (随机森林)')
            ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_path = f"success_prediction_results/模型分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"   ✅ 图表已保存: {chart_path}")
        return chart_path

    def predict(self, X_pred, original_df):
        """进行预测"""
        print("\n🔮 进行预测")
        print("=" * 60)

        # 预处理
        X_pred_scaled = self.scaler.transform(X_pred)
        X_pred_selected = self.feature_selector.transform(X_pred_scaled)

        print(f"   📊 预测数据形状: {X_pred_selected.shape}")

        # 使用所有模型进行预测
        predictions = {}
        probabilities = {}

        for model_name, model in self.models.items():
            pred = model.predict(X_pred_selected)
            pred_proba = model.predict_proba(X_pred_selected)

            # 解码预测结果
            pred_labels = self.label_encoder.inverse_transform(pred)

            predictions[model_name] = pred_labels
            probabilities[model_name] = pred_proba[:, 1]  # 成功的概率

            print(f"   ✅ {model_name} 预测完成")

        # 集成预测（投票）
        ensemble_pred = []
        ensemble_proba = []

        for i in range(len(X_pred_selected)):
            # 投票决定最终预测
            votes = [predictions[model][i] for model in self.models.keys()]
            success_votes = votes.count('成功')
            final_pred = '成功' if success_votes > len(votes) / 2 else '失败'
            ensemble_pred.append(final_pred)

            # 平均概率
            avg_proba = np.mean([probabilities[model][i] for model in self.models.keys()])
            ensemble_proba.append(avg_proba)

        # 格式化结果
        results_df = original_df.copy()
        results_df['集成预测'] = ensemble_pred
        results_df['成功概率'] = ensemble_proba

        # 添加各模型的预测结果
        for model_name in self.models.keys():
            results_df[f'{model_name}_预测'] = predictions[model_name]
            results_df[f'{model_name}_概率'] = probabilities[model_name]

        # 统计预测结果
        pred_counts = pd.Series(ensemble_pred).value_counts()
        print(f"\n   📊 预测结果统计:")
        for label, count in pred_counts.items():
            percentage = count / len(ensemble_pred) * 100
            print(f"      {label}: {count}个 ({percentage:.1f}%)")

        # 验证预测准确率（如果预测数据集有目标列）
        if self.target_feature in original_df.columns:
            actual_values = original_df[self.target_feature]
            # 检查是否有非空值
            non_null_mask = actual_values.notna() & (actual_values != '') & (actual_values != '未知')

            if non_null_mask.sum() > 0:
                print(f"\n   🎯 预测准确率验证:")
                print(f"      可验证样本数: {non_null_mask.sum()}")

                # 提取可验证的样本
                actual_valid = actual_values[non_null_mask]
                predicted_valid = pd.Series(ensemble_pred)[non_null_mask]
                proba_valid = pd.Series(ensemble_proba)[non_null_mask]

                # 计算准确率
                correct_predictions = (actual_valid == predicted_valid).sum()
                total_predictions = len(actual_valid)
                accuracy = correct_predictions / total_predictions

                print(f"      总体准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")

                # 分别计算成功和失败的准确率
                success_mask = actual_valid == '成功'
                failure_mask = actual_valid == '失败'

                if success_mask.sum() > 0:
                    success_correct = (predicted_valid[success_mask] == '成功').sum()
                    success_total = success_mask.sum()
                    success_accuracy = success_correct / success_total
                    print(f"      成功预测准确率: {success_accuracy:.4f} ({success_correct}/{success_total})")

                if failure_mask.sum() > 0:
                    failure_correct = (predicted_valid[failure_mask] == '失败').sum()
                    failure_total = failure_mask.sum()
                    failure_accuracy = failure_correct / failure_total
                    print(f"      失败预测准确率: {failure_accuracy:.4f} ({failure_correct}/{failure_total})")

                # 计算预测为成功且实际也为成功的概率
                predicted_success_mask = predicted_valid == '成功'
                if predicted_success_mask.sum() > 0:
                    actual_success_in_predicted = (actual_valid[predicted_success_mask] == '成功').sum()
                    predicted_success_total = predicted_success_mask.sum()
                    success_precision = actual_success_in_predicted / predicted_success_total
                    print(f"      🎯 预测成功的精确率: {success_precision:.4f} ({actual_success_in_predicted}/{predicted_success_total})")
                    print(f"         (预测为成功且实际也为成功的概率)")

                # 高置信度预测分析
                high_confidence_mask = proba_valid > 0.7
                if high_confidence_mask.sum() > 0:
                    high_conf_correct = (actual_valid[high_confidence_mask] == predicted_valid[high_confidence_mask]).sum()
                    high_conf_total = high_confidence_mask.sum()
                    high_conf_accuracy = high_conf_correct / high_conf_total
                    print(f"      高置信度(>70%)准确率: {high_conf_accuracy:.4f} ({high_conf_correct}/{high_conf_total})")

                # 添加验证结果到DataFrame
                results_df['实际结果'] = original_df[self.target_feature]
                results_df['预测正确'] = (results_df['实际结果'] == results_df['集成预测']).astype(str)
                results_df['预测正确'][~non_null_mask] = '无法验证'

        return results_df

    def save_models_and_results(self, train_file, pred_file=None):
        """保存模型和结果"""
        print("\n💾 保存模型和结果")
        print("=" * 60)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存模型
        for model_name, model in self.models.items():
            model_path = f"success_prediction_models/{model_name}_{timestamp}.pkl"
            joblib.dump(model, model_path)
            print(f"   ✅ 模型已保存: {model_path}")

        # 保存预处理器
        scaler_path = f"success_prediction_models/scaler_{timestamp}.pkl"
        selector_path = f"success_prediction_models/feature_selector_{timestamp}.pkl"
        encoder_path = f"success_prediction_models/label_encoder_{timestamp}.pkl"

        joblib.dump(self.scaler, scaler_path)
        joblib.dump(self.feature_selector, selector_path)
        joblib.dump(self.label_encoder, encoder_path)

        print(f"   ✅ 预处理器已保存")

        # 保存训练结果
        results_summary = {
            'timestamp': timestamp,
            'train_file': train_file,
            'pred_file': pred_file,
            'input_features': self.input_features,
            'selected_features': self.feature_names,
            'target_feature': self.target_feature,
            'model_performance': self.results
        }

        results_path = f"success_prediction_results/训练结果_{timestamp}.json"
        import json
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2, default=str)

        print(f"   ✅ 训练结果已保存: {results_path}")

        return timestamp

    def generate_report(self, results_df, train_file, pred_file):
        """生成分析报告"""
        print("\n📋 生成分析报告")
        print("=" * 60)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存预测结果
        pred_results_path = f"success_prediction_results/预测结果_{timestamp}.xlsx"
        results_df.to_excel(pred_results_path, index=False)
        print(f"   ✅ 预测结果已保存: {pred_results_path}")

        # 生成HTML报告
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>成功选股预测分析报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metric {{ background-color: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .success {{ color: #28a745; font-weight: bold; }}
                .failure {{ color: #dc3545; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎯 成功选股预测分析报告</h1>
                <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>训练数据:</strong> {os.path.basename(train_file)}</p>
                <p><strong>预测数据:</strong> {os.path.basename(pred_file)}</p>
            </div>

            <div class="section">
                <h2>📊 模型性能总结</h2>
        """

        # 添加模型性能表格
        html_report += "<table><tr><th>模型</th><th>训练准确率</th><th>测试准确率</th><th>AUC得分</th><th>交叉验证</th></tr>"

        for model_name, results in self.results.items():
            html_report += f"""
                <tr>
                    <td>{model_name}</td>
                    <td>{results['train_accuracy']:.4f}</td>
                    <td>{results['test_accuracy']:.4f}</td>
                    <td>{results['auc_score']:.4f}</td>
                    <td>{results['cv_mean']:.4f} ± {results['cv_std']:.4f}</td>
                </tr>
            """

        html_report += "</table></div>"

        # 添加预测结果统计
        pred_counts = results_df['集成预测'].value_counts()
        success_count = pred_counts.get('成功', 0)
        failure_count = pred_counts.get('失败', 0)
        total_count = len(results_df)

        html_report += f"""
            <div class="section">
                <h2>🔮 预测结果统计</h2>
                <div class="metric">总预测数量: {total_count}</div>
                <div class="metric">预测成功: <span class="success">{success_count}个 ({success_count/total_count*100:.1f}%)</span></div>
                <div class="metric">预测失败: <span class="failure">{failure_count}个 ({failure_count/total_count*100:.1f}%)</span></div>
            </div>

            <div class="section">
                <h2>🎯 重要特征</h2>
                <p>系统选择了以下{len(self.feature_names)}个重要特征进行预测:</p>
                <ul>
        """

        for feature in self.feature_names:
            html_report += f"<li>{feature}</li>"

        html_report += """
                </ul>
            </div>

            <div class="section">
                <h2>📈 使用建议</h2>
                <ul>
                    <li>重点关注预测为"成功"且成功概率>0.7的股票</li>
                    <li>结合其他分析方法进行综合判断</li>
                    <li>定期重新训练模型以保持预测准确性</li>
                    <li>关注模型的特征重要性，优化选股策略</li>
                </ul>
            </div>
        </body>
        </html>
        """

        # 保存HTML报告
        report_path = f"success_prediction_results/分析报告_{timestamp}.html"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_report)

        print(f"   ✅ HTML报告已保存: {report_path}")

        return pred_results_path, report_path


def main():
    """主函数"""
    print("🎯 成功选股预测系统")
    print("=" * 80)

    # 文件路径
    train_file = r"C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250728_230031.xlsx"
    pred_file = r"C:\Users\<USER>\Documents\Stock\StockAssistant\选股分析结果\选股分析结果_20250728_225651.xlsx"

    print(f"📚 训练数据: {os.path.basename(train_file)}")
    print(f"🔮 预测数据: {os.path.basename(pred_file)}")
    print("=" * 80)

    try:
        # 创建预测系统
        system = SuccessPredictionSystem()

        # 1. 加载和预处理训练数据
        X_train, y_train, train_df = system.load_and_preprocess_data(train_file, is_training=True)

        # 2. 统计学分析
        correlations, mi_ranking = system.statistical_analysis(X_train, y_train)

        # 3. 训练机器学习模型
        X_train_split, X_test_split, y_train_split, y_test_split = system.train_models(X_train, y_train)

        # 4. 可视化结果
        chart_path = system.visualize_results()

        # 5. 加载预测数据
        X_pred, pred_df = system.load_and_preprocess_data(pred_file, is_training=False)

        # 6. 进行预测
        results_df = system.predict(X_pred, pred_df)

        # 7. 保存模型和结果
        timestamp = system.save_models_and_results(train_file, pred_file)

        # 8. 生成报告
        pred_results_path, report_path = system.generate_report(results_df, train_file, pred_file)

        # 9. 打印总结
        print("\n" + "=" * 80)
        print("🎉 分析完成！")
        print("=" * 80)

        print(f"📊 训练数据: {len(X_train)} 条记录")
        print(f"🔮 预测数据: {len(X_pred)} 条记录")
        print(f"🎯 选择特征: {len(system.feature_names)} 个")

        # 显示最佳模型
        best_model = max(system.results.keys(), key=lambda m: system.results[m]['test_accuracy'])
        best_acc = system.results[best_model]['test_accuracy']
        print(f"🏆 最佳模型: {best_model} (准确率: {best_acc:.4f})")

        # 显示预测结果
        pred_counts = results_df['集成预测'].value_counts()
        print(f"📈 预测成功: {pred_counts.get('成功', 0)} 个")
        print(f"📉 预测失败: {pred_counts.get('失败', 0)} 个")

        print(f"\n📁 输出文件:")
        print(f"   📊 可视化图表: {chart_path}")
        print(f"   📋 预测结果: {pred_results_path}")
        print(f"   📄 分析报告: {report_path}")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
