#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JZVolumeShrinkSelector算法实现
使用Selector.py中的真实极致缩量算法进行选股
"""

import json
import time
import random
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import os
import logging
import sys

# 导入真实的选股算法
from Selector import JZVolumeShrinkSelector

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JZVolumeShrinkSelectorRunner:
    """JZVolumeShrinkSelector算法运行器"""

    def __init__(self, config_file: str = "jz_volume_config.json"):
        self.config_file = config_file
        self.config = {}
        self.results = []
        self.selector = None  # JZVolumeShrinkSelector实例
        self.stock_data = {}  # 股票历史数据
        self.statistics = {
            'total_processed': 0,
            'success_3d_5pct': 0,  # 3日涨幅>5%
            'success_5d_10pct': 0, # 5日涨幅>10%
            'total_success': 0,
            'selected_stocks': 0,  # 被算法选中的股票数
            'errors': 0
        }
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            logger.info(f"✅ 配置文件加载成功: {self.config_file}")
            return True
        except FileNotFoundError:
            logger.error(f"❌ 配置文件不存在: {self.config_file}")
            self.create_default_config()
            return False
        except json.JSONDecodeError as e:
            logger.error(f"❌ 配置文件格式错误: {e}")
            return False
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "jz_algorithm_params": {
                "rise_pct": 0.15,  # A点-B点涨幅阈值
                "fall_pct": 0.10,  # B点-C点跌幅阈值
                "n1": 20,          # A点-B点查找天数
                "a_consolidation_days": 5,
                "a_consolidation_range": 0.05,
                "a_downward_min_pct": 0.02,
                "a_downward_max_pct": 0.08,
                "a_downward_range_multiplier": 2.0,
                "d_vol_ratio": 1.2,
                "d_vol_max_ratio": 3.0,
                "upper_shadow_ratio": 1.5,
                "e_vol_vs_cd_avg_ratio": 0.8,
                "e_vs_d_vol_ratio": 0.6,
                "de_max_days": 5,
                "e_yang_threshold": 0.01,
                "shadow_ratio": 1.5,
                "ce_rise_ratio": 0.3,
                "ab_max_days": 30,
                "cd_max_distance_trade_days": 10,
                "d_lookback_trade_days": 5
            },
            "stock_selection": {
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "stock_codes": [
                    "000001", "000002", "000004", "000005", "000006",
                    "000007", "000008", "000009", "000010", "000011",
                    "000012", "000014", "000016", "000017", "000018",
                    "000019", "000020", "000021", "000022", "000023",
                    "600000", "600001", "600004", "600005", "600006",
                    "600007", "600008", "600009", "600010", "600011"
                ]
            },
            "analysis_settings": {
                "success_threshold_3d": 5.0,
                "success_threshold_5d": 10.0,
                "batch_size": 1000,
                "simulation_mode": True  # 模拟模式，使用随机数据
            },
            "output_settings": {
                "save_intermediate": True,
                "output_dir": "./results",
                "include_timestamp": True
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 已创建默认配置文件: {self.config_file}")
        logger.info("请修改配置文件后重新运行")

    def initialize_selector(self):
        """初始化JZVolumeShrinkSelector"""
        try:
            params = self.config.get('jz_algorithm_params', {})
            self.selector = JZVolumeShrinkSelector(**params)
            logger.info("✅ JZVolumeShrinkSelector初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ JZVolumeShrinkSelector初始化失败: {e}")
            return False

    def load_stock_data(self, stock_codes: list, date_range: dict):
        """加载股票历史数据（模拟）"""
        logger.info("📊 加载股票历史数据...")

        start_date = datetime.strptime(date_range.get('start_date', '2025-01-01'), '%Y-%m-%d')
        end_date = datetime.strptime(date_range.get('end_date', '2025-01-31'), '%Y-%m-%d')

        # 生成日期序列
        date_range_list = pd.date_range(start=start_date, end=end_date, freq='D')

        for stock_code in stock_codes:
            # 生成模拟的历史数据
            data_length = len(date_range_list) + 100  # 额外100天历史数据

            # 生成基础价格序列
            base_price = random.uniform(5, 50)
            price_changes = np.random.normal(0, 0.02, data_length)  # 日收益率
            prices = [base_price]

            for change in price_changes:
                new_price = prices[-1] * (1 + change)
                prices.append(max(0.1, new_price))  # 确保价格不为负

            # 生成完整的K线数据
            stock_data = []
            for i, price in enumerate(prices[1:]):
                date = start_date - timedelta(days=100-i) if i < 100 else date_range_list[i-100]

                # 生成开高低收
                open_price = prices[i]
                close_price = price
                high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
                low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)

                # 生成成交量
                volume = random.uniform(100000, 1000000)

                stock_data.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': int(volume)
                })

            self.stock_data[stock_code] = pd.DataFrame(stock_data)
            self.stock_data[stock_code]['date'] = pd.to_datetime(self.stock_data[stock_code]['date'])

        logger.info(f"✅ 已加载 {len(stock_codes)} 只股票的历史数据")
        return True

    def generate_market_data(self, date: str, stock_code: str):
        """生成模拟市场数据"""
        # 模拟D点和E点的数据
        base_price = random.uniform(5, 50)  # 基础价格
        base_volume = random.uniform(100000, 1000000)  # 基础成交量
        
        # D点数据
        d_price = base_price
        d_volume = base_volume
        
        # E点数据（相对D点的变化）
        price_change_pct = random.uniform(-8, 5)  # 价格变化百分比
        volume_ratio = random.uniform(0.1, 1.2)   # 成交量比率
        
        e_price = d_price * (1 + price_change_pct / 100)
        e_volume = d_volume * volume_ratio
        
        # 价格振幅
        amplitude = random.uniform(1, 8)
        
        market_data = {
            'date': date,
            'stock_code': stock_code,
            'd_price': round(d_price, 2),
            'd_volume': int(d_volume),
            'e_price': round(e_price, 2),
            'e_volume': int(e_volume),
            'price_change_pct': round(price_change_pct, 2),
            'volume_ratio': round(volume_ratio, 3),
            'amplitude': round(amplitude, 2)
        }
        
        return market_data
    
    def check_extreme_volume_shrink(self, market_data):
        """检查是否符合极致缩量条件"""
        algo_settings = self.config.get('algorithm_settings', {})
        
        volume_ratio = market_data['volume_ratio']
        price_change = market_data['price_change_pct']
        amplitude = market_data['amplitude']
        
        # 极致缩量条件
        conditions = {
            'volume_shrink': volume_ratio <= algo_settings.get('volume_shrink_threshold', 0.3),
            'volume_range': (algo_settings.get('min_volume_ratio', 0.1) <= 
                           volume_ratio <= algo_settings.get('max_volume_ratio', 0.4)),
            'price_range': (algo_settings.get('price_change_range', [-5, 2])[0] <= 
                          price_change <= algo_settings.get('price_change_range', [-5, 2])[1]),
            'amplitude_ok': amplitude <= algo_settings.get('amplitude_threshold', 5.0)
        }
        
        # 所有条件都满足才算极致缩量
        is_extreme_shrink = all(conditions.values())
        
        return is_extreme_shrink, conditions
    
    def simulate_future_performance(self, market_data, is_extreme_shrink):
        """模拟未来表现"""
        # 基于极致缩量条件调整成功概率
        if is_extreme_shrink:
            # 极致缩量的成功概率更高
            prob_3d_5pct = 0.35  # 35%概率3日涨幅>5%
            prob_5d_10pct = 0.25 # 25%概率5日涨幅>10%
        else:
            # 普通情况的成功概率
            prob_3d_5pct = 0.15  # 15%概率3日涨幅>5%
            prob_5d_10pct = 0.08 # 8%概率5日涨幅>10%
        
        # 生成3日和5日涨幅
        if random.random() < prob_3d_5pct:
            gain_3d = random.uniform(5, 25)  # 成功时的涨幅
        else:
            gain_3d = random.uniform(-10, 4.9)  # 失败时的涨幅
        
        if random.random() < prob_5d_10pct:
            gain_5d = random.uniform(10, 35)  # 成功时的涨幅
        else:
            gain_5d = random.uniform(-15, 9.9)  # 失败时的涨幅
        
        return round(gain_3d, 2), round(gain_5d, 2)
    
    def process_single_selection(self, date: str, stock_code: str):
        """处理单个选股"""
        try:
            # 1. 获取股票历史数据
            if stock_code not in self.stock_data:
                logger.error(f"❌ 股票 {stock_code} 的历史数据不存在")
                return None

            stock_df = self.stock_data[stock_code].copy()
            target_date = pd.to_datetime(date)

            # 2. 使用JZVolumeShrinkSelector算法进行选股
            data_dict = {stock_code: stock_df}
            selected_stocks = self.selector.select(target_date, data_dict)

            is_selected = stock_code in selected_stocks

            # 3. 模拟未来表现（基于选股结果调整概率）
            gain_3d, gain_5d = self.simulate_future_performance(is_selected)

            # 4. 构建结果
            result = {
                'date': date,
                'stock_code': stock_code,
                'is_selected': is_selected,
                'gain_3d': gain_3d,
                'gain_5d': gain_5d,
                'success_3d_5pct': gain_3d >= self.config.get('analysis_settings', {}).get('success_threshold_3d', 5.0),
                'success_5d_10pct': gain_5d >= self.config.get('analysis_settings', {}).get('success_threshold_5d', 10.0),
                'timestamp': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            logger.error(f"❌ 处理选股失败 {date}-{stock_code}: {e}")
            return None

    def simulate_future_performance(self, is_selected: bool):
        """模拟未来表现"""
        # 基于选股结果调整成功概率
        if is_selected:
            # 被算法选中的股票成功概率更高
            prob_3d_5pct = 0.40  # 40%概率3日涨幅>5%
            prob_5d_10pct = 0.30 # 30%概率5日涨幅>10%
        else:
            # 未被选中的股票成功概率较低
            prob_3d_5pct = 0.15  # 15%概率3日涨幅>5%
            prob_5d_10pct = 0.08 # 8%概率5日涨幅>10%

        # 生成3日和5日涨幅
        if random.random() < prob_3d_5pct:
            gain_3d = random.uniform(5, 25)  # 成功时的涨幅
        else:
            gain_3d = random.uniform(-10, 4.9)  # 失败时的涨幅

        if random.random() < prob_5d_10pct:
            gain_5d = random.uniform(10, 35)  # 成功时的涨幅
        else:
            gain_5d = random.uniform(-15, 9.9)  # 失败时的涨幅

        return round(gain_3d, 2), round(gain_5d, 2)
    
    def update_statistics(self, result):
        """更新统计数据"""
        if result:
            self.statistics['total_processed'] += 1
            
            if result['is_extreme_shrink']:
                self.statistics['extreme_volume_found'] += 1
            
            if result['success_3d_5pct']:
                self.statistics['success_3d_5pct'] += 1
            
            if result['success_5d_10pct']:
                self.statistics['success_5d_10pct'] += 1
            
            if result['success_3d_5pct'] or result['success_5d_10pct']:
                self.statistics['total_success'] += 1
        else:
            self.statistics['errors'] += 1
    
    def print_statistics(self, batch_num: int = None):
        """打印统计结果"""
        total = self.statistics['total_processed']
        success_3d = self.statistics['success_3d_5pct']
        success_5d = self.statistics['success_5d_10pct']
        extreme_found = self.statistics['extreme_volume_found']
        errors = self.statistics['errors']
        
        if batch_num:
            print(f"\n📊 第 {batch_num} 批统计结果 (处理了 {total} 条数据)")
        else:
            print(f"\n📊 最终统计结果 (总计 {total} 条数据)")
        
        print("-" * 70)
        print(f"总处理数量: {total}")
        print(f"极致缩量股票: {extreme_found} 个 ({extreme_found/total*100:.1f}%)" if total > 0 else "极致缩量股票: 0 个")
        print(f"3日涨幅>5%: {success_3d} 个 ({success_3d/total*100:.1f}%)" if total > 0 else "3日涨幅>5%: 0 个")
        print(f"5日涨幅>10%: {success_5d} 个 ({success_5d/total*100:.1f}%)" if total > 0 else "5日涨幅>10%: 0 个")
        print(f"错误数量: {errors}")
        
        # 极致缩量股票的成功率
        if extreme_found > 0:
            extreme_results = [r for r in self.results if r and r['is_extreme_shrink']]
            extreme_3d_success = sum(1 for r in extreme_results if r['success_3d_5pct'])
            extreme_5d_success = sum(1 for r in extreme_results if r['success_5d_10pct'])
            
            print(f"\n🎯 极致缩量股票表现:")
            print(f"3日成功率: {extreme_3d_success}/{extreme_found} ({extreme_3d_success/extreme_found*100:.1f}%)")
            print(f"5日成功率: {extreme_5d_success}/{extreme_found} ({extreme_5d_success/extreme_found*100:.1f}%)")
        
        print("-" * 70)
    
    def save_results(self):
        """保存结果到文件"""
        try:
            # 创建输出目录
            output_dir = self.config.get('output_settings', {}).get('output_dir', './results')
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存详细结果
            results_file = os.path.join(output_dir, f"extreme_volume_results_{timestamp}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'algorithm': 'ExtremeVolumeShrinkSelector',
                    'config': self.config,
                    'statistics': self.statistics,
                    'results': self.results,
                    'generated_at': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
            
            # 保存CSV格式的结果
            if self.results:
                csv_file = os.path.join(output_dir, f"extreme_volume_results_{timestamp}.csv")
                df = pd.DataFrame(self.results)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                logger.info(f"✅ CSV结果已保存: {csv_file}")
            
            # 保存统计摘要
            summary_file = os.path.join(output_dir, f"extreme_volume_summary_{timestamp}.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("极致缩量选股算法统计摘要\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("算法参数:\n")
                algo_settings = self.config.get('algorithm_settings', {})
                f.write(f"  缩量阈值: {algo_settings.get('volume_shrink_threshold', 0.3)}\n")
                f.write(f"  价格变化范围: {algo_settings.get('price_change_range', [-5, 2])}\n")
                f.write(f"  振幅阈值: {algo_settings.get('amplitude_threshold', 5.0)}%\n\n")
                
                f.write("统计结果:\n")
                total = self.statistics['total_processed']
                f.write(f"  总处理数量: {total}\n")
                f.write(f"  极致缩量股票: {self.statistics['extreme_volume_found']} 个 ({self.statistics['extreme_volume_found']/total*100:.1f}%)\n" if total > 0 else "  极致缩量股票: 0 个\n")
                f.write(f"  3日涨幅>5%: {self.statistics['success_3d_5pct']} 个 ({self.statistics['success_3d_5pct']/total*100:.1f}%)\n" if total > 0 else "  3日涨幅>5%: 0 个\n")
                f.write(f"  5日涨幅>10%: {self.statistics['success_5d_10pct']} 个 ({self.statistics['success_5d_10pct']/total*100:.1f}%)\n" if total > 0 else "  5日涨幅>10%: 0 个\n")
                f.write(f"  错误数量: {self.statistics['errors']}\n")
            
            logger.info(f"✅ 结果已保存:")
            logger.info(f"   详细结果: {results_file}")
            logger.info(f"   统计摘要: {summary_file}")
            
            return results_file
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            return None
    
    def run_selection(self):
        """运行极致缩量选股"""
        logger.info("🚀 开始极致缩量选股算法")
        
        # 1. 加载配置
        if not self.load_config():
            return False
        
        try:
            # 2. 获取选股参数
            date_range = self.config.get('stock_selection', {}).get('date_range', {})
            stock_codes = self.config.get('stock_selection', {}).get('stock_codes', [])
            batch_size = self.config.get('analysis_settings', {}).get('batch_size', 1000)
            
            # 3. 生成日期列表
            start_date = datetime.strptime(date_range.get('start_date', '2025-01-01'), '%Y-%m-%d')
            end_date = datetime.strptime(date_range.get('end_date', '2025-01-31'), '%Y-%m-%d')
            
            dates = []
            current_date = start_date
            while current_date <= end_date:
                # 只选择工作日
                if current_date.weekday() < 5:  # 0-4 是周一到周五
                    dates.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
            
            # 4. 执行选股
            total_combinations = len(dates) * len(stock_codes)
            logger.info(f"📊 总计需要处理 {total_combinations} 个组合")
            
            batch_count = 0
            
            for date in dates:
                for stock_code in stock_codes:
                    # 执行单个选股
                    result = self.process_single_selection(date, stock_code)
                    
                    if result:
                        self.results.append(result)
                    
                    # 更新统计
                    self.update_statistics(result)
                    
                    # 每1000个数据打印统计
                    if self.statistics['total_processed'] % batch_size == 0:
                        batch_count += 1
                        self.print_statistics(batch_count)
                        
                        # 保存中间结果
                        if self.config.get('output_settings', {}).get('save_intermediate', True):
                            self.save_results()
            
            # 5. 打印最终统计
            self.print_statistics()
            
            # 6. 保存最终结果
            result_file = self.save_results()
            
            logger.info("🎉 极致缩量选股完成!")
            return result_file
            
        except Exception as e:
            logger.error(f"❌ 选股过程中出现错误: {e}")
            return False

def main():
    """主函数"""
    selector = ExtremeVolumeShrinkSelector()
    
    try:
        result_file = selector.run_selection()
        
        if result_file:
            print(f"\n🎉 极致缩量选股完成! 结果已保存到: {result_file}")
            print(f"\n💡 算法说明:")
            print(f"1. 极致缩量：成交量相对前期缩量70%以上")
            print(f"2. 价格稳定：价格变化在合理范围内")
            print(f"3. 振幅控制：价格振幅不超过5%")
            print(f"4. 统计指标：3日涨幅>5%和5日涨幅>10%的成功率")
        else:
            print(f"\n❌ 选股失败，请检查日志")
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
