# 深度学习股票预测系统

## 🎯 系统概述

本系统使用深度学习方法实现股票涨跌预测，与传统的规则方法形成对比。采用前馈神经网络进行二分类任务，预测股票5日内涨超10%的概率。

## 🏗️ 系统架构

```
deeplearning/
├── __init__.py                 # 模块初始化
├── config.json                 # 深度学习配置文件
├── data_preprocessor.py        # 数据预处理模块
├── neural_network.py           # 神经网络模型
├── train_dl_model.py          # 模型训练脚本
├── predict_dl_stocks.py       # 股票预测脚本
├── compare_models.py          # 模型比较脚本
├── models/                    # 保存训练好的模型
├── output/                    # 输出结果
├── logs/                      # TensorBoard日志
└── checkpoints/               # 模型检查点
```

## 🔧 核心功能

### 1. 数据预处理 (`data_preprocessor.py`)
- **特征工程**: 处理16个技术指标特征
- **数据清洗**: 处理缺失值和异常值
- **标准化**: StandardScaler或MinMaxScaler
- **数据分割**: 训练集/测试集分割
- **目标创建**: 二分类标签生成

### 2. 神经网络模型 (`neural_network.py`)
- **架构**: 前馈神经网络
- **隐藏层**: 可配置的多层结构 [128, 64, 32]
- **激活函数**: ReLU + Sigmoid输出
- **优化器**: Adam优化器
- **正则化**: Dropout防止过拟合
- **回调函数**: 早停、学习率调度、模型检查点

### 3. 模型训练 (`train_dl_model.py`)
- **完整训练流程**: 数据预处理 → 模型训练 → 评估 → 保存
- **性能评估**: 准确率、精确率、召回率、F1分数、AUC
- **特征重要性**: 排列重要性分析
- **可视化**: 训练历史、混淆矩阵、特征重要性图

### 4. 股票预测 (`predict_dl_stocks.py`)
- **概率预测**: 输出涨超10%的概率
- **置信度等级**: 7个等级分类
- **投资建议**: 基于概率的投资建议
- **结果排序**: 按概率降序排列
- **准确率分析**: 与实际结果对比

### 5. 模型比较 (`compare_models.py`)
- **重叠度分析**: Top N推荐的重叠率
- **准确率对比**: 整体和Top N准确率
- **分布比较**: 概率/置信度分布对比
- **可视化对比**: 生成对比图表

## 📊 特征说明

系统使用16个技术指标特征：

| 特征类别 | 特征名称 | 说明 |
|---------|---------|------|
| **价格波动** | A-B涨幅, C-D涨幅, D-E涨幅 | 各阶段涨跌幅度 |
| **时间因子** | A-B天数, B-C天数, C-D天数, D-E天数 | 各阶段持续时间 |
| **回调分析** | B-C跌幅 | 回调深度 |
| **成交量** | D点成交量/C-D均量, E点成交量/C-D均量, E点成交量/D点成交量 | 成交量变化 |
| **技术指标** | E点J值, E点J值相对D点J值涨幅 | 超买超卖指标 |
| **上影线** | D点上影线涨幅, D点上影线/实体 | 上影线分析 |
| **价格变化** | E点相对D点收盘价涨幅 | 最新价格变化 |

## 🚀 使用方法

### 1. 环境准备
```bash
pip install tensorflow pandas numpy scikit-learn matplotlib seaborn openpyxl
```

### 2. 配置设置
编辑 `config.json` 文件：
```json
{
  "model": {
    "hidden_layers": [128, 64, 32],
    "dropout_rate": 0.3,
    "learning_rate": 0.001,
    "batch_size": 32,
    "epochs": 100
  },
  "data": {
    "target_threshold": 0.10,
    "train_test_split": 0.8
  }
}
```

### 3. 模型训练
```bash
python deeplearning/train_dl_model.py
```

### 4. 股票预测
```bash
python deeplearning/predict_dl_stocks.py
```

### 5. 模型比较
```bash
python deeplearning/compare_models.py
```

## 📈 输出结果

### 训练输出
- **模型文件**: `models/latest_model.h5`
- **标准化器**: `models/scaler.pkl`
- **训练结果**: `output/training_results_*.json`
- **可视化图表**: `output/training_history_*.png`

### 预测输出
- **预测结果**: `output/dl_prediction_results_*.xlsx`
- **包含列**: 排名、概率、置信度等级、投资建议

### 比较输出
- **比较报告**: `output/model_comparison_report_*.json`
- **对比图表**: `output/model_comparison_*.png`

## 🎯 性能指标

### 模型评估指标
- **准确率**: 整体预测准确性
- **精确率**: 预测为正例中的正确率
- **召回率**: 实际正例中被预测出的比例
- **F1分数**: 精确率和召回率的调和平均
- **AUC分数**: ROC曲线下面积

### 投资效果指标
- **Top3准确率**: 前3只推荐股票的成功率
- **Top5准确率**: 前5只推荐股票的成功率
- **Top10准确率**: 前10只推荐股票的成功率
- **概率校准**: 预测概率与实际成功率的一致性

## 🔍 与传统方法对比

| 方面 | 深度学习方法 | 传统规则方法 |
|------|-------------|-------------|
| **方法** | 神经网络自动学习 | 人工设定规则阈值 |
| **输出** | 连续概率值 | 离散置信度 |
| **特征处理** | 自动特征组合 | 线性特征组合 |
| **泛化能力** | 较强 | 依赖规则质量 |
| **可解释性** | 较弱 | 较强 |
| **训练时间** | 较长 | 较短 |

## ⚙️ 高级配置

### 模型架构调整
```json
{
  "model": {
    "hidden_layers": [256, 128, 64, 32],  // 更深的网络
    "dropout_rate": 0.4,                  // 更强的正则化
    "activation": "relu",                 // 激活函数
    "optimizer": "adam"                   // 优化器选择
  }
}
```

### 训练策略
```json
{
  "model": {
    "early_stopping": {
      "patience": 15,                     // 早停耐心值
      "monitor": "val_loss"               // 监控指标
    },
    "validation_split": 0.2               // 验证集比例
  }
}
```

## 🐛 常见问题

### 1. 内存不足
- 减少批次大小: `"batch_size": 16`
- 减少网络层数: `"hidden_layers": [64, 32]`

### 2. 过拟合
- 增加Dropout: `"dropout_rate": 0.5`
- 减少网络复杂度
- 增加训练数据

### 3. 训练不收敛
- 调整学习率: `"learning_rate": 0.0001`
- 检查数据质量
- 增加训练轮数

## 📝 开发说明

### 扩展新特征
1. 在 `config.json` 中添加特征名
2. 确保数据文件包含该特征
3. 重新训练模型

### 自定义模型架构
1. 修改 `neural_network.py` 中的 `build_model` 方法
2. 调整配置文件中的参数
3. 重新训练和评估

### 添加新的评估指标
1. 在 `neural_network.py` 中扩展 `evaluate` 方法
2. 在 `train_dl_model.py` 中添加相应的保存逻辑

## 🎉 总结

深度学习股票预测系统提供了一个完整的端到端解决方案，从数据预处理到模型训练、预测和比较分析。相比传统规则方法，深度学习方法能够自动学习特征之间的复杂关系，potentially提供更好的预测性能。

系统设计注重模块化和可扩展性，便于后续的功能扩展和性能优化。
